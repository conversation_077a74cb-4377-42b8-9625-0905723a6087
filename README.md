# saas-localserver-cloud


## CloudServer

### Mysql
#### 1. 删除菜单
```sql
update system_menu a, (select max(base_version) bv from system_menu) max 
set a.deleted = 1, a.base_version = max.bv + 1 
where a.name in ('作者动态', 'Boot 开发文档', 'Cloud 开发文档');
```



## LocalServer




## install 
```bash
mvn clean install -DskipTests
```

## deploy （排除 saas-cloudserver）
```bash
mvn clean deploy -P test -DskipTests -pl '!saas-cloudserver'
```


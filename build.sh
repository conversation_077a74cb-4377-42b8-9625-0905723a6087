#
#cd ../local-server-cloud &&
#  mvn clean install -Dmaven.test.skip=true &&
#  mvn deploy -P test -Dmaven.test.skip=true &&
#  cd ../saas-localserver-cloud &&
#  mvn clean install -Dmaven.test.skip=true -pl '!saas-cloudserver' &&
#  mvn clean deploy -P test -Dmaven.test.skip=true -pl '!saas-cloudserver' &&
#  cd ../saas-localserver &&
#  mvn clean install -Dmaven.test.skip=true -pl '!saas-localserver-application' &&
#  mvn clean deploy -P test -Dmaven.test.skip=true -pl '!saas-localserver-application'

#!/bin/bash

# 步骤1: 构建和部署 local-server-cloud
echo "================== STEP 1: Processing local-server-cloud =================="
cd ./local-server-cloud
echo "Current directory: $(pwd)"

echo "-------- Cleaning and installing local-server-cloud (skipping tests) --------"
mvn clean install -Dmaven.test.skip=true
if [ $? -ne 0 ]; then
    echo "❌ Error: Failed to install local-server-cloud"
    exit 1
fi

echo "-------- Deploying local-server-cloud (with test profile) --------"
mvn deploy -P test -Dmaven.test.skip=true
if [ $? -ne 0 ]; then
    echo "❌ Error: Failed to deploy local-server-cloud"
    exit 1
fi
echo "✅ local-server-cloud deployed successfully"

# 步骤2: 构建和部署 saas-localserver-cloud (排除 saas-cloudserver 模块)
echo -e "\n================== STEP 2: Processing saas-localserver-cloud =================="
cd ../saas-localserver-cloud
echo "Current directory: $(pwd)"

echo "-------- Cleaning and installing saas-localserver-cloud (excluding saas-cloudserver) --------"
mvn clean install -Dmaven.test.skip=true -pl '!saas-cloudserver'
if [ $? -ne 0 ]; then
    echo "❌ Error: Failed to install saas-localserver-cloud"
    exit 1
fi

echo "-------- Deploying saas-localserver-cloud (excluding saas-cloudserver) --------"
mvn deploy -P test -Dmaven.test.skip=true -pl '!saas-cloudserver'
if [ $? -ne 0 ]; then
    echo "❌ Error: Failed to deploy saas-localserver-cloud"
    exit 1
fi
echo "✅ saas-localserver-cloud deployed successfully"

# 步骤3: 构建和部署 saas-localserver (排除 saas-localserver-application 模块)
echo -e "\n================== STEP 3: Processing saas-localserver =================="
cd ../saas-localserver
echo "Current directory: $(pwd)"

echo "-------- Cleaning and installing saas-localserver (excluding saas-localserver-application) --------"
mvn clean install -Dmaven.test.skip=true -pl '!saas-localserver-application'
if [ $? -ne 0 ]; then
    echo "❌ Error: Failed to install saas-localserver"
    exit 1
fi

echo "-------- Deploying saas-localserver (excluding saas-localserver-application) --------"
mvn deploy -P test -Dmaven.test.skip=true -pl '!saas-localserver-application'
if [ $? -ne 0 ]; then
    echo "❌ Error: Failed to deploy saas-localserver"
    exit 1
fi
echo "✅ saas-localserver deployed successfully"

echo -e "\n======================================================="
echo "🚀 All deployments completed successfully! 🚀"
echo "======================================================="

package com.xyy.saas.datasync.h2.autoconfigure;

import com.xyy.saas.datasync.h2.support.H2DBFileBuilder;
import com.xyy.saas.datasync.h2.support.H2DBUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.AutoConfigureOrder;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;
import org.springframework.util.ObjectUtils;

/**
 * @Desc sqlite配置
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-29 14:56
 */
//@MapperScan(basePackages = {"club.dlblog.sqlite.mapper"},
//        sqlSessionFactoryRef = "sqlSessionFactory")
@Configuration
// 防止 dataSource 重复
@AutoConfigureBefore(DataSourceAutoConfiguration.class)
// 注解排除 DataSourceAutoConfiguration
@ConditionalOnClass(name = "org.h2.Driver")
//@AutoConfigureBefore(DataSyncClientAutoConfiguration.class)
@EnableConfigurationProperties(LocalDBConfigProperties.class)
public class LocalH2DBAutoConfiguration {

    /**
     * 数据源url
     */
	@Value("${spring.datasource.url:${spring.datasource.dynamic.datasource.master.url:}}")
    private String dataSourceUrl;

    /**
     * 配置sqlite数据源
     *
     * @return
     */
    @Bean
    public DataSource dataSource(LocalDBConfigProperties localDBConfigProperties) {
		String dbPath = localDBConfigProperties.getDbPath();
		if (ObjectUtils.isEmpty(dbPath)) {
			//默认地址
		}

		//TODO 跨账号登录,db路径缺一个机构号路径，以便支持多账号切换登录
		//TODO 水平切分按照年份切分大表
		//尝试创建sqlite文件-不存在时创建
		H2DBUtils.initH2DBFile(H2DBUtils.getFilePath(dataSourceUrl));
		//创建数据源
		//TODO 多连接DataSource，最近1年的数据(最多不超过2年)放一个DataSource,查前年的数据另外一个DataSource
		//TODO 优化一下
		DataSource dataSource = H2DBFileBuilder.create().url(dataSourceUrl).build();
//        try {
//            //尝试初始化数据库-表不存在时创建
//            H2DBUtils.initProDb(dataSource.getConnection());
//        } catch (SQLException e) {
//            e.printStackTrace();
//        }
		return dataSource;
	}
}

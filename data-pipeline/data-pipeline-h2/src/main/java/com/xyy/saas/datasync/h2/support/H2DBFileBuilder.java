package com.xyy.saas.datasync.h2.support;

import org.h2.Driver;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.util.ObjectUtils;

import javax.sql.DataSource;

import static com.xyy.saas.datasync.h2.support.H2DBUtils.JDBC_PREFIX_H2_FILE;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-29 15:29
 */
public class H2DBFileBuilder {

    private String filePath;

    private String url;

    public static H2DBFileBuilder create(){
        return new H2DBFileBuilder();
    }

    public H2DBFileBuilder filePath(String filePath){
        this.filePath = filePath;
        return this;
    }

    public H2DBFileBuilder url(String url) {
        this.url = url;
        return this;
    }

    public DataSource build(){
        if (!ObjectUtils.isEmpty(url)){
            return build(url);
        }
        if(!ObjectUtils.isEmpty(filePath)){
            return build(JDBC_PREFIX_H2_FILE + filePath);
        }
        return DataSourceBuilder.create().build();
    }

	private DataSource build(String url) {
		return DataSourceBuilder.create()
			.url(url)
			.driverClassName(Driver.class.getName())
			.build();
	}
}

package com.xyy.saas.datasync.h2.support;

import com.xyy.saas.datasync.client.entity.DataSyncEntityDispatcher.DataSyncEntityContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ResourceUtils;

import java.io.*;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-29 15:23
 */
@Slf4j
public class H2DBUtils {


	public static final String JDBC_PREFIX_H2_FILE = "jdbc:h2:file:";

	/**
     * 初始化项目db
     * @param connection
     */
    public static void initProDb(Connection connection){
        //判断数据表是否存在
        boolean hasPro = false;
        DataSyncEntityContext.getDataSyncEntityMap();
        try {
            hasPro = true;

            //测试数据表是否存在
            connection.prepareStatement("select * from pro").execute();


        } catch (SQLException e){
            //不存在
            log.debug("table pro is not exist");
            hasPro = false;
        }
        //不存在时创建db
        if(!hasPro) {
            log.debug(">>>start init pro db");
            File file = null;
            try {
                //读取初始化数据sql
                //TODO 数据文件初始化
                file = ResourceUtils.getFile("classpath:db/h2/init.sql");
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            }

            //获取sql
            String sql = "";
            FileInputStream fis = null;
            InputStreamReader isr = null;
            try {
                fis = new FileInputStream(file);
                isr = new InputStreamReader(fis, "UTF-8");
                BufferedReader bf = new BufferedReader(isr);
                String content = "";
                StringBuilder sb = new StringBuilder();
                while (content != null) {
                    content = bf.readLine();
                    if (content == null) {
                        break;
                    }
                    sb.append(content).append(System.lineSeparator());
                }
                sql = sb.toString();
            } catch (FileNotFoundException | UnsupportedEncodingException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                try {
                    isr.close();
                    fis.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            //分割sql
            String[] sqls = sql.split(";");

            try {
                for (String str : sqls) {
                    if (str == null || str.trim().isEmpty()) continue;
                    //开始初始化数据库
                    connection.setAutoCommit(false);
                    connection.prepareStatement(str).execute();
                }
                //提交sql
                connection.commit();
            } catch (SQLException e) {
                e.printStackTrace();
                try {
                    connection.rollback();
                } catch (SQLException e2) {
                    e.printStackTrace();
                }
            } finally {
                try {
                    connection.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            log.debug("finish init pro db>>>");
        }else {
            log.debug("pro db is exist");
        }
    }

    public static void initDb(Connection connection,String... sqls){
        log.debug(">>> start initDb: {}", sqls);
        try {
            for(String str:sqls) {
                connection.setAutoCommit(false);
                connection.prepareStatement(str).execute();
            }
            connection.commit();
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            try {
                connection.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        log.debug("finish initDb>>>");
    }

    public static void initH2DBFile(String filePath){
        File file = new File(filePath);
        File dir = file.getParentFile();
        if(!dir.exists()){
            dir.mkdirs();
        }
        if(!file.exists()){
            try {
                file.createNewFile();
                log.info("create h2 db file is success, filePath:{} ", filePath);
            } catch (IOException e) {
                log.error("create Sqlite DB file is error, filePath:{} ", filePath);
                e.printStackTrace();
            }
        }
    }

    public static String getFilePath(String url){
        url = url.replace(JDBC_PREFIX_H2_FILE, "");
        return url;
    }

}

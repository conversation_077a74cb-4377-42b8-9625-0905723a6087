package com.xyy.saas.datasync.h2.entity;

import com.xyy.saas.datasync.client.constants.DataSyncDirection;
import com.xyy.saas.datasync.client.entity.AutoIncrement;
import com.xyy.saas.datasync.client.entity.DataSyncEntity;
import com.xyy.saas.datasync.client.entity.DefaultValue;
import com.xyy.saas.datasync.client.entity.PrimaryKey;
import jakarta.validation.constraints.NotNull;
import org.hibernate.validator.constraints.Length;

/**
 * @Desc 数据同步实体
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-08-03 21:48
 */
@DataSyncEntity(direction = DataSyncDirection.CLOUD_TO_LOCAL, ack = true)
public class DatasyncEntityTest {

    /** id*/
    @PrimaryKey
    @AutoIncrement
    private Integer id;

    @NotNull
    private String a1;

    @DefaultValue(value = "abc")
    private String b1;

    @Length(max = 100)
    private boolean a2;

    @Length(max = 100)
    private String add1;

    @Length(max = 100)
    private String add2;

    @Length(max = 100)
    private String add11;

    @Length(max = 100)
    private String add22;

    @Length(max = 100)
    private String add33;
    @Length(max = 100)
    private String x;
    @Length(max = 100)
    private String y1;
    private String y2;
    private String y3;

    @DefaultValue(value = "aaa")
    private String y4;

    @DefaultValue(value = "aaa")
    private String y5;

    private String y6;

    private String y7;


}


package com.xyy.saas.datasync.h2.test.service;

import com.xyy.saas.datasync.h2.test.mapper.RegisterRecordMapper;
import com.xyy.saas.datasync.h2.test.mapper.po.RegisterRecord;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;


/**
 * <AUTHOR>
 * <p>
 */
@Service
@Transactional(rollbackFor = Throwable.class)
@RequiredArgsConstructor
public class RegisterRecordService {
    final RegisterRecordMapper registerRecordMapper;

    public void dataSyncTest(boolean exception) {
        // sqlite db 测试
        int len = 10;
        List<RegisterRecord> list = new ArrayList<>(len);
        for (int i = 0; i < len; i++) {
            list.add(assemble());
        }

        // 插入
        registerRecordMapper.insert(list.get(0));
        // 批量插入
        registerRecordMapper.insertBatch(list.subList(1, len));

        List<Integer> ids = new ArrayList<>(len);
        for (RegisterRecord registerRecord : list) {
            ids.add(registerRecord.getId());
        }
        // 查询
        registerRecordMapper.getById(ids.get(0));
        // 批量查询
        registerRecordMapper.listByIds(ids);

        // 根据id更新
        RegisterRecord recordUpd2 = new RegisterRecord();
        recordUpd2.setId(ids.get(1));
        recordUpd2.setEmail("通过id修改邮箱@qq.com");
        registerRecordMapper.updateEmailById(recordUpd2);

        // 根据id批量更新
        registerRecordMapper.updateEmailByIds(Arrays.asList(list.get(1).getId(), list.get(2).getId()), "通过ids批量修改邮箱@qq.com");

        // 根据name更新
        RegisterRecord recordUpd = new RegisterRecord();
        recordUpd.setName(list.get(0).getName());
        recordUpd.setEmail("通过name修改邮箱@qq.com");
        registerRecordMapper.updateEmailByName(recordUpd);

        if (exception) {
            throw new RuntimeException("[数据同步] 异常测试回滚事务");
        }
    }

    private RegisterRecord assemble() {
        RegisterRecord recordUpd = new RegisterRecord();
        String now = LocalDateTime.now().toString();
        recordUpd.setName(now);
        recordUpd.setAge(new Random().nextInt(100) + 1);
        recordUpd.setEmail(now + "@qq.com");
        return recordUpd;
    }
}

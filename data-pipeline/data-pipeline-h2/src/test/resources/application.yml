server:
  port: 8080
  tomcat:
    uri-encoding: UTF-8
  servlet:
    encoding:
      charset: UTF-8
      force: true
      enabled: true
localserver:
  db-path: ./db/h2/ls.db
spring:
  profiles:
    active: test
  datasource:
#    type: com.zaxxer.hikari.HikariDataSource
#    url: *******************************************************************************************************************************************************************************************************************************************************
#    username: root
#    password: zhangpeng123
    url: jdbc:h2:file:${localserver.db-path}\h2\localserver.db
    driver-class-name: org.h2.Driver
  application:
    name: data-pipeline-h2
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: NON_NULL #序列化全部不为空的属性
  servlet:
    multipart:
      enabled: true
      file-size-threshold: 0
      #单个数据的大小
      max-file-size: 10000000
  mvc:
    servlet:
      load-on-startup: 1
#    date-format: yyyy-MM-dd HH:mm:ss  #MVC的时间配置  需要3处配置  jackson时间和时区
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
#    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
  mapper-locations: classpath*:/mapper/**/*.xml
  #typeAliasesPackage: com.service.*.entity;
logging:
  level:
    com.xyy.saas.datasync.client.jdbc: debug


<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>data-pipeline</artifactId>
		<groupId>com.xyy.saas</groupId>
		<version>${revision}</version>
	</parent>

	<modelVersion>4.0.0</modelVersion>

	<artifactId>data-pipeline-plugin</artifactId>
	<version>${revision}</version>

	<dependencies>
		<!-- Dubbo -->
		<dependency>
			<artifactId>dubbo-spring-boot-starter</artifactId>
			<groupId>org.apache.dubbo</groupId>
			<version>${dubbo.version}</version>
		</dependency>

		<dependency>
			<artifactId>spring-boot-starter</artifactId>
			<groupId>org.springframework.boot</groupId>
			<version>${springboot.version}</version>
		</dependency>
		<dependency>
			<artifactId>spring-boot-autoconfigure</artifactId>
			<groupId>org.springframework.boot</groupId>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<groupId>org.springframework.boot</groupId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<artifactId>db-starter</artifactId>
			<groupId>com.xyy.saas</groupId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<artifactId>utils-starter</artifactId>
			<groupId>com.xyy.saas</groupId>
		</dependency>
		<dependency>
			<artifactId>data-pipeline-starter-client</artifactId>
			<groupId>com.xyy.saas</groupId>
			<version>${project.version}</version>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>21</source>
					<target>21</target>
					<parameters>true</parameters>
				</configuration>
			</plugin>
		</plugins>
	</build>


</project>

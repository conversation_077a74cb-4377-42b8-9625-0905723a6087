package com.xyy.saas.datasync.plugin;

import com.xyy.saas.datasync.client.db.DataTableDmlTransformer;
import com.xyy.saas.datasync.client.worker.DataSyncLookup;
import org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * @Desc 数据同步插件配置
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/04/14 11:50
 */
//@EnableDubbo
@Configuration
public class DataPipelinePluginConfiguration {

	@Bean
	public DataSyncMetadataUpdater getDataSyncMetadataUpdater(Environment environment) {
		return new DataSyncMetadataUpdater(environment);
	}

	@Bean
	@ConditionalOnMissingBean(ReferenceAnnotationBeanPostProcessor.class)
	public DataSyncLookup dataSyncLookup(Environment environment, JdbcTemplate jdbcTemplate, ObjectProvider<DataTableDmlTransformer> dataTableDmlTransformerProvider) {
		return new DataSyncLookupImpl(environment, jdbcTemplate, dataTableDmlTransformerProvider);
	}

}

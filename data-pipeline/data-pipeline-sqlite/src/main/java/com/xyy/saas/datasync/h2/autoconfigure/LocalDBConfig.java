package com.xyy.saas.datasync.h2.autoconfigure;

import com.xyy.saas.datasync.h2.support.SqliteFileBuilder;
import com.xyy.saas.datasync.h2.support.SqliteUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfigureOrder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;
import org.springframework.util.ObjectUtils;

/**
 * @Desc sqlite配置
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-29 14:56
 */
//@MapperScan(basePackages = {"club.dlblog.sqlite.mapper"},
//        sqlSessionFactoryRef = "sqlSessionFactory")
@Configuration
@AutoConfigureOrder(100)
//@AutoConfigureBefore(DataSyncClientAutoConfiguration.class)
@EnableConfigurationProperties(LocalDBConfigProperties.class)
public class LocalDBConfig {

    /**
     * 数据源url
     */
	@Value("${spring.datasource.url:${spring.datasource.dynamic.datasource.master.url}}")
    private String dataSourceUrl;

    /**
     * 配置sqlite数据源
     *
     * @return
     */
    @Bean
    public DataSource dataSource(LocalDBConfigProperties localDBConfigProperties) {
		String dbPath = localDBConfigProperties.getDbPath();
		if (ObjectUtils.isEmpty(dbPath)) {
			//默认地址
		}

		//TODO 跨账号登录,db路径缺一个机构号路径，以便支持多账号切换登录
		//TODO 水平切分按照年份切分大表
		//尝试创建sqlite文件-不存在时创建
		SqliteUtils.initSqliteFile(SqliteUtils.getFilePath(dataSourceUrl));
		//创建数据源
		//TODO 多连接DataSource，最近1年的数据(最多不超过2年)放一个DataSource,查前年的数据另外一个DataSource
		//TODO 优化一下
		DataSource dataSource = SqliteFileBuilder.create().url(dataSourceUrl).build();
//        try {
//            //尝试初始化数据库-表不存在时创建
//            SqliteUtils.initProDb(dataSource.getConnection());
//        } catch (SQLException e) {
//            e.printStackTrace();
//        }
		return dataSource;
	}
}

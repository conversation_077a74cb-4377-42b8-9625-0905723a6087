package com.xyy.saas.datasync.h2;

import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.annotation.AliasFor;
import org.springframework.test.context.ActiveProfiles;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Desc 小药药SaaS测试用例
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-08-03 17:36
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@SpringBootTest(classes = DatasyncClientTestApplication.class)
@ActiveProfiles
public @interface XyySaaSLocalServerTest {

    String[] value() default {};

    @AliasFor(
            annotation = ActiveProfiles.class
    )
    String[] profiles() default {"test"};

    @AliasFor(
            annotation = SpringBootTest.class
    )
    SpringBootTest.WebEnvironment webEnvironment() default SpringBootTest.WebEnvironment.RANDOM_PORT;

}

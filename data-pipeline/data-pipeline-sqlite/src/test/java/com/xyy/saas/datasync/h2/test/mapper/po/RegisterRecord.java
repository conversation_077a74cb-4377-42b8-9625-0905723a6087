package com.xyy.saas.datasync.h2.test.mapper.po;

import com.xyy.saas.datasync.client.constants.DataSyncDirection;
import com.xyy.saas.datasync.client.entity.AutoIncrement;
import com.xyy.saas.datasync.client.entity.DataSyncEntity;
import com.xyy.saas.datasync.client.entity.PrimaryKey;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@DataSyncEntity(direction = DataSyncDirection.LOCAL_TO_CLOUD)
public class RegisterRecord {

    @PrimaryKey
    @AutoIncrement
    private int id;

    private String name;
    private Integer age;
    private String email;


    private Date createTime;
    private Date updateTime;
}

package com.xyy.saas.datasync.client.autoconfigure;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.xyy.saas.datasync.client.EnableDataSyncScan;
import com.xyy.saas.datasync.client.constants.DataContextHolder;
import com.xyy.saas.datasync.client.constants.DataSyncUtil;
import com.xyy.saas.datasync.client.db.DataSyncPersistencer;
import com.xyy.saas.datasync.client.db.DataSyncTableInitializer;
import com.xyy.saas.datasync.client.db.DataTableDmlTransformer;
import com.xyy.saas.datasync.client.db.database.MysqlDataTableDmlTransformer;
import com.xyy.saas.datasync.client.db.database.SqliteDataTableDmlTransformer;
import com.xyy.saas.datasync.client.db.table.DataSyncPushDao;
import com.xyy.saas.datasync.client.jdbc.DataSyncInterceptor;
import com.xyy.saas.datasync.client.protocol.*;
import com.xyy.saas.datasync.client.protocol.mqtt.DelayMQTTSubscriberImpl;
import com.xyy.saas.datasync.client.protocol.mqtt.MQTTSubscriber;
import com.xyy.saas.datasync.client.worker.*;
import com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor;
import com.xyy.saas.datasync.client.worker.pull.PullSubscriberExecutor;
import com.xyy.saas.datasync.client.worker.pull.SingleThreadPullSubscriberExecutor;
import com.xyy.saas.datasync.client.worker.push.CyclePublisherExecutor;
import com.xyy.saas.datasync.client.worker.push.DelayedRemovalDuplicatePublisherExecutor;
import com.xyy.saas.datasync.client.worker.push.PublisherExecutor;
import com.xyy.saas.datasync.client.worker.push.SingleThreadPublisherExecutor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

import org.springframework.http.HttpStatus;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;
import reactor.core.publisher.Mono;

import java.time.Duration;

/**
 * @Desc 数据同步客户端自动配置
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-21 17:46
 */
@Slf4j
@Configuration
//@AutoConfigureOrder(99)
@EnableConfigurationProperties(DataSyncClientProperties.class)
public class DataSyncClientAutoConfiguration {

	@Bean
	@ConditionalOnMissingBean(DataSyncMonitor.class)
	public DataSyncMonitor dataSyncMonitor() {
		return new DefaultDataSyncMonitor();
	}

	/**
	 * 数据同步客户端,数据库dml语句转换器
	 *
	 */
	@Bean
	@ConditionalOnMissingBean
	@ConditionalOnClass(name = "org.sqlite.JDBC")
	public DataTableDmlTransformer dataTableDmlTransformer(JdbcTemplate jdbcTemplate) {
		return new SqliteDataTableDmlTransformer(jdbcTemplate);
	}

	@Bean
	@ConditionalOnMissingBean
	@ConditionalOnClass(name = "com.mysql.cj.jdbc.Driver")
	public DataTableDmlTransformer mysqlDataTableDmlTransformer(JdbcTemplate jdbcTemplate) {
		return new MysqlDataTableDmlTransformer(jdbcTemplate);
	}

	/**
	 * 数据同步客户端,数据同步表初始化器，初始化表结构或者修改表结构,只在客户端是sqlite的时候生效
	 *
	 */
	@Bean
	@ConditionalOnMissingBean
	@ConditionalOnSyncType(EnableDataSyncScan.SyncType.client)
//	@ConditionalOnClass(name = "org.sqlite.JDBC")
//	@ConditionalOnBean(value = {DataTableDmlTransformer.class})
	public DataSyncTableInitializer dataSyncTableInitializer(
		ObjectProvider<DataTableDmlTransformer> dmlProvider,
		ObjectProvider<DataSource> dataSourceProvider,
		ObjectProvider<DataSyncClient>  dataSyncClientProvider) {
		return new DataSyncTableInitializer(dmlProvider, dataSourceProvider, dataSyncClientProvider);
	}


	/**
	 * 数据同步客户端工作者,主要是负责数据的上传或者推送
	 *
	 * @param dataSyncClientProperties 数据同步配置
	 */
	@Bean
	@ConditionalOnSyncType(EnableDataSyncScan.SyncType.client)
	public DataSyncClientWorker getDataSyncClientWorker(
		DataSyncClientProperties dataSyncClientProperties,
		PullSubscriberExecutor pullSubscriberExecutor,
		PublisherExecutor publisherExecutor) {
		return new DataSyncClientWorker(dataSyncClientProperties, pullSubscriberExecutor, publisherExecutor);
	}

	/**
	 * 订阅者执行器(异步线程),主要是负责数据的订阅，处理并发和编码过程
	 */
	@Bean
	@ConditionalOnSyncType(EnableDataSyncScan.SyncType.client)
	public PullSubscriberExecutor pullSubscriberExecutor(DataSyncClientProperties dataSyncClientProperties, DataSubscriber dataSubscriber,
														 DataSyncPersistencer dataSyncPersistencer) {
		return new SingleThreadPullSubscriberExecutor(
			new CycleLoopPullSubscriberExecutor(dataSyncClientProperties, dataSubscriber, dataSyncPersistencer));
	}


	/**
	 * 数据订阅器，拉取云端数据使用,处理协议
	 *
	 */
	@Bean
	@ConditionalOnSyncType(EnableDataSyncScan.SyncType.client)
	public DataSubscriber getDataSubscriber(DataSyncClient dataSyncClient) {
		return new DataSubscriberImpl(dataSyncClient);
	}

	/**
	 * 数据同步持久化
	 */
	@Bean
	@ConditionalOnSyncType(EnableDataSyncScan.SyncType.client)
	public DataSyncPersistencer getDataSyncPersistencer(SqlSessionTemplate sqlSessionTemplate, SqlSessionFactory sqlSessionFactory, JdbcTemplate jdbcTemplate) {
		return new DataSyncPersistencer(sqlSessionTemplate, sqlSessionFactory, jdbcTemplate);
	}

	@Bean
	@ConditionalOnSyncType(EnableDataSyncScan.SyncType.client)
	public PublisherExecutor publisherExecutor(DataSyncClientProperties dataSyncClientProperties, DataPublisher dataPublisher, DataSyncPushDao dataSyncDao) {
		return new DelayedRemovalDuplicatePublisherExecutor(new SingleThreadPublisherExecutor(new CyclePublisherExecutor(dataSyncClientProperties, dataPublisher, dataSyncDao)));
	}

	@Bean
	@ConditionalOnSyncType(EnableDataSyncScan.SyncType.client)
	public DataPublisher getDataPublisher(DataSyncClient dataSyncClient) {
		return new DataPublisherImpl(dataSyncClient);
	}


	@Bean
	@ConditionalOnSyncType(EnableDataSyncScan.SyncType.client)
	public DataSyncPushDao dataSyncDao(JdbcTemplate jdbcTemplate) {
		return new DataSyncPushDao(jdbcTemplate);
	}

	/**
	 * 数据同步数据变更拦截器,主要是拦截mybatis plus的sql执行器，并进行数据同步
	 * Server端 监听变更后，将变更数据以MQTT发送给客户端，客户端再进行数据同步Pull;
	 * Client端 监听变更后，将变更数据保存到本地数据同步表，并且Push到云端;
	 */
	@Bean
	public DataSyncInterceptor dataSyncInterceptor(DataTableDmlTransformer dataTableDmlTransformer,
												   ObjectProvider<DataSyncPushDao> dataSyncDaoProvider,
												   ObjectProvider<PublisherExecutor> publisherExecutorProvider) {
		DataSyncInterceptor dataSyncInterceptor = new DataSyncInterceptor(dataTableDmlTransformer, dataSyncDaoProvider, publisherExecutorProvider);
//		mybatisPlusInterceptor.addInnerInterceptor(dataSyncInterceptor);
		return dataSyncInterceptor;
	}

	/*
	 * Pull及时性
	 */
	@Bean
	@ConditionalOnSyncType(EnableDataSyncScan.SyncType.client)
	public MQTTSubscriber getDelayMQTTSubscriber(DataSyncClient dataSyncClient, PullSubscriberExecutor pullSubscriberExecutor) {
		return new DelayMQTTSubscriberImpl(dataSyncClient, pullSubscriberExecutor);
	}


	@Bean
	@ConditionalOnSyncType(EnableDataSyncScan.SyncType.client)
//	@ConditionalOnBean(value = {DataSyncTableInitializer.class})
	public WebClient webClient(DataSyncClientProperties dataSyncClientProperties) {
		// 设置更大的缓冲区大小
		ExchangeStrategies strategies = ExchangeStrategies.builder()
			// 例如，设置为1MB
			.codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(32 * 1024 * 1024))
			.build();
		//设置超时时间
		Duration timeout = Duration.ofMillis(dataSyncClientProperties.getTimeout());

		//当强求超时的时候，提示超时信息，帮我写出代码。设置定义超时返回的ExchangeFilterFunction
		ExchangeFilterFunction responseTimeoutFilterFunction = ExchangeFilterFunction.ofResponseProcessor(clientResponse -> {
			if (clientResponse.statusCode().is4xxClientError() || clientResponse.statusCode().is5xxServerError()) {
				log.error("请求失败，状态码：{}，错误信息：{}", clientResponse.statusCode(), clientResponse.bodyToMono(String.class).block());
				return Mono.error(new RuntimeException("请求超时，请检查网络连接或服务端状态"));
			}
			if (clientResponse.statusCode().is2xxSuccessful()) {
				return Mono.just(clientResponse);
			}
			if (clientResponse.statusCode().is3xxRedirection()) {
				log.error("请求失败，状态码：{}，错误信息：{}", clientResponse.statusCode(), clientResponse.bodyToMono(String.class).block());
				return Mono.error(new RuntimeException("请求超时，请检查网络连接或服务端状态"));
			}
			// 添加超时处理逻辑
//			if (clientResponse.statusCode() == HttpStatus.REQUEST_TIMEOUT) {
//				return Mono.just(
//					ClientResponse.builder()
//					.status(HttpStatus.SERVICE_UNAVAILABLE) // 或者使用其他合适的HTTP状态码
//					.body(BodyInserters.fromObject(new ErrorResponse("请求超时，请检查网络连接或服务端状态")))
//					.build());
//			}
			return Mono.just(clientResponse);
		});

		WebClient build = WebClient.builder()
			.baseUrl(dataSyncClientProperties.getUrl())
			.exchangeStrategies(strategies)
			.filter(responseTimeoutFilterFunction)
			.filter(ExchangeFilterFunction.ofRequestProcessor(request -> {
				// 请求添加header: 租户ID
				ClientRequest clientRequest = ClientRequest.from(request)
					.header(DataSyncUtil.HEADER_TENANT_ID, DataContextHolder.getTenantId())
					.build();
				// 在这里设置请求级别的超时
				return Mono.just(clientRequest);
			}).andThen(ExchangeFilterFunction.ofResponseProcessor(response -> {
				// 这里处理超时的响应
				if (response.statusCode() == HttpStatus.REQUEST_TIMEOUT) {
					return Mono.error(new RuntimeException("请求超时，请检查网络连接或服务端状态"));
				}
				//处理连接异常
				if (response.statusCode().is4xxClientError() || response.statusCode().is5xxServerError()) {
					return Mono.error(new RuntimeException("请求失败，状态码：" + response.statusCode() + "，错误信息：" + response.bodyToMono(String.class).block()));
				}

				return Mono.just(response);
			})))
			.build();

		//当强求超时的时候，提示超时信息，自定义超时返回的信息给客户端
		return build;
	}


	@Bean
	@SneakyThrows
	@ConditionalOnSyncType(EnableDataSyncScan.SyncType.client)
//	@ConditionalOnBean(value = {DataSyncTableInitializer.class})
	public DataSyncClient dataSyncClient(WebClient webClient, DataSyncClientProperties dataSyncClientProperties) {
		WebClientAdapter webClientAdapter = WebClientAdapter.create(webClient);

		//设置超时时间
		Duration timeout = Duration.ofMillis(dataSyncClientProperties.getTimeout());
		webClientAdapter.setBlockTimeout(timeout);

		HttpServiceProxyFactory httpServiceProxyFactory = HttpServiceProxyFactory
			.builderFor(webClientAdapter)
			.build();
		return httpServiceProxyFactory.createClient(DataSyncClient.class);
	}
}

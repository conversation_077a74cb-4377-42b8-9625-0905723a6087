package com.xyy.saas.datasync.client.constants;

import cn.hutool.extra.spring.SpringUtil;
import com.xyy.saas.datasync.client.entity.DataSyncEntityDefinition;

import java.util.Map;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/5/11 下午2:38
 */
public class FieldSupport {

//	/**
//	 * @param tableName
//	 */
//	public static void checkAndInitBaseVersion(String tableName) {
//
//	}

	public static void buildBaseVersion(DataSyncEntityDefinition dataSyncEntityDefinition) {
//		SyncStrategy strategy = SpringUtil.getBean(DataSyncClientProperties.class).getStrategy();
		String strategy = SpringUtil.getProperty("datasync.strategy");
		if (SyncStrategy.event.name().equals(strategy)) {
			return;
		}
		//检查是否存在baseVersion字段
		Map<String, DataSyncEntityDefinition.FieldDefinition> fieldDefinitionMap = dataSyncEntityDefinition.getFieldDefinitionMap();
		DataSyncEntityDefinition.FieldDefinition fieldDefinition = fieldDefinitionMap.get(DataSyncUtil.BASE_VERSION);
		if (fieldDefinition != null) {
			return;    //存在则不再添加
		}
		fieldDefinition = DataSyncEntityDefinition.FieldDefinition.builder()
			.fieldName("baseVersion")
			.tableFieldName(DataSyncUtil.BASE_VERSION)
			.fieldType(Long.class)
			.notNull(true)
			.defaultValue("0")
//			.autoIncrement(true)
//			.primaryKey(false)
			.build();
		dataSyncEntityDefinition.getFields().add(fieldDefinition);
		dataSyncEntityDefinition.getFieldDefinitionMap().put(DataSyncUtil.BASE_VERSION, fieldDefinition);
	}

	public static void buildTenantId(DataSyncEntityDefinition dataSyncEntityDefinition) {
		Map<String, DataSyncEntityDefinition.FieldDefinition> fieldDefinitionMap = dataSyncEntityDefinition.getFieldDefinitionMap();
		DataSyncEntityDefinition.FieldDefinition fieldDefinition = fieldDefinitionMap.get(DataSyncUtil.TENANT_ID);
		//检查是否存在tenantId字段
		if (fieldDefinition != null) {
			return;    //存在则不再添加
		}
		//dataSyncEntityDefinition.getTableName()不存在在TenantIdTable枚举中的name
		if (TenantIdTable.isTenantIdTable(dataSyncEntityDefinition.getTableName())) {
			return;
		}
		//TODO 需要添加tenantId字段的处理逻辑
		fieldDefinition = DataSyncEntityDefinition.FieldDefinition.builder()
			.fieldName("tenantId")
			.tableFieldName(DataSyncUtil.TENANT_ID)
			.fieldType(Long.class)
			.notNull(true)
			.defaultValue("0")
//			.autoIncrement(true)
//			.primaryKey(false)
			.build();
		dataSyncEntityDefinition.getFields().add(fieldDefinition);
		dataSyncEntityDefinition.getFieldDefinitionMap().put(DataSyncUtil.TENANT_ID, fieldDefinition);
	}

	enum TenantIdTable {
		system_error_code,
		;

		public static boolean isTenantIdTable(String tableName) {
			for (TenantIdTable tenantIdTable : TenantIdTable.values()) {
				if (tenantIdTable.name().equals(tableName)) {
					return true;
				}
			}
			return false;
		}
	}
}

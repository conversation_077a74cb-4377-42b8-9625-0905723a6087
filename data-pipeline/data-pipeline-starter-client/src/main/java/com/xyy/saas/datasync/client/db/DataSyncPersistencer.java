package com.xyy.saas.datasync.client.db;

import com.baomidou.mybatisplus.core.batch.MybatisBatch;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.MybatisBatchUtils;
import com.xyy.saas.datasync.client.protocol.vo.DataPullVO;
import com.xyy.saas.localserver.utils.DbUtils;
import lombok.extern.java.Log;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.BatchResult;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.jdbc.UncategorizedSQLException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.dao.DuplicateKeyException;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-28 16:12
 */
@Slf4j
public class DataSyncPersistencer {

	private final SqlSessionTemplate sqlSessionTemplate;

	private final SqlSessionFactory sqlSessionFactory;

	private final JdbcTemplate jdbcTemplate;

	private final DataTableDmlTransformer dataTableDmlTransformer;

	public DataSyncPersistencer(SqlSessionTemplate sqlSessionTemplate, SqlSessionFactory sqlSessionFactory, JdbcTemplate jdbcTemplate, DataTableDmlTransformer dataTableDmlTransformer) {
		this.sqlSessionTemplate = sqlSessionTemplate;
		this.sqlSessionFactory = sqlSessionFactory;
		this.jdbcTemplate = jdbcTemplate;
		this.dataTableDmlTransformer = dataTableDmlTransformer;
	}


	/**
	 * 拉取数据持久化
	 *
	 * @param pullList    保存的数据
	 * @param entityClazz 保存的实体类
	 *                    保存数据到数据库
	 * @param tableName   保存的表名
	 * @return 保存成功的id列表
	 */
	@Transactional(rollbackFor = Exception.class)
	public <T> List<Long> pullToPersistence(String tableName, List<Map<String, Object>> pullList, Class<T> entityClazz) {
		if (pullList.isEmpty()) {
			return Collections.emptyList();
		}
		//不用这里的写入方法是因为MybatisPlus的批量插入方法不支持，少赋值BaseVersion字段，导致(base_version)插入失败
//		Class<?> mapperClass = null;
//		try {
//			mapperClass = Class.forName(generateMapperClassName(entityClazz));
//		} catch (ClassNotFoundException e) {
//			throw new RuntimeException(e);
//		}
		// 获取实体类对应的 Mapper,这里变成了代理类下面在执行MybatisBatchUtils.execute会报找不到Ms
//		BaseMapper mapper = (BaseMapper) sqlSessionTemplate.getMapper(mapperClass);
//		MybatisBatch.Method<T> mapperMethod = new MybatisBatch.Method<>(mapperClass);
//		try {
//			List<BatchResult> result = MybatisBatchUtils.execute(sqlSessionFactory, pullList, mapperMethod.insert());
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
		List<Long> duplicateKeyIds = new ArrayList<>(); // 用于收集插入重复的ID
		List<Long> failIds = new ArrayList<>(); // 用于收集插入失败的ID
		List<Long> successIds = batchInsertToTable(pullList, tableName, duplicateKeyIds, failIds);
		//打印一下，成功的ids,key重复的ids,失败的ids,
		if (!CollectionUtils.isEmpty(duplicateKeyIds) || !CollectionUtils.isEmpty(failIds)) {
			log.error("插入数据库，表名：{},successIds:{},duplicateKeyIds:{},failIds:{}", tableName, successIds, duplicateKeyIds, failIds);
		} else {
			log.info("插入数据库，表名：{},successIds:{},duplicateKeyIds:{},failIds:{}", tableName, successIds, duplicateKeyIds, failIds);
		}
		return successIds;

	}

	public List<Long> batchInsertToTable(List<Map<String, Object>> dataList, String tableName, List<Long> duplicateKeyIds, List<Long> failIds) {
		List<Long> ids = dataList.stream().map(map -> Long.valueOf(DbUtils.getIdValueFromMap(map).toString())).collect(Collectors.toList());

		//  dataList 根据 tableName.columns 删除多余列数据
		Set<String> columnSet = jdbcTemplate.queryForList(dataTableDmlTransformer.getDetectionTable(tableName))
			.stream().map(i -> i.get("FIELD").toString().toLowerCase()).collect(Collectors.toSet());
		//列名
		List<String> removeColumns = dataList.getFirst().keySet().stream().filter(i -> !columnSet.contains(i)).toList();
		List<String> columns = dataList.getFirst().keySet().stream().filter(columnSet::contains).toList();
		String sql = MessageFormat.format( "INSERT INTO {0} ({1}) VALUES ({2})",
										   DbUtils.addBackquote(tableName),
										   columns.stream().map(DbUtils::addBackquote).collect(Collectors.joining(", ")),
										   columns.stream().map(k -> "?").collect(Collectors.joining(", ")));

		// 去除多余列
		dataList.forEach(data -> removeColumns.forEach(data::remove));

		//这里批量插入的时候，如果有重复的id，会抛出DuplicateKeyException，这里捕获异常，然后逐条插入
		try {
			List<Object[]> batches = dataList.stream().map(map -> map.values().toArray()).collect(Collectors.toList());
			jdbcTemplate.batchUpdate(sql, batches);
			return ids;
		} catch (Exception e) {    //如果有异常，说明有重复的id，则逐条插入
			log.error("插入数据库，表名：{},异常信息:{}", tableName, e.getMessage(), e);
			if (e.getCause().getMessage().contains("has no column named tenant_id")) {
				//如果是因为没有tenant_id字段导致的，则新增tenant_id字段，再次尝试插入
				jdbcTemplate.execute("ALTER TABLE " + tableName + " ADD tenant_id BIGINT DEFAULT 0 NOT NULL");
				log.error("插入的时候没有tenant_id字段,tableName:{}, 新增tenant_id字段成功，再次尝试插入", tableName);
			}
			return oneRowInsertData(dataList, tableName, duplicateKeyIds, failIds, sql, ids);
		}
	}

	/**
	 * 单条插入数据,如果有重复的id，则直接覆盖数据库的值，并记录失败的id
	 *
	 * @param dataList
	 * @param tableName
	 * @param duplicateKeyIds
	 * @param failIds
	 * @param sql
	 * @param ids
	 * @return
	 */
	private List<Long> oneRowInsertData(List<Map<String, Object>> dataList, String tableName,
										List<Long> duplicateKeyIds, List<Long> failIds, String sql, List<Long> ids) {
		//插入成功的id列表
		List<Long> successIds = new ArrayList<>();
		for (Map<String, Object> data : dataList) {
			Long id = Long.valueOf(DbUtils.getIdValueFromMap(data).toString());
			try {
				jdbcTemplate.update(sql, data.values().toArray());
				successIds.add(id);
				//catch DuplicateKeyException 和 UncategorizedSQLException
			} catch (DuplicateKeyException e1) {
				//如果是重复的id，则直接覆盖数据库的值
				onDuplicateKeyUpdate(tableName, data);
				successIds.add(id);
				duplicateKeyIds.add(id);
			} catch (UncategorizedSQLException e2) {
				//org.springframework.jdbc.UncategorizedSQLException: PreparedStatementCallback; uncategorized SQLException for SQL [INSERT INTO system_mail_account (id, mail, username, password, host, port, ssl_enable, creator, create_time, updater, update_time, deleted, base_version) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]; SQL state [null]; error code [19]; [SQLITE_CONSTRAINT_PRIMARYKEY] A PRIMARY KEY constraint failed (UNIQUE constraint failed: system_mail_account.id)
				//如果是因为主键冲突导致的，则直接覆盖数据库的值
				onDuplicateKeyUpdate(tableName, data);
				successIds.add(id);
				duplicateKeyIds.add(id);
			} catch (Exception e0) {
				log.error("插入数据库，表名：{},异常信息:{}", tableName, e0.getMessage(), e0);
				//TODO 数据同步失败,报警通知等操作
				failIds.add(id);
			}
		}
		return successIds;
	}

	private void onDuplicateKeyUpdate(String tableName, Map<String, Object> data) {
		Object id = data.remove("id");
		Collection<Object> params = data.values();
		params.add(id);
		jdbcTemplate.update("UPDATE " + tableName + " SET " + data.entrySet().stream()
			.map(entry -> entry.getKey() + " =? ")
			.collect(Collectors.joining(", ")) + " WHERE id =?", params.toArray());
	}

	/**
	 * 根据entityClass获取mapper的名字
	 *
	 * @param entityClass
	 * @return
	 */
	private String generateMapperClassName(Class<?> entityClass) {
		String entityClassName = entityClass.getSimpleName(); // 获取实体类的类名
		String mapperClassName;
		if (entityClassName.endsWith("DO")) {
			mapperClassName = entityClassName.substring(0, entityClassName.length() - 2) + "Mapper"; // 去掉 "DO" 后缀，并加上 "Mapper" 后缀
		} else {
			mapperClassName = entityClassName + "Mapper"; // 在类名末尾直接加上 "Mapper" 后缀
		}
		String packagePath = entityClass.getPackage().getName(); // 获取实体类的包路径
		String mapperPackagePath = packagePath.replace("dal.dataobject", "dal.mysql"); // 替换包路径中的 "dal.dataobject" 为 "dal.mysql"
		return mapperPackagePath + "." + mapperClassName; // 拼接 Mapper 类的完整类名
	}


}

package com.xyy.saas.datasync.client.db.database;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-08-17 16:45
 */

import com.xyy.saas.datasync.client.constants.DataSyncUtil;
import com.xyy.saas.datasync.client.db.DataTableDmlTransformer;
import com.xyy.saas.datasync.client.entity.DataSyncEntityDefinition;
import com.xyy.saas.datasync.client.entity.DataSyncException;
import com.xyy.saas.datasync.client.constants.DataContextHolder;
import com.xyy.saas.datasync.client.entity.dto.TableColumn;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-08-17 16:20
 */
@Slf4j
public class SqliteDataTableDmlTransformer implements DataTableDmlTransformer {

	private JdbcTemplate jdbcTemplate;

	public SqliteDataTableDmlTransformer(JdbcTemplate jdbcTemplate) {
		this.jdbcTemplate = jdbcTemplate;
	}

	@Override
    public void doCheckAndUpdateTable(DataSource dataSource, DataSyncEntityDefinition dataSyncEntityDefinition) {
		Connection connection = null;
		String sql = null;
		try {
			connection = DataSourceUtils.getConnection(dataSource);
            Statement statement = connection.createStatement();
            String tableName = dataSyncEntityDefinition.getTableName();
            sql = getDetectionTable(tableName);
            ResultSet resultSet = statement.executeQuery(sql);
            //数据库的列名
            Set<String> tableFields = new HashSet();
            while (resultSet.next()) {
                //获取全部的列
                String name = resultSet.getString("name");
                tableFields.add(name);
            }
            //没有一列,说明表不存在,需要新建表
            if (CollectionUtils.isEmpty(tableFields)) {
                sql = createTableSql(dataSyncEntityDefinition);
				boolean result = statement.execute(sql);
				log.info("execute createTableSql:[{}],result:[{}]", sql, result);
				return;
            }
            List<DataSyncEntityDefinition.FieldDefinition> fields = dataSyncEntityDefinition.getFields();
            Map<String, DataSyncEntityDefinition.FieldDefinition> fieldDefinitionMap = dataSyncEntityDefinition.getFieldDefinitionMap();
            Set<String> diffFields = new HashSet<>(fieldDefinitionMap.keySet());
            diffFields.removeAll(tableFields);
            //没有需要变更
            if (diffFields.isEmpty()) {
                return;
            }

			Map<String, TableColumn> tableColumnMap = dataSyncEntityDefinition.getTableColumnMap();
            //拼装需要变更的sql记录
            StringBuilder stringBuilder = new StringBuilder();
            for (String tableField : diffFields) {
				alterTableSql(tableName, fieldDefinitionMap.get(tableField), tableColumnMap.get(tableField), stringBuilder);
                sql = stringBuilder.toString();
                //TODO sqlite似乎只支持单列新增,否则可以优化
				boolean result = statement.execute(sql);
				log.info("execute alterTableSql:[{}],result:[{}]", sql, result);
				stringBuilder.setLength(0);
            }
		} catch (SQLException e) {
            log.error("doCheckAndUpdateTable execute sql: {} error: {}", sql, e.getMessage(), e);
		} finally {
			try {
				connection.close();
			} catch (SQLException e) {
				throw new RuntimeException(e);
			}
		}
    }

    /**
     * 检测表
     *
     * @param tableName
     * @return
     */
    @Override
    public String getDetectionTable(String tableName) {
        return "PRAGMA table_info ('" + tableName + "')";
    }


    /**
     * 获取修改表语句
     *
     * @param fieldDefinition
     * @return
     */
    @Override
    public void alterTableSql(String tableName,
        DataSyncEntityDefinition.FieldDefinition fieldDefinition, TableColumn tableColumn, StringBuilder stringBuilder) {
        stringBuilder.append("ALTER TABLE " + tableName + " ");
        String fieldName = fieldDefinition.getTableFieldName();
        stringBuilder.append("ADD COLUMN ");
        transformToFieldSql(fieldDefinition, tableColumn, stringBuilder);
        stringBuilder.setLength(stringBuilder.length() - 1);
        stringBuilder.append(";");
    }

    /**
     * 获取建表SQL
     *
     * @param dataSyncEntityDefinition
     */
    @Override
    public String createTableSql(DataSyncEntityDefinition dataSyncEntityDefinition) {
        String tableName = dataSyncEntityDefinition.getTableName();
        if (StringUtils.isEmpty(tableName)) {
            throw new DataSyncException("数据同步获取建表语句异常, Error tableName Empty.");
        }
        StringBuilder stringBuilder = new StringBuilder();
        //添加表名
        stringBuilder.append("CREATE TABLE '");

        stringBuilder.append(tableName);
        stringBuilder.append("' (");

		Map<String, TableColumn> tableColumnMap = dataSyncEntityDefinition.getTableColumnMap();

		Map<String, DataSyncEntityDefinition.FieldDefinition> fieldDefinitionMap = dataSyncEntityDefinition.getFieldDefinitionMap();
		for (Map.Entry<String, DataSyncEntityDefinition.FieldDefinition> entry : fieldDefinitionMap.entrySet()) {
			// 获取云端列
			TableColumn tableColumn = tableColumnMap.get(entry.getKey());

			transformToFieldSql(entry.getValue(), tableColumn, stringBuilder);
		}

        stringBuilder.setLength(stringBuilder.length() - 1);
        stringBuilder.append(");");
        return stringBuilder.toString();
    }


    /**
     * 检查有变更的字段
     *
     * @param dataSyncEntityDefinition
     */
    @Override
    public void checkFieldUpdate(DataSyncEntityDefinition dataSyncEntityDefinition) {
//        StringBuilder stringBuilder = new StringBuilder();
//        transformToFieldSql(dataSyncEntityDefinition, stringBuilder);

    }

    /**
	 * 根据字段定义拼装字段SQL
     * @param fieldDefinition
     * @param tableColumn 云端列
     * @param stringBuilder
     * @return
     */
    @Override
    public String transformToFieldSql(DataSyncEntityDefinition.FieldDefinition fieldDefinition, TableColumn tableColumn, StringBuilder stringBuilder) {
		// 云端表结构信息 重写字段定义
		rewriteFieldDefinition(fieldDefinition, tableColumn);
		//添加列名
        stringBuilder.append("'")
			.append(DataSyncEntityDefinition.getSnakeCaseName(fieldDefinition.getFieldName()))
			.append("' ");

		String columnType = tableColumn != null ? getColumnTypeByTableColumn(tableColumn)
			: getColumnTypeByFieldType(fieldDefinition);

		stringBuilder.append(columnType);

		stringBuilder.append(" ");
        //判断是否主键,是否主键
        if (fieldDefinition.isPrimaryKey()) {
            stringBuilder.append("PRIMARY KEY ");
        }
        //判断是否自增,是否自增
        if (fieldDefinition.isAutoIncrement()) {
            stringBuilder.append("AUTOINCREMENT ");
        }
        //添加默认值
        if (fieldDefinition.getDefaultValue() != null) {
            stringBuilder.append("DEFAULT ");
			if (columnType.startsWith("VARCHAR") || columnType.startsWith("TEXT")) {
				stringBuilder.append("'").append(fieldDefinition.getDefaultValue()).append("'");
			} else {
				stringBuilder.append(fieldDefinition.getDefaultValue());
			}
        }
        //添加Not Null
        if (fieldDefinition.isNotNull()) {
			stringBuilder.append(" NOT NULL ");
        }
		//添加 COMMENT - sqlite不支持
		// if (StringUtils.hasLength(fieldDefinition.getComment())) {
		// 	stringBuilder.append(" COMMENT '").append(fieldDefinition.getComment()).append("'");
		// }
        stringBuilder.append(",");
        return stringBuilder.toString();
    }

	/**
	 * 重写字段定义
	 * @param fieldDefinition
	 * @param tableColumn
	 */
	private static void rewriteFieldDefinition(DataSyncEntityDefinition.FieldDefinition fieldDefinition, TableColumn tableColumn) {
		if (tableColumn == null) {
			return;
		}
		fieldDefinition.setNotNull(!tableColumn.nullable());
		String defaultValue = tableColumn.defaultValue();
		// mysql bool值 默认值 b'0', b'1'
		if (defaultValue != null && defaultValue.startsWith("b'")) {
			fieldDefinition.setDefaultValue(defaultValue.replaceAll("[b']", ""));
		} else if (defaultValue != null && defaultValue.toUpperCase().startsWith("CURRENT_TIMESTAMP")) {
			fieldDefinition.setDefaultValue("(datetime('now', 'localtime'))");
		}else {
			fieldDefinition.setDefaultValue(defaultValue);
		}
		fieldDefinition.setFieldLength(tableColumn.maxCharLength());
		// 会不会有2个不同的主键? 暂时不考虑
		fieldDefinition.setPrimaryKey(tableColumn.primaryKey());
		fieldDefinition.setAutoIncrement(tableColumn.autoIncrement());
		fieldDefinition.setComment(tableColumn.comment());
	}

	/**
	 * 根据字段类型推断列类型
	 * @param fieldDefinition
	 * @return
	 */
	private static String getColumnTypeByFieldType(DataSyncEntityDefinition.FieldDefinition fieldDefinition) {
		//添加类型
		Class<?> fieldType = fieldDefinition.getFieldType();
		return switch (fieldType.getName()) {
			case "java.lang.Long",
				 "long"
				-> "BIGINT";
			case "java.lang.Integer",
				 "int",
				 "java.lang.Boolean",
				 "boolean"
				-> "INTEGER";
			case "java.util.List",
				 "java.util.Set",
				 "java.util.Map",
				 "cn.iocoder.yudao.module.infra.framework.file.core.client.FileClientConfig",
				 "java.lang.String"
				-> "VARCHAR(" + fieldDefinition.getFieldLength() + ")";
			case "java.util.Date",
				 "java.time.LocalDateTime"
				-> "TIMESTAMP";
			case "java.math.BigDecimal"
				-> "NUMERIC";
			//byte[]数组类型
			case "[B"
				-> "VARCHAR(" + fieldDefinition.getFieldLength() + ")";
			default
				-> throw new DataSyncException("数据同步初始化,transformToSqlite switch fieldType not find. Field ClassName:" + fieldType.getName() + ", fieldName:" + fieldDefinition.getFieldName());
		};
	}

	/**
	 * 根据云端列类型转换列类型
	 * @param tableColumn
	 * @return
	 */
	private static String getColumnTypeByTableColumn(TableColumn tableColumn) {
		return switch (tableColumn.dataType().toUpperCase()) {
			case "BIT" -> "BOOLEAN";
			case "TINYINT" -> "TINYINT";
			case "SMALLINT" -> "SMALLINT";
			case "MEDIUMINT" -> "MEDIUMINT";
			case "INT", "BIGINT", "INTEGER" -> "INTEGER";

			case "FLOAT" -> "FLOAT";
			case "DOUBLE" -> "DOUBLE";
			case "DECIMAL", "NUMERIC" -> "DECIMAL";

			case "DATE", "DATETIME", "TIMESTAMP" -> "TIMESTAMP";

			case "CHAR", "VARCHAR" -> "VARCHAR" + (tableColumn.maxCharLength() != null ? "(" + tableColumn.maxCharLength() + ")" : "");

			case "BINARY", "VARBINARY", "TINYBLOB", "BLOB", "MEDIUMBLOB", "LONGBLOB" -> "BLOB";

			default -> "TEXT";
		};
	}

	/**
	 * @param tableName
	 * @param columnName
	 * @return
	 */
	@Override
	public boolean checkTableColumnExist(String tableName, String columnName) {
//		String queryColumnSql = "PRAGMA table_info(" + tableName + ") WHERE name = ?";
		String queryColumnSql = "SELECT * FROM pragma_table_info('" + tableName + "') WHERE name = ?";

		List<Map<String, Object>> columns = jdbcTemplate.queryForList(queryColumnSql, columnName);
		return !columns.isEmpty();
	}

	/**
	 * @param tableName
	 * @return
	 */
	@Override
	public Long getMaxBaseVersion(String tableName) {
		String maxVersionSql = "SELECT MAX(" + DataSyncUtil.BASE_VERSION + ") FROM " + tableName;
		boolean isNotStaticTable = checkTableColumnExist(tableName, DataSyncUtil.TENANT_ID);
		String staticTableMaxVersionSql = maxVersionSql;
		if (isNotStaticTable) {
			staticTableMaxVersionSql = maxVersionSql + " where tenant_id = " + DataContextHolder.getTenantId();
		}
		Long maxBaseVersion = jdbcTemplate.queryForObject(staticTableMaxVersionSql, Long.class);
		if (maxBaseVersion == null) {
			//tenant_id字段可能是空
			//就去掉where tenant_id = 条件
			maxBaseVersion = jdbcTemplate.queryForObject(maxVersionSql, Long.class);
		}
		return maxBaseVersion != null ? maxBaseVersion : 0L;
	}
}

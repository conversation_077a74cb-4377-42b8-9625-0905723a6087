package com.xyy.saas.datasync.client.db.database;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-08-17 16:45
 */

import com.xyy.saas.datasync.client.constants.DataContextHolder;
import com.xyy.saas.datasync.client.constants.DataSyncUtil;
import com.xyy.saas.datasync.client.db.DataTableDmlTransformer;
import com.xyy.saas.datasync.client.db.table.DataSyncPush;
import com.xyy.saas.datasync.client.entity.DataSyncEntityDefinition;
import com.xyy.saas.datasync.client.entity.DataSyncException;
import com.xyy.saas.datasync.client.entity.dto.TableColumn;
import com.xyy.saas.localserver.utils.DbUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-08-17 16:20
 */
@Slf4j
public class H2DataTableDmlTransformer implements DataTableDmlTransformer {

	private final JdbcTemplate jdbcTemplate;

	public H2DataTableDmlTransformer(JdbcTemplate jdbcTemplate) {
		this.jdbcTemplate = jdbcTemplate;
	}


	/**
	 * 初始化表，比如 data_sync_push
	 */
	@Override
	public void initializeTable() {
		try {
			String createTableSql = """
				CREATE TABLE IF NOT EXISTS data_sync_push
				(
					id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
					tenantId VARCHAR(255) NOT NULL,
					table_name VARCHAR(255) NOT NULL,
					dml_type VARCHAR(255) NOT NULL,
					source_id VARCHAR(255) NOT NULL,
					create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
					last_sync_time TIMESTAMP NOT NULL
				)""";
			jdbcTemplate.execute(createTableSql);
		} catch (Exception e) {
			throw new DataSyncException("SqliteDataTableDmlTransformer initializeTable error", e);
		}
	}

	@Override
	public <T> void doCheckAndUpdateTable(DataSource dataSource, DataSyncEntityDefinition<T> dataSyncEntityDefinition) {
		String sql = null;
		try (Connection connection = DataSourceUtils.getConnection(dataSource); Statement statement = connection.createStatement();) {

			String tableName = dataSyncEntityDefinition.getTableName();
			sql = getDetectionTable(tableName);
			ResultSet resultSet = statement.executeQuery(sql);
			//数据库的列名
			Set<String> tableFields = new HashSet<>();
			while (resultSet.next()) {
				//获取全部的列, 列名转小写
				String name = resultSet.getString("FIELD").toLowerCase();
				tableFields.add(name);
			}
			//没有一列,说明表不存在,需要新建表
			if (CollectionUtils.isEmpty(tableFields)) {
				sql = createTableSql(dataSyncEntityDefinition);
				statement.execute(sql);
				log.info("EXECUTE DDL: {}", sql);
				return;
			}
			Map<String,DataSyncEntityDefinition.FieldDefinition> fieldDefinitionMap = dataSyncEntityDefinition.getFieldDefinitionMap();
			// 字段名忽略大小写
			Set<String> addColumns = fieldDefinitionMap.keySet().stream().filter(f -> !tableFields.contains(f.toLowerCase())).collect(Collectors.toSet());

			Set<String> allFields = fieldDefinitionMap.keySet().stream().map(String::toLowerCase).collect(Collectors.toSet());
			Set<String> dropColumns = tableFields.stream().filter(f -> !allFields.contains(f.toLowerCase())).collect(Collectors.toSet());

			if (!dropColumns.isEmpty()) {
				log.error("表 {} 存在多余字段, 请手动删除: {}", tableName, dropColumns);
				// 不提供删除字段逻辑，防止数据丢失
			}

			// 没有需要变更
			if (addColumns.isEmpty()) {
				return;
			}

			Map<String,TableColumn> tableColumnMap = dataSyncEntityDefinition.getTableColumnMap();
			//拼装需要变更的sql记录
			StringBuilder stringBuilder = new StringBuilder();
			for (String tableField : addColumns) {
				alterTableSql(tableName, fieldDefinitionMap.get(tableField), tableColumnMap.get(tableField), stringBuilder);
				sql = stringBuilder.toString();
				//H2支持多列新增，但这里保持单列新增的逻辑
				statement.execute(sql);
				log.info("EXECUTE DDL: {}", sql);
				stringBuilder.setLength(0);
			}
		} catch (SQLException e) {
			log.error("doCheckAndUpdateTable execute sql: {} error: {}", sql, e.getMessage(), e);
		}
	}

	/**
	 * 检测表结构（H2实现）
	 *
	 * @param tableName
	 * @return
	 */
	@Override
	public String getDetectionTable(String tableName) {
		return MessageFormat.format("SHOW COLUMNS FROM {0}", tableName);
	}


	/**
	 * 构建ALTER TABLE语句（H2语法）
	 *
	 * @param fieldDefinition
	 * @return
	 */
	@Override
	public void alterTableSql(String tableName, DataSyncEntityDefinition.FieldDefinition fieldDefinition, TableColumn tableColumn, StringBuilder stringBuilder) {
		if (checkTableColumnExist(tableName, DataSyncEntityDefinition.getSnakeCaseName(fieldDefinition.getFieldName()))) {
			return;
		}
		stringBuilder.append("ALTER TABLE ").append(DbUtils.addBackquote(tableName)).append(" ").append("ADD COLUMN ");

		transformToFieldSql(fieldDefinition, tableColumn, stringBuilder);
		// 移除末尾逗号
		stringBuilder.setLength(stringBuilder.lastIndexOf(","));
		stringBuilder.append(";");
	}

	/**
	 * 构建CREATE TABLE语句（H2语法）
	 *
	 * @param dataSyncEntityDefinition
	 */
	@Override
	public <T> String createTableSql(DataSyncEntityDefinition<T> dataSyncEntityDefinition) {
		String tableName = dataSyncEntityDefinition.getTableName();
		if (ObjectUtils.isEmpty(tableName)) {
			throw new DataSyncException("数据同步获取建表语句异常, Error tableName Empty.");
		}
		log.info("createTableSql tableName:{}", tableName);

		//添加表名
		StringBuilder stringBuilder = new StringBuilder("CREATE TABLE ").append(DbUtils.addBackquote(tableName)).append(" \n(\n");

		// 生成列定义
		Map<String,TableColumn> tableColumnMap = dataSyncEntityDefinition.getTableColumnMap();

		Map<String,DataSyncEntityDefinition.FieldDefinition> fieldDefinitionMap = dataSyncEntityDefinition.getFieldDefinitionMap();
		for (Map.Entry<String,DataSyncEntityDefinition.FieldDefinition> entry : fieldDefinitionMap.entrySet()) {
			// 获取云端列
			TableColumn tableColumn = tableColumnMap.get(entry.getKey());

			transformToFieldSql(entry.getValue(), tableColumn, stringBuilder);
		}

		// 移除末尾逗号
		stringBuilder.setLength(stringBuilder.lastIndexOf(","));
		stringBuilder.append("\n);");

		// 处理 COMMENT（H2不支持内联注释，字段注释单独声明）
		List<String> comments = dataSyncEntityDefinition.getFieldDefinitionMap().values().stream().map(f -> getCommentSql(tableName, f)).filter(Objects::nonNull).toList();

		if (!comments.isEmpty()) {
			stringBuilder.append("\n").append(String.join("\n", comments));
		}

		return stringBuilder.toString();
	}

	/**
	 * H2数据库 字段注释需要单独加
	 *
	 * @param tableName
	 * @param fieldDefinition
	 * @return
	 */
	private String getCommentSql(String tableName, DataSyncEntityDefinition.FieldDefinition fieldDefinition) {
		if (!StringUtils.hasLength(fieldDefinition.getComment())) {
			return null;
		}
		String columnName = DataSyncEntityDefinition.getSnakeCaseName(fieldDefinition.getFieldName());
		return MessageFormat.format("COMMENT ON COLUMN {0}.{1} IS ''{2}'';", DbUtils.addBackquote(tableName), DbUtils.addBackquote(columnName), fieldDefinition.getComment());
	}


	/**
	 * 检查有变更的字段
	 *
	 * @param dataSyncEntityDefinition
	 */
	@Override
	public <T> void checkFieldUpdate(DataSyncEntityDefinition<T> dataSyncEntityDefinition) {
		//        StringBuilder stringBuilder = new StringBuilder();
		//        transformToFieldSql(dataSyncEntityDefinition, stringBuilder);

	}

	/**
	 * 根据字段定义拼装字段SQL
	 *
	 * @param fieldDefinition
	 * @param tableColumn     云端列
	 * @param stringBuilder
	 * @return
	 */
	@Override
	public String transformToFieldSql(DataSyncEntityDefinition.FieldDefinition fieldDefinition, TableColumn tableColumn, StringBuilder stringBuilder) {
		// 云端表结构信息 重写字段定义
		rewriteFieldDefinition(fieldDefinition, tableColumn);

		// 添加列名（关键字特殊处理）
		String columnName = DataSyncEntityDefinition.getSnakeCaseName(fieldDefinition.getFieldName());
		stringBuilder.append(" ").append(DbUtils.addBackquote(columnName));

		// 字段类型
		String columnType = getH2ColumnType(tableColumn, fieldDefinition);
		stringBuilder.append(" ").append(columnType);
		//判断是否自增,是否自增
		if (fieldDefinition.isAutoIncrement()) {
			stringBuilder.append(" GENERATED BY DEFAULT AS IDENTITY ");
		}
		//判断是否主键,是否主键（不能在自增前面）
		if (fieldDefinition.isPrimaryKey()) {
			stringBuilder.append(" PRIMARY KEY ");
		}
		//添加Not Null
		if (fieldDefinition.isNotNull()) {
			stringBuilder.append(" NOT NULL ");
		}
		//添加默认值
		if (fieldDefinition.getDefaultValue() != null) {
			String defaultValue = formatDefaultValue(fieldDefinition, columnType);
			stringBuilder.append(" DEFAULT ").append(defaultValue);
		}
		stringBuilder.append(",\n");
		return stringBuilder.toString();
	}

	/**
	 * 格式化默认值（处理H2特殊语法）
	 */
	private String formatDefaultValue(DataSyncEntityDefinition.FieldDefinition fieldDef, String columnType) {
		Object defaultValue = fieldDef.getDefaultValue();
		if (defaultValue == null) return "";
		// 处理时间戳默认值
		if (defaultValue.toString().contains("CURRENT_TIMESTAMP")) {
			return "CURRENT_TIMESTAMP";
		}
		// 处理字符串类型
		else if (columnType.startsWith("VARCHAR") || columnType.startsWith("CHAR") || columnType.startsWith("TEXT")) {
			return "'" + defaultValue + "'";
		}
		// 处理布尔值
		else if (columnType.equals("BOOLEAN")) {
			return defaultValue.toString().equalsIgnoreCase("true") ? "TRUE" : "FALSE";
		}
		return defaultValue.toString();
	}

	/**
	 * 重写字段定义
	 *
	 * @param fieldDefinition
	 * @param tableColumn
	 */
	private static void rewriteFieldDefinition(DataSyncEntityDefinition.FieldDefinition fieldDefinition, TableColumn tableColumn) {
		if (tableColumn == null) {
			return;
		}
		fieldDefinition.setNotNull(!tableColumn.nullable());
		String defaultValue = tableColumn.defaultValue();
		// mysql bool值 默认值 b'0', b'1'
		if (defaultValue != null && defaultValue.startsWith("b'")) {
			fieldDefinition.setDefaultValue(defaultValue.replaceAll("[b']", ""));
		} else if (defaultValue != null && defaultValue.toUpperCase().startsWith("CURRENT_TIMESTAMP")) {
			fieldDefinition.setDefaultValue("CURRENT_TIMESTAMP");
		} else {
			fieldDefinition.setDefaultValue(defaultValue);
		}
		fieldDefinition.setFieldLength(tableColumn.maxCharLength());
		// 会不会有2个不同的主键? 暂时不考虑
		fieldDefinition.setPrimaryKey(tableColumn.primaryKey());
		fieldDefinition.setAutoIncrement(tableColumn.autoIncrement());
		fieldDefinition.setComment(tableColumn.comment());
	}

	/**
	 * 1、根据云端列类型转换列类型
	 * 2、根据字段类型推断列类型
	 *
	 * @param tableColumn
	 * @return
	 */
	private static String getH2ColumnType(TableColumn tableColumn, DataSyncEntityDefinition.FieldDefinition fieldDefinition) {
		if (tableColumn != null && !ObjectUtils.isEmpty(tableColumn.dataType())) {
			return switch (tableColumn.dataType().toUpperCase()) {
				case "BIT" -> "BOOLEAN";
				case "TINYINT" -> "TINYINT";
				case "SMALLINT" -> "SMALLINT";
				case "INT", "INTEGER" -> "INT";
				case "BIGINT" -> "BIGINT";

				case "FLOAT" -> "REAL";
				case "DOUBLE" -> "DOUBLE";
				case "DECIMAL" -> "DECIMAL" + tableColumn.getNumericWithLength();

				case "VARCHAR", "CHAR" -> "VARCHAR" + tableColumn.getVarcharWithLength();

				case "DATE", "DATETIME", "TIMESTAMP" -> "TIMESTAMP";
				default -> tableColumn.dataType();
			};
		}
		// 字段类型
		Class<?> fieldType = fieldDefinition.getFieldType();
		return switch (fieldType.getName()) {
			case "java.lang.Long", "long" -> "BIGINT";
			case "java.lang.Integer", "int", "java.lang.Boolean", "boolean" -> "INT";
			case "java.util.List", "java.util.Set", "java.util.Map", "cn.iocoder.yudao.module.infra.framework.file.core.client.FileClientConfig", "java.lang.String",
				 //byte[]数组类型
				 "[B" -> "VARCHAR(" + fieldDefinition.getFieldLength() + ")";
			case "java.util.Date", "java.time.LocalDate", "java.time.LocalDateTime" -> "TIMESTAMP";
			case "java.math.BigDecimal" -> "NUMERIC";
			default -> "TEXT";
//			default -> throw new DataSyncException("数据同步初始化,transformToH2 switch fieldType not find. Field ClassName:" + fieldType.getName() + ", fieldName:" + fieldDefinition.getFieldName());
		};
	}

	/**
	 * @param tableName
	 * @param columnName
	 * @return
	 */
	@Override
	public boolean checkTableColumnExist(String tableName, String columnName) {
		String queryColumnSql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? AND  COLUMN_NAME = ?";
		// 默认SCHEMA：PUBLIC
		Integer count = jdbcTemplate.queryForObject(queryColumnSql, Integer.class, "PUBLIC", tableName, columnName);
		return count > 0;
	}

	/**
	 * @param tableName
	 * @return
	 */
	@Override
	public Long getMaxBaseVersion(String tableName) {
		String maxVersionSql = "SELECT MAX(" + DataSyncUtil.BASE_VERSION + ") FROM " + DbUtils.addBackquote(tableName);
		boolean isNotStaticTable = checkTableColumnExist(tableName, DataSyncUtil.TENANT_ID);
		String staticTableMaxVersionSql = maxVersionSql;
		if (isNotStaticTable) {
			staticTableMaxVersionSql = maxVersionSql + " WHERE tenant_id = " + DataContextHolder.getTenantId();
		}
		Long maxBaseVersion = jdbcTemplate.queryForObject(staticTableMaxVersionSql, Long.class);
		if (maxBaseVersion == null) {
			//tenant_id字段可能是空
			//就去掉where tenant_id = 条件
			maxBaseVersion = jdbcTemplate.queryForObject(maxVersionSql, Long.class);
		}
		return maxBaseVersion != null ? maxBaseVersion : 0L;
	}
}

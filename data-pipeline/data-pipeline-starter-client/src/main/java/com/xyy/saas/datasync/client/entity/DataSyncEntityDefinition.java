package com.xyy.saas.datasync.client.entity;

import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.PropertyNamingStrategy.PropertyNamingStrategyBase;
import com.xyy.saas.datasync.client.constants.FieldSupport;
import com.xyy.saas.datasync.client.entity.dto.TableColumn;
import com.xyy.saas.localserver.utils.DbUtils;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.Length;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.util.ClassUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Nullable;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.*;

/**
 * @Desc 数据同步实体定义
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-29 17:37
 */
@Getter
@Setter
@ToString
@Slf4j
public class DataSyncEntityDefinition<T> {


	/**
	 * 驼峰转下划线命名策略
	 */
	private static PropertyNamingStrategyBase snakeCase = new PropertyNamingStrategy.SnakeCaseStrategy();
	/**
	 * 下划线转驼峰命名策略
	 */
	private static PropertyNamingStrategyBase upperCamelCase = new PropertyNamingStrategy.UpperCamelCaseStrategy();
	/* 可能为空 */
	@Nullable
    private AnnotationAttributes annotationAttributes;
	private String className;
	private String tableName;
	private FieldDefinition primaryKey;
	private List<FieldDefinition> fields;
	private Class<T> clazz;

	/**
	 * 表列名 有下划线的字段名,  列定义
	 */
	private Map<String, DataSyncEntityDefinition.FieldDefinition> fieldDefinitionMap;

	/**
	 * 云端mysql表的信息
	 */
	private Map<String, TableColumn> tableColumnMap = new HashMap<>();

	/**
	 * 创建表定义
	 *
	 * @param className
	 * @param annotationAttributes
	 * @throws ClassNotFoundException
	 * @link {com.xyy.saas.datasync.client.entity.ClassPathDataSyncEntityScanner#findCandidateDatasyncEntity(java.lang.Class[], java.lang.String)}
	 */
    @Builder
    public DataSyncEntityDefinition(String className, AnnotationAttributes annotationAttributes)
        throws ClassNotFoundException {
		try {
			// 获取类对象
			Class<T> clazz = (Class<T>) Class.forName(className);
			this.clazz = clazz;
			this.className = className;
			this.annotationAttributes = annotationAttributes;
			// 如果annotationAttributes为null，直接返回
			if (annotationAttributes == null) {
				return;
			}
			// 获取表名
			String tableName = getTableNameFromAttributes(annotationAttributes);
			if (StringUtils.isEmpty(tableName)) {
				// 如果表名为空，则根据类名生成
				tableName = getDefaultTableName(className);
			}
			this.tableName = tableName;

			// 构建字段并检查
			buildFields();
			checkFields();
		} catch (ClassNotFoundException e) {
			// 处理类未找到异常
			log.error("Class not found: {}", className);
			throw e;
		}
	}


	/**
	 * 根据驼峰式命名获取下划线命名
	 *
	 * @param name 表名
	 * @return
	 */
    public static String getSnakeCaseName(String name) {
		return snakeCase.translate(name);
	}

	/**
	 * 根据下划线命名获取驼峰式命名
	 *
	 * @param name
	 * @return
	 */
	public static String getCamelCaseName(String name) {
		return upperCamelCase.translate(name);
	}

	/**
	 * 获取所有字段(包含父类中的字段)
	 * @param clazz
	 * @return
	 */
	public static List<Field> getAllFields(Class<?> clazz) {
		List<Field> fields = new ArrayList<>();
		addFields(clazz, fields);
		return fields;
	}

	/**
	 * 递归获取本类以及父类字段
	 * @param clazz
	 * @param fields
	 */
	private static void addFields(Class<?> clazz, List<Field> fields) {
		if (clazz == null || clazz == Object.class) {
			return;
		}
		// 获取当前类声明的字段
		Field[] declaredFields = clazz.getDeclaredFields();
		for (Field field : declaredFields) {
			// 过滤掉 static final 字段
			if (Modifier.isStatic(field.getModifiers()) || Modifier.isFinal(field.getModifiers())) {
				continue;
			}
			fields.add(field);
		}
		// 递归获取父类的字段
		addFields(clazz.getSuperclass(), fields);
	}

	/**
	 * 获取表名
	 *
	 * @param attributes
	 * @return
	 */
	private String getTableNameFromAttributes(AnnotationAttributes attributes) {
		// 优先从"tableName"属性获取表名，如果不存在则尝试从"value"属性获取
		String tableName = attributes.containsKey("tableName") ?
			attributes.getString("tableName") :
			attributes.containsKey("value") ? attributes.getString("value") : "";
		return tableName;
	}

	/**
	 * 获取默认的表名
	 *
	 * @param className
	 * @return
	 */
	private String getDefaultTableName(String className) {
		// 根据类名生成默认表名
		String shortName = ClassUtils.getShortName(className);
		return getSnakeCaseName(shortName);
	}

	private void buildFields() {
		// TODO 需要去掉 static final字段（ID_MASTER）
		List<Field> fields = getAllFields(clazz);
		List<FieldDefinition> fieldDefinitions = new ArrayList<>(fields.size());
		this.fieldDefinitionMap = new LinkedHashMap<>(fields.size());

		for (Field field : fields) {
			FieldDefinition fieldDefinition = buildFieldDefinition(field);
			fieldDefinitions.add(fieldDefinition);
		}
		rectifyPrimaryKey();
		this.fields = fieldDefinitions;
		FieldSupport.buildBaseVersion(this);

		// 租户表 主键id 就是 tenant_id
		if (!DbUtils.isTenantDB(tableName)) {
			FieldSupport.buildTenantId(this);
		}

	}

	private FieldDefinition buildFieldDefinition(Field field) {
		FieldDefinition.FieldDefinitionBuilder builder = FieldDefinition.builder();
		String fieldName = field.getName();
		builder.fieldName(fieldName);
		String tableFieldName = getSnakeCaseName(fieldName);
		builder.tableFieldName(tableFieldName);
		builder.fieldType(field.getType());

		switch (fieldName) {
			case "deleted" -> builder.defaultValue("0").notNull(true);
			case "creator", "updater" -> builder.defaultValue("").notNull(false).fieldLength(64);
			case "createTime", "updateTime" -> builder.defaultValue("CURRENT_TIMESTAMP").notNull(true);
		}

		NotNull notNull = field.getAnnotation(NotNull.class);
		if (notNull != null) {
			builder.notNull(true);
		}
		Length length = field.getAnnotation(Length.class);
		if (length != null) {
			int max = length.max();
			if (max < 2147483647) {
				builder.fieldLength(max);
			}
		}
		AutoIncrement autoIncrement = field.getAnnotation(AutoIncrement.class);
		if (autoIncrement != null) {
			builder.autoIncrement(true);
		}
		//默认值
		DefaultValue defaultValue = field.getAnnotation(DefaultValue.class);
		if (defaultValue != null) {
			builder.defaultValue(defaultValue.value());
		}
		builder.fieldType(field.getType());

		//数据同步主键id
		PrimaryKey primaryKey = field.getAnnotation(PrimaryKey.class);
		//mybatis-plus的id自动生成主键
		TableId tableId = field.getAnnotation(TableId.class);
		if (primaryKey != null || tableId != null) {
			if (this.primaryKey != null) {
				throw new DataSyncException("数据同步,扫描实体 DataSyncEntityDefinition[" + className +
												"], 重复扫描到主键primaryKey[" + fieldName + "]" + ",已扫描主键[" + this.primaryKey.getFieldName() + "]");
			}
			builder.primaryKey(true);
		}

		FieldDefinition fieldDefinition = builder.build();
		if (primaryKey != null) {
			this.primaryKey = fieldDefinition;
		}
		this.fieldDefinitionMap.put(tableFieldName, fieldDefinition);
		return fieldDefinition;
	}


	/**
	 * 效验主键,未使用主键注解，默认字段为id的是主键
	 */
	private void rectifyPrimaryKey() {
		if (this.primaryKey == null) {
//            String primaryKey = annotationAttributes.getString("primaryKey");
			//默认字段id的主键
			FieldDefinition idDefinition = fieldDefinitionMap.get("id");
			if (idDefinition == null) {
                throw new DataSyncException("数据同步,扫描实体 DataSyncEntityDefinition[" + className + "],没有配置主键!");
            }
			idDefinition.setPrimaryKey(true);
            this.primaryKey = idDefinition;
        }
    }

    /**
     * 字段校验
     * 1. 主键PrimaryKey 有且只有一个
     */
    public void checkFields() {
        // 主键有且只有一个
        final ArrayList<FieldDefinition> primaryKeys = new ArrayList<>();
		for (FieldDefinition field : this.fields) {
			if (field.isPrimaryKey()) {
				primaryKeys.add(field);
			}
		}
		if (primaryKeys.isEmpty()) {
			throw new DataSyncException("[数据同步] 没有配置主键, className:" + className);
		}
		if (primaryKeys.size() > 1) {
			throw new DataSyncException("[数据同步] 主键配置有[" +  primaryKeys.size() + "]个主键, className:" + className);
        }
    }

    @Builder
    @Getter
    @Setter
    @ToString
    static public class FieldDefinition {

        private String fieldName;

        private String tableFieldName;

        private Class<?> fieldType;

        @Default
        private int fieldLength = 255;

        /**
         * 默认值
         */
        private String defaultValue;

        /**
         * 是否不为空
         */
        private boolean notNull;

        /**
         * 是否自增
         */
        private boolean autoIncrement;

        /** 是否主键*/
        private boolean primaryKey;

        /** 字段描述 */
        private String comment;
    }
}

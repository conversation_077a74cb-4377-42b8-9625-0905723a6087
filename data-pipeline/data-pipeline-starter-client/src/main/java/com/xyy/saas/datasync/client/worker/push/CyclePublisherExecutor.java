package com.xyy.saas.datasync.client.worker.push;

import cn.hutool.core.map.MapUtil;
import com.xyy.saas.datasync.client.autoconfigure.DataSyncClientProperties;
import com.xyy.saas.datasync.client.constants.DataContextHolder;
import com.xyy.saas.datasync.client.db.DataSyncPersistencer;
import com.xyy.saas.datasync.client.db.table.DataSyncPush;
import com.xyy.saas.datasync.client.db.table.DataSyncPushDao;
import com.xyy.saas.datasync.client.protocol.DataPublisher;
import com.xyy.saas.datasync.client.protocol.vo.DataPushVO;
import com.xyy.saas.localserver.utils.DbUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.mapping.SqlCommandType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/6/17 下午4:49
 */
@Slf4j
public class CyclePublisherExecutor implements PublisherExecutor {

	private DataPublisher dataPublisher;

	private DataSyncPushDao dataSyncPushDao;

	public CyclePublisherExecutor(DataSyncClientProperties dataSyncClientProperties,
								  DataPublisher dataPublisher, DataSyncPushDao dataSyncPushDao) {
		this.dataPublisher = dataPublisher;
		this.dataSyncPushDao = dataSyncPushDao;
	}

	/**
	 * 遍历本地数据同步表的数据，循环push到服务端
	 */
	@Override
	public void pushInvoker() {
		String tableName = dataSyncPushDao.pollingDataSyncTableName();
		while (StringUtils.hasText(tableName)) {
			try {
				pushInvoker(tableName);
				tableName = dataSyncPushDao.pollingDataSyncTableName();
			} catch (Throwable e) {
				log.error("数据同步 pushInvoker error: {}", e.getMessage(), e);
				break;
			}
		}
	}

	/**
	 * @param tableName
	 */
	@Override
	public void pushInvoker(String tableName) {
		// 不过滤data = null 的数据, 防止这里一直循环查数据
		List<DataSyncPush> dataList = dataSyncPushDao.pollingDataSyncTableByTableName(tableName);
		//dataList有个字段，是操作类型，增、删、改吗，根据这个类型分组，然后分别push。
		if (CollectionUtils.isEmpty(dataList)) {
			return;
		}

		List<Long> idList = dataList.stream().map(DataSyncPush::getId).collect(Collectors.toList());
		// 去除data 为null的
		dataList = dataList.stream().filter(i -> i.getData() != null).toList();
		if (CollectionUtils.isEmpty(dataList)) {
			int cnt = dataSyncPushDao.deleteDataSyncTable(idList, null);
			log.info("数据同步 跳过push: 表名 {} 同步记录清除 {} / 总 {}", tableName, cnt, idList.size());
			return;
		}

		// data 可能会有null值，在云端push接口做兼容
		Map<String, List<Map<String, Object>>> dataMap = dataList.stream()
			.collect(Collectors.groupingBy(DataSyncPush::getDmlType,
										   Collectors.mapping(DataSyncPush::getData, Collectors.toList())));
		//这里查询出数据同步，然后分组，增、删、改。
		DataPushVO dataPushVO = DataPushVO.builder()
			.tableName(tableName)
			.insertList(dataMap.get(SqlCommandType.INSERT.name()))
			.updateList(dataMap.get(SqlCommandType.UPDATE.name()))
			.build();
		if (dataMap.get(SqlCommandType.DELETE.name()) != null) {
			List<Long> deleteIds = dataMap.get(SqlCommandType.DELETE.name()).stream()
				.map(map -> (Long) DbUtils.getIdValueFromMap(map))
				.collect(Collectors.toList());
			dataPushVO.setDeleteList(deleteIds);
		}
		if (!dataPushVO.checkData()) {
			return;
		}
		log.info("数据同步 push开始: 表名 {} 条数 {}", tableName, idList.size());
		List<Long> successIds = dataPublisher.push(DataContextHolder.getTenantId(), dataPushVO);
		int cnt = dataSyncPushDao.deleteDataSyncTable(idList, successIds);
		log.info("数据同步 push结束: 表名 {} 成功 {} / 总 {}", tableName, cnt, idList.size());
	}
}

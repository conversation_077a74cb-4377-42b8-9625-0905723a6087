<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>local-server-cloud</artifactId>
		<groupId>com.xyy.saas</groupId>
		<version>1.0-SNAPSHOT</version>
	</parent>

	<modules>
		<module>data-pipeline-starter-client</module>  <!-- 数据同步客户端 -->
		<module>data-pipeline-starter-server</module>  <!-- 数据同步服务端 -->
		<module>data-pipeline-sqlite</module>          <!-- sqlite客户端  -->
		<module>data-pipeline-plugin</module>          <!-- 数据同步插件  -->
	</modules>
	<artifactId>data-pipeline</artifactId>
	<name>data-pipeline</name>
	<packaging>pom</packaging>

	<properties>
		<java.version>21</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<dependencies>
		<dependency>
			<artifactId>utils-starter</artifactId>
			<groupId>com.xyy.saas</groupId>
		</dependency>
		<dependency>
			<artifactId>spring-boot-starter-test</artifactId>
			<groupId>org.springframework.boot</groupId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<artifactId>spring-boot-starter-web</artifactId>
			<groupId>org.springframework.boot</groupId>
			<scope>test</scope>
		</dependency>

	</dependencies>

	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-compiler-plugin</artifactId>
<!-- 					<version>3.8.1</version> -->
					<configuration>
						<source>21</source>
						<target>21</target>
						<parameters>true</parameters>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>

</project>

16:33:47.887 INFO  [e.validator.internal.util.Version:21] [] - HV000001: Hibernate Validator 8.0.1.Final
16:33:48.087 INFO  [ocalserver.LocalserverApplication:50] [] - Starting LocalserverApplication using Java 21.0.3 with PID 14800 (/Users/<USER>/codes/xyy/ls/saas-localserver/saas-localserver-application/target/classes started by caojialin in /Users/<USER>/codes/xyy/ls)
16:33:48.090 DEBUG [ocalserver.LocalserverApplication:51] [] - Running with Spring Boot v3.3.4, Spring v6.1.13
16:33:48.091 INFO  [calserver.LocalserverApplication:660] [] - The following 1 profile is active: "test"
16:33:50.501 WARN  [NamingStrategy$SnakeCaseStrategy:207] [] - PropertyNamingStrategy.SnakeCaseStrategy is used but it has been deprecated due to risk of deadlock. Consider using PropertyNamingStrategies.SnakeCaseStrategy instead. See https://github.com/FasterXML/jackson-databind/issues/2715 for more details.
16:33:50.502 WARN  [gStrategy$UpperCamelCaseStrategy:207] [] - PropertyNamingStrategy.UpperCamelCaseStrategy is used but it has been deprecated due to risk of deadlock. Consider using PropertyNamingStrategies.UpperCamelCaseStrategy instead. See https://github.com/FasterXML/jackson-databind/issues/2715 for more details.
16:33:50.515 DEBUG [y.ClassPathDataSyncEntityScanner:116] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.localserver.entity.merchant.MedicareMerchant]
16:33:50.520 DEBUG [y.ClassPathDataSyncEntityScanner:116] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.localserver.entity.product.MedicareProduct]
16:33:50.522 INFO  [ty.ClassPathDataSyncEntityScanner:61] [] - 数据同步扫描basePackage:[[com.xyy.saas.localserver.entity]],扫描到@DataSyncEntity的class size:[2]
16:33:52.753 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
16:33:52.765 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'stringOrNumberMigrationVersionConverter' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$StringOrNumberToMigrationVersionConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
16:33:53.394 WARN  [elegate$BeanPostProcessorChecker:429] [] - Bean 'cn.iocoder.yudao.framework.quartz.config.YudaoAsyncAutoConfiguration' of type [cn.iocoder.yudao.framework.quartz.config.YudaoAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
16:33:53.993 INFO  [.embedded.tomcat.TomcatWebServer:111] [] - Tomcat initialized with port 48081 (http)
16:33:54.009 INFO  [.coyote.http11.Http11NioProtocol:173] [] - Initializing ProtocolHandler ["http-nio-48081"]
16:33:54.016 INFO  [he.catalina.core.StandardService:173] [] - Starting service [Tomcat]
16:33:54.016 INFO  [che.catalina.core.StandardEngine:173] [] - Starting Servlet engine: [Apache Tomcat/10.1.30]
16:33:54.153 INFO  [nerBase.[Tomcat].[localhost].[/]:173] [] - Initializing Spring embedded WebApplicationContext
16:33:54.154 INFO  [rvletWebServerApplicationContext:296] [] - Root WebApplicationContext: initialization completed in 5926 ms
16:33:55.362 INFO  [async.sqlite.support.SqliteUtils:142] [] - create sqlite db file is success, filePath:/Users/<USER>/codes/xyy/ls/db/saas-local.db 
16:33:55.628 INFO  [m.zaxxer.hikari.HikariDataSource:109] [] - HikariPool-1 - Starting...
16:33:59.018 INFO  [om.zaxxer.hikari.pool.HikariPool:554] [] - HikariPool-1 - Added connection org.sqlite.jdbc4.JDBC4Connection@402fdef1
16:33:59.021 INFO  [m.zaxxer.hikari.HikariDataSource:122] [] - HikariPool-1 - Start completed.
16:33:59.041 INFO  [al.resource.ResourceNameValidator:37] [] - 3 SQL migrations were detected but not run because they did not follow the filename convention.
16:33:59.042 INFO  [al.resource.ResourceNameValidator:37] [] - Set 'validateMigrationNaming' to true to fail fast and see a list of the invalid file names.
16:33:59.049 INFO  [ org.flywaydb.core.FlywayExecutor:37] [] - Database: jdbc:sqlite:/Users/<USER>/codes/xyy/ls/db/saas-local.db (SQLite 3.45)
16:33:59.081 INFO  [emahistory.JdbcTableSchemaHistory:37] [] - Creating Schema History table "main"."flyway_schema_history" ...
16:33:59.153 INFO  [b.core.internal.command.DbMigrate:37] [] - Current version of schema "main": << Empty Schema >>
16:33:59.162 INFO  [b.core.internal.command.DbMigrate:37] [] - Schema "main" is up to date. No migration necessary.
16:33:59.316 INFO  [MybatisPlusJoinAutoConfiguration:139] [] - mybatis plus join properties config complete
16:33:59.607 INFO  [MybatisPlusJoinAutoConfiguration:139] [] - mybatis plus join SqlInjector init
16:34:38.494 ERROR [lient.db.DataSyncTableInitializer:87] [] - --------------getTableInfo from cloud error:Timeout on blocking read for 30000000000 NANOSECONDS----------------
java.lang.IllegalStateException: Timeout on blocking read for 30000000000 NANOSECONDS
	at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:129)
	at reactor.core.publisher.Mono.block(Mono.java:1807)
	at org.springframework.web.service.invoker.HttpServiceMethod$ReactorExchangeResponseFunction.execute(HttpServiceMethod.java:416)
	at org.springframework.web.service.invoker.HttpServiceMethod.invoke(HttpServiceMethod.java:130)
	at org.springframework.web.service.invoker.HttpServiceProxyFactory$HttpServiceMethodInterceptor.invoke(HttpServiceProxyFactory.java:304)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy195.getTableInfo(Unknown Source)
	at com.xyy.saas.datasync.client.db.DataSyncTableInitializer.getTableInfo(DataSyncTableInitializer.java:85)
	at com.xyy.saas.datasync.client.db.DataSyncTableInitializer.afterPropertiesSet(DataSyncTableInitializer.java:54)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1802)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.xyy.saas.localserver.LocalserverApplication.main(LocalserverApplication.java:22)
Caused by: java.util.concurrent.TimeoutException: Timeout on blocking read for 30000000000 NANOSECONDS
	... 28 common frames omitted
16:34:38.511 INFO  [ase.SqliteDataTableDmlTransformer:65] [] - execute createTableSql:[CREATE TABLE 'medicare_product' ('name' VARCHAR(255)  NOT NULL ,'tenant_id' VARCHAR(255) DEFAULT '' NOT NULL ,'base_version' BIGINT DEFAULT 0 NOT NULL ,'id' INTEGER PRIMARY KEY AUTOINCREMENT ,'common_name' VARCHAR(255)  NOT NULL );],result:[false]
16:34:38.513 INFO  [ase.SqliteDataTableDmlTransformer:65] [] - execute createTableSql:[CREATE TABLE 'medicare_merchant' ('tenant_id' VARCHAR(255) DEFAULT '' NOT NULL ,'base_version' BIGINT DEFAULT 0 NOT NULL ,'id' INTEGER PRIMARY KEY AUTOINCREMENT ,'medicare_institution_name' VARCHAR(100) ,'medicare_institution_code' VARCHAR(15) ,'organ_sign' VARCHAR(255)  NOT NULL );],result:[false]
16:34:39.722 INFO  [ndpoint.web.EndpointLinksResolver:60] [] - Exposing 1 endpoint beneath base path '/actuator'
16:34:40.117 INFO  [work.dict.core.DictFrameworkUtils:86] [] - [init][初始化 DictFrameworkUtils 成功]
16:34:40.126 INFO  [fig.YudaoJacksonAutoConfiguration:48] [] - [init][初始化 JsonUtils 成功]
16:34:40.197 INFO  [quartz.impl.StdSchedulerFactory:1220] [] - Using default implementation for ThreadExecutor
16:34:40.215 INFO  [quartz.core.SchedulerSignalerImpl:61] [] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
16:34:40.216 INFO  [ org.quartz.core.QuartzScheduler:229] [] - Quartz Scheduler v.2.3.2 created.
16:34:40.216 INFO  [    org.quartz.simpl.RAMJobStore:155] [] - RAMJobStore initialized.
16:34:40.217 INFO  [ org.quartz.core.QuartzScheduler:294] [] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

16:34:40.218 INFO  [quartz.impl.StdSchedulerFactory:1374] [] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
16:34:40.218 INFO  [quartz.impl.StdSchedulerFactory:1378] [] - Quartz scheduler version: 2.3.2
16:34:40.218 INFO  [org.quartz.core.QuartzScheduler:2293] [] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@106f566b
16:34:40.375 INFO  [s.common.spring.SpringContextUtil:38] [] - ------SpringContextUtil setApplicationContext-------
16:34:41.690 ERROR [ient.worker.DataSyncClientWorker:143] [] - push数据同步,start调度任务因同步机构号为空未开启, syncState:[NONE]
16:34:41.691 INFO  [.coyote.http11.Http11NioProtocol:173] [] - Starting ProtocolHandler ["http-nio-48081"]
16:34:41.709 INFO  [.embedded.tomcat.TomcatWebServer:243] [] - Tomcat started on port 48081 (http) with context path '/'
16:34:41.713 INFO  [ling.quartz.SchedulerFactoryBean:729] [] - Starting Quartz Scheduler now
16:34:41.713 INFO  [ org.quartz.core.QuartzScheduler:547] [] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
16:34:41.740 INFO  [ocalserver.LocalserverApplication:56] [] - Started LocalserverApplication in 55.003 seconds (process running for 58.289)
16:34:41.767 ERROR [ient.worker.DataSyncClientWorker:115] [] - pull数据同步,start调度任务因同步机构号为空未开启, syncState:[NONE]
16:34:42.758 INFO  [nner.core.BannerApplicationRunner:23] [] - 
----------------------------------------------------------
	项目启动成功！
	接口文档: 	https://doc.iocoder.cn/api-doc/ 
	开发文档: 	https://doc.iocoder.cn 
	视频教程: 	https://t.zsxq.com/02Yf6M7Qn 
----------------------------------------------------------
16:55:15.020 INFO  [ org.quartz.core.QuartzScheduler:585] [] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
16:55:18.984 INFO  [ling.quartz.SchedulerFactoryBean:844] [] - Shutting down Quartz Scheduler
16:55:18.986 INFO  [ org.quartz.core.QuartzScheduler:666] [] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
16:55:18.986 INFO  [ org.quartz.core.QuartzScheduler:585] [] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
16:55:18.987 INFO  [ org.quartz.core.QuartzScheduler:740] [] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
16:55:19.007 INFO  [m.zaxxer.hikari.HikariDataSource:349] [] - HikariPool-1 - Shutdown initiated...
16:55:19.041 INFO  [m.zaxxer.hikari.HikariDataSource:351] [] - HikariPool-1 - Shutdown completed.
17:14:19.660 INFO  [e.validator.internal.util.Version:21] [] - HV000001: Hibernate Validator 8.0.1.Final
17:14:19.794 INFO  [ocalserver.LocalserverApplication:50] [] - Starting LocalserverApplication using Java 21.0.3 with PID 18583 (/Users/<USER>/codes/xyy/ls/saas-localserver/saas-localserver-application/target/classes started by caojialin in /Users/<USER>/codes/xyy/ls)
17:14:19.795 DEBUG [ocalserver.LocalserverApplication:51] [] - Running with Spring Boot v3.3.4, Spring v6.1.13
17:14:19.797 INFO  [calserver.LocalserverApplication:660] [] - The following 1 profile is active: "test"
17:14:21.648 WARN  [rvletWebServerApplicationContext:633] [] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.xyy.saas.localserver.LocalserverApplication]
17:14:21.725 ERROR [framework.boot.SpringApplication:859] [] - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.xyy.saas.localserver.LocalserverApplication]
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:179)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:417)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:789)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:607)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.xyy.saas.localserver.LocalserverApplication.main(LocalserverApplication.java:22)
Caused by: org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'forwardWebClientConfig' for bean class [com.xyy.saas.inquiry.hospital.server.config.foward.ForwardWebClientConfig] conflicts with existing, non-compatible bean definition of same name and class [com.xyy.saas.inquiry.product.server.config.forward.ForwardWebClientConfig]
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.checkCandidate(ClassPathBeanDefinitionScanner.java:361)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:288)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:128)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:306)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:246)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:197)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:165)
	... 13 common frames omitted
17:24:03.989 INFO  [e.validator.internal.util.Version:21] [] - HV000001: Hibernate Validator 8.0.1.Final
17:24:04.089 INFO  [ocalserver.LocalserverApplication:50] [] - Starting LocalserverApplication using Java 21.0.3 with PID 19310 (/Users/<USER>/codes/xyy/ls/saas-localserver/saas-localserver-application/target/classes started by caojialin in /Users/<USER>/codes/xyy/ls)
17:24:04.090 DEBUG [ocalserver.LocalserverApplication:51] [] - Running with Spring Boot v3.3.4, Spring v6.1.13
17:24:04.091 INFO  [calserver.LocalserverApplication:660] [] - The following 1 profile is active: "test"
17:24:05.533 WARN  [rvletWebServerApplicationContext:633] [] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.xyy.saas.localserver.LocalserverApplication]
17:24:05.568 ERROR [framework.boot.SpringApplication:859] [] - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.xyy.saas.localserver.LocalserverApplication]
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:179)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:417)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:789)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:607)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.xyy.saas.localserver.LocalserverApplication.main(LocalserverApplication.java:22)
Caused by: org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'forwardWebClientConfig' for bean class [com.xyy.saas.inquiry.hospital.server.config.foward.ForwardWebClientConfig] conflicts with existing, non-compatible bean definition of same name and class [com.xyy.saas.inquiry.product.server.config.forward.ForwardWebClientConfig]
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.checkCandidate(ClassPathBeanDefinitionScanner.java:361)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:288)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:128)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:306)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:246)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:197)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:165)
	... 13 common frames omitted
17:25:29.297 INFO  [e.validator.internal.util.Version:21] [] - HV000001: Hibernate Validator 8.0.1.Final
17:25:29.386 INFO  [ocalserver.LocalserverApplication:50] [] - Starting LocalserverApplication using Java 21.0.3 with PID 19532 (/Users/<USER>/codes/xyy/ls/saas-localserver/saas-localserver-application/target/classes started by caojialin in /Users/<USER>/codes/xyy/ls)
17:25:29.387 DEBUG [ocalserver.LocalserverApplication:51] [] - Running with Spring Boot v3.3.4, Spring v6.1.13
17:25:29.388 INFO  [calserver.LocalserverApplication:660] [] - The following 1 profile is active: "test"
17:25:30.677 WARN  [rvletWebServerApplicationContext:633] [] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.xyy.saas.localserver.LocalserverApplication]
17:25:30.709 ERROR [framework.boot.SpringApplication:859] [] - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.xyy.saas.localserver.LocalserverApplication]
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:179)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:417)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:789)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:607)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.xyy.saas.localserver.LocalserverApplication.main(LocalserverApplication.java:22)
Caused by: org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'forwardWebClientConfig' for bean class [com.xyy.saas.inquiry.hospital.server.config.foward.ForwardWebClientConfig] conflicts with existing, non-compatible bean definition of same name and class [com.xyy.saas.inquiry.product.server.config.forward.ForwardWebClientConfig]
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.checkCandidate(ClassPathBeanDefinitionScanner.java:361)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:288)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:128)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:306)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:246)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:197)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:165)
	... 13 common frames omitted
19:55:37.145 INFO  [e.validator.internal.util.Version:21] [] - HV000001: Hibernate Validator 8.0.1.Final
19:55:37.301 INFO  [ocalserver.LocalserverApplication:50] [] - Starting LocalserverApplication using Java 21.0.3 with PID 27501 (/Users/<USER>/codes/xyy/ls/saas-localserver/saas-localserver-application/target/classes started by caojialin in /Users/<USER>/codes/xyy/ls)
19:55:37.302 DEBUG [ocalserver.LocalserverApplication:51] [] - Running with Spring Boot v3.3.4, Spring v6.1.13
19:55:37.304 INFO  [calserver.LocalserverApplication:660] [] - The following 1 profile is active: "test"
19:55:42.143 WARN  [NamingStrategy$SnakeCaseStrategy:207] [] - PropertyNamingStrategy.SnakeCaseStrategy is used but it has been deprecated due to risk of deadlock. Consider using PropertyNamingStrategies.SnakeCaseStrategy instead. See https://github.com/FasterXML/jackson-databind/issues/2715 for more details.
19:55:42.144 WARN  [gStrategy$UpperCamelCaseStrategy:207] [] - PropertyNamingStrategy.UpperCamelCaseStrategy is used but it has been deprecated due to risk of deadlock. Consider using PropertyNamingStrategies.UpperCamelCaseStrategy instead. See https://github.com/FasterXML/jackson-databind/issues/2715 for more details.
19:55:42.164 DEBUG [y.ClassPathDataSyncEntityScanner:116] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.localserver.entity.merchant.MedicareMerchant]
19:55:42.171 DEBUG [y.ClassPathDataSyncEntityScanner:116] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.localserver.entity.product.MedicareProduct]
19:55:42.173 INFO  [ty.ClassPathDataSyncEntityScanner:61] [] - 数据同步扫描basePackage:[[com.xyy.saas.localserver.entity]],扫描到@DataSyncEntity的class size:[2]
19:55:42.868 WARN  [rvletWebServerApplicationContext:633] [] - Exception encountered during context initialization - cancelling refresh attempt: java.lang.IllegalStateException: Error processing condition on org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizationAutoConfiguration.platformTransactionManagerCustomizers
19:55:42.894 INFO  [g.ConditionEvaluationReportLogger:82] [] - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
19:55:42.982 ERROR [framework.boot.SpringApplication:859] [] - Application run failed
java.lang.IllegalStateException: Error processing condition on org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizationAutoConfiguration.platformTransactionManagerCustomizers
	at org.springframework.boot.autoconfigure.condition.SpringBootCondition.matches(SpringBootCondition.java:60)
	at org.springframework.context.annotation.ConditionEvaluator.shouldSkip(ConditionEvaluator.java:108)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForBeanMethod(ConfigurationClassBeanDefinitionReader.java:183)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForConfigurationClass(ConfigurationClassBeanDefinitionReader.java:144)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitions(ConfigurationClassBeanDefinitionReader.java:120)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:429)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:789)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:607)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.xyy.saas.localserver.LocalserverApplication.main(LocalserverApplication.java:22)
Caused by: java.lang.IllegalStateException: Failed to introspect Class [com.xyy.saas.inquiry.config.servlet.TomcatServerConfiguration] from ClassLoader [jdk.internal.loader.ClassLoaders$AppClassLoader@18b4aac2]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:483)
	at org.springframework.util.ReflectionUtils.doWithMethods(ReflectionUtils.java:360)
	at org.springframework.util.ReflectionUtils.getUniqueDeclaredMethods(ReflectionUtils.java:417)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.lambda$getTypeForFactoryMethod$1(AbstractAutowireCapableBeanFactory.java:750)
	at java.base/java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1740)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getTypeForFactoryMethod(AbstractAutowireCapableBeanFactory.java:749)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineTargetType(AbstractAutowireCapableBeanFactory.java:682)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.predictBeanType(AbstractAutowireCapableBeanFactory.java:653)
	at org.springframework.beans.factory.support.AbstractBeanFactory.isFactoryBean(AbstractBeanFactory.java:1687)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:562)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:534)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.collectBeanNamesForType(OnBeanCondition.java:247)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getBeanNamesForType(OnBeanCondition.java:240)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getBeanNamesForType(OnBeanCondition.java:230)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getMatchingBeans(OnBeanCondition.java:183)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getMatchOutcome(OnBeanCondition.java:158)
	at org.springframework.boot.autoconfigure.condition.SpringBootCondition.matches(SpringBootCondition.java:47)
	... 17 common frames omitted
Caused by: java.lang.NoClassDefFoundError: com/xyy/common/config/TomcatServerConfig
	at java.base/java.lang.Class.getDeclaredMethods0(Native Method)
	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3578)
	at java.base/java.lang.Class.getDeclaredMethods(Class.java:2676)
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:465)
	... 33 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.xyy.common.config.TomcatServerConfig
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	... 37 common frames omitted
19:58:32.358 INFO  [e.validator.internal.util.Version:21] [] - HV000001: Hibernate Validator 8.0.1.Final
19:58:32.508 INFO  [ocalserver.LocalserverApplication:50] [] - Starting LocalserverApplication using Java 21.0.3 with PID 27658 (/Users/<USER>/codes/xyy/ls/saas-localserver/saas-localserver-application/target/classes started by caojialin in /Users/<USER>/codes/xyy/ls)
19:58:32.510 DEBUG [ocalserver.LocalserverApplication:51] [] - Running with Spring Boot v3.3.4, Spring v6.1.13
19:58:32.512 INFO  [calserver.LocalserverApplication:660] [] - The following 1 profile is active: "test"
19:58:38.240 WARN  [rvletWebServerApplicationContext:633] [] - Exception encountered during context initialization - cancelling refresh attempt: java.lang.IllegalStateException: Error processing condition on com.xyy.saas.eventbus.rocketmq.core.aliyun.AliyunEventBusSimpleEventMulticaster
19:58:38.269 INFO  [g.ConditionEvaluationReportLogger:82] [] - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
19:58:38.358 ERROR [framework.boot.SpringApplication:859] [] - Application run failed
java.lang.IllegalStateException: Error processing condition on com.xyy.saas.eventbus.rocketmq.core.aliyun.AliyunEventBusSimpleEventMulticaster
	at org.springframework.boot.autoconfigure.condition.SpringBootCondition.matches(SpringBootCondition.java:60)
	at org.springframework.context.annotation.ConditionEvaluator.shouldSkip(ConditionEvaluator.java:108)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader$TrackedConditionEvaluator.shouldSkip(ConfigurationClassBeanDefinitionReader.java:470)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitionsForConfigurationClass(ConfigurationClassBeanDefinitionReader.java:131)
	at org.springframework.context.annotation.ConfigurationClassBeanDefinitionReader.loadBeanDefinitions(ConfigurationClassBeanDefinitionReader.java:120)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:429)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:789)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:607)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.xyy.saas.localserver.LocalserverApplication.main(LocalserverApplication.java:22)
Caused by: java.lang.IllegalStateException: Failed to introspect Class [com.xyy.saas.inquiry.product.server.config.forward.ProductForwardWebClientConfig] from ClassLoader [jdk.internal.loader.ClassLoaders$AppClassLoader@18b4aac2]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:483)
	at org.springframework.util.ReflectionUtils.doWithMethods(ReflectionUtils.java:360)
	at org.springframework.util.ReflectionUtils.getUniqueDeclaredMethods(ReflectionUtils.java:417)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.lambda$getTypeForFactoryMethod$1(AbstractAutowireCapableBeanFactory.java:750)
	at java.base/java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1708)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getTypeForFactoryMethod(AbstractAutowireCapableBeanFactory.java:749)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineTargetType(AbstractAutowireCapableBeanFactory.java:682)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.predictBeanType(AbstractAutowireCapableBeanFactory.java:653)
	at org.springframework.beans.factory.support.AbstractBeanFactory.isFactoryBean(AbstractBeanFactory.java:1687)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doGetBeanNamesForType(DefaultListableBeanFactory.java:562)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBeanNamesForType(DefaultListableBeanFactory.java:534)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.collectBeanNamesForType(OnBeanCondition.java:247)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getBeanNamesForType(OnBeanCondition.java:240)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getBeanNamesForType(OnBeanCondition.java:230)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getMatchingBeans(OnBeanCondition.java:183)
	at org.springframework.boot.autoconfigure.condition.OnBeanCondition.getMatchOutcome(OnBeanCondition.java:158)
	at org.springframework.boot.autoconfigure.condition.SpringBootCondition.matches(SpringBootCondition.java:47)
	... 17 common frames omitted
Caused by: java.lang.NoClassDefFoundError: com/xyy/saas/inquiry/config/webclient/InquiryForwardProperties
	at java.base/java.lang.Class.getDeclaredMethods0(Native Method)
	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3578)
	at java.base/java.lang.Class.getDeclaredMethods(Class.java:2676)
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:465)
	... 33 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.xyy.saas.inquiry.config.webclient.InquiryForwardProperties
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	... 37 common frames omitted
20:00:36.555 INFO  [e.validator.internal.util.Version:21] [] - HV000001: Hibernate Validator 8.0.1.Final
20:00:36.667 INFO  [ocalserver.LocalserverApplication:50] [] - Starting LocalserverApplication using Java 21.0.3 with PID 28087 (/Users/<USER>/codes/xyy/ls/saas-localserver/saas-localserver-application/target/classes started by caojialin in /Users/<USER>/codes/xyy/ls)
20:00:36.668 DEBUG [ocalserver.LocalserverApplication:51] [] - Running with Spring Boot v3.3.4, Spring v6.1.13
20:00:36.670 INFO  [calserver.LocalserverApplication:660] [] - The following 1 profile is active: "test"
20:00:40.618 WARN  [NamingStrategy$SnakeCaseStrategy:207] [] - PropertyNamingStrategy.SnakeCaseStrategy is used but it has been deprecated due to risk of deadlock. Consider using PropertyNamingStrategies.SnakeCaseStrategy instead. See https://github.com/FasterXML/jackson-databind/issues/2715 for more details.
20:00:40.619 WARN  [gStrategy$UpperCamelCaseStrategy:207] [] - PropertyNamingStrategy.UpperCamelCaseStrategy is used but it has been deprecated due to risk of deadlock. Consider using PropertyNamingStrategies.UpperCamelCaseStrategy instead. See https://github.com/FasterXML/jackson-databind/issues/2715 for more details.
20:00:40.631 DEBUG [y.ClassPathDataSyncEntityScanner:116] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.localserver.entity.merchant.MedicareMerchant]
20:00:40.636 DEBUG [y.ClassPathDataSyncEntityScanner:116] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.localserver.entity.product.MedicareProduct]
20:00:40.638 INFO  [ty.ClassPathDataSyncEntityScanner:61] [] - 数据同步扫描basePackage:[[com.xyy.saas.localserver.entity]],扫描到@DataSyncEntity的class size:[2]
20:00:43.348 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'cn.iocoder.yudao.framework.lock4j.config.YudaoLock4jConfiguration' of type [cn.iocoder.yudao.framework.lock4j.config.YudaoLock4jConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:00:43.353 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'lockFailureStrategy' of type [cn.iocoder.yudao.framework.lock4j.core.DefaultLockFailureStrategy] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:00:43.380 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:00:43.389 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'stringOrNumberMigrationVersionConverter' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$StringOrNumberToMigrationVersionConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:00:44.176 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'cn.iocoder.yudao.framework.datapermission.config.YudaoDataPermissionAutoConfiguration' of type [cn.iocoder.yudao.framework.datapermission.config.YudaoDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:00:44.194 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'dataPermissionAnnotationAdvisor' of type [cn.iocoder.yudao.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:00:44.251 WARN  [elegate$BeanPostProcessorChecker:429] [] - Bean 'cn.iocoder.yudao.framework.quartz.config.YudaoAsyncAutoConfiguration' of type [cn.iocoder.yudao.framework.quartz.config.YudaoAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
20:00:44.263 WARN  [elegate$BeanPostProcessorChecker:429] [] - Bean 'cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration' of type [cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [tenantRocketMQInitializer] is declared through a non-static factory method on that class; consider declaring it as static instead.
20:00:44.299 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:00:44.308 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:00:44.438 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:00:44.450 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:00:44.463 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:00:45.730 INFO  [.embedded.tomcat.TomcatWebServer:111] [] - Tomcat initialized with port 48081 (http)
20:00:45.756 INFO  [.coyote.http11.Http11NioProtocol:173] [] - Initializing ProtocolHandler ["http-nio-48081"]
20:00:45.769 INFO  [he.catalina.core.StandardService:173] [] - Starting service [Tomcat]
20:00:45.770 INFO  [che.catalina.core.StandardEngine:173] [] - Starting Servlet engine: [Apache Tomcat/10.1.30]
20:00:45.986 INFO  [nerBase.[Tomcat].[localhost].[/]:173] [] - Initializing Spring embedded WebApplicationContext
20:00:45.986 INFO  [rvletWebServerApplicationContext:296] [] - Root WebApplicationContext: initialization completed in 9234 ms
20:00:48.948 INFO  [m.zaxxer.hikari.HikariDataSource:109] [] - HikariPool-1 - Starting...
20:00:49.961 INFO  [om.zaxxer.hikari.pool.HikariPool:554] [] - HikariPool-1 - Added connection org.sqlite.jdbc4.JDBC4Connection@314ecfa1
20:00:49.965 INFO  [m.zaxxer.hikari.HikariDataSource:122] [] - HikariPool-1 - Start completed.
20:00:49.999 INFO  [al.resource.ResourceNameValidator:37] [] - 3 SQL migrations were detected but not run because they did not follow the filename convention.
20:00:49.999 INFO  [al.resource.ResourceNameValidator:37] [] - Set 'validateMigrationNaming' to true to fail fast and see a list of the invalid file names.
20:00:50.009 INFO  [ org.flywaydb.core.FlywayExecutor:37] [] - Database: jdbc:sqlite:/Users/<USER>/codes/xyy/ls/db/saas-local.db (SQLite 3.45)
20:00:50.070 INFO  [b.core.internal.command.DbMigrate:37] [] - Current version of schema "main": << Empty Schema >>
20:00:50.076 INFO  [b.core.internal.command.DbMigrate:37] [] - Schema "main" is up to date. No migration necessary.
20:00:50.386 INFO  [MybatisPlusJoinAutoConfiguration:139] [] - mybatis plus join properties config complete
20:00:50.590 INFO  [MybatisPlusJoinAutoConfiguration:139] [] - mybatis plus join SqlInjector init
20:01:00.216 WARN  [rvletWebServerApplicationContext:633] [] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'easyExcelUtil': Injection of resource dependencies failed
20:01:00.222 INFO  [m.zaxxer.hikari.HikariDataSource:349] [] - HikariPool-1 - Shutdown initiated...
20:01:00.255 INFO  [m.zaxxer.hikari.HikariDataSource:351] [] - HikariPool-1 - Shutdown completed.
20:01:00.265 INFO  [he.catalina.core.StandardService:173] [] - Stopping service [Tomcat]
20:01:00.330 INFO  [g.ConditionEvaluationReportLogger:82] [] - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
20:01:00.390 ERROR [cs.LoggingFailureAnalysisReporter:40] [] - 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'cn.iocoder.yudao.module.infra.api.file.FileApi' that could not be found.


Action:

Consider defining a bean of type 'cn.iocoder.yudao.module.infra.api.file.FileApi' in your configuration.

20:03:18.291 INFO  [e.validator.internal.util.Version:21] [] - HV000001: Hibernate Validator 8.0.1.Final
20:03:18.435 INFO  [ocalserver.LocalserverApplication:50] [] - Starting LocalserverApplication using Java 21.0.3 with PID 28282 (/Users/<USER>/codes/xyy/ls/saas-localserver/saas-localserver-application/target/classes started by caojialin in /Users/<USER>/codes/xyy/ls)
20:03:18.436 DEBUG [ocalserver.LocalserverApplication:51] [] - Running with Spring Boot v3.3.4, Spring v6.1.13
20:03:18.438 INFO  [calserver.LocalserverApplication:660] [] - The following 1 profile is active: "test"
20:03:29.553 WARN  [NamingStrategy$SnakeCaseStrategy:207] [] - PropertyNamingStrategy.SnakeCaseStrategy is used but it has been deprecated due to risk of deadlock. Consider using PropertyNamingStrategies.SnakeCaseStrategy instead. See https://github.com/FasterXML/jackson-databind/issues/2715 for more details.
20:03:29.554 WARN  [gStrategy$UpperCamelCaseStrategy:207] [] - PropertyNamingStrategy.UpperCamelCaseStrategy is used but it has been deprecated due to risk of deadlock. Consider using PropertyNamingStrategies.UpperCamelCaseStrategy instead. See https://github.com/FasterXML/jackson-databind/issues/2715 for more details.
20:03:29.575 DEBUG [y.ClassPathDataSyncEntityScanner:116] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.localserver.entity.merchant.MedicareMerchant]
20:03:29.581 DEBUG [y.ClassPathDataSyncEntityScanner:116] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.localserver.entity.product.MedicareProduct]
20:03:29.583 INFO  [ty.ClassPathDataSyncEntityScanner:61] [] - 数据同步扫描basePackage:[[com.xyy.saas.localserver.entity]],扫描到@DataSyncEntity的class size:[2]
20:03:33.788 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'cn.iocoder.yudao.framework.lock4j.config.YudaoLock4jConfiguration' of type [cn.iocoder.yudao.framework.lock4j.config.YudaoLock4jConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:03:33.812 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'lockFailureStrategy' of type [cn.iocoder.yudao.framework.lock4j.core.DefaultLockFailureStrategy] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:03:33.869 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:03:33.887 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'stringOrNumberMigrationVersionConverter' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$StringOrNumberToMigrationVersionConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:03:34.875 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'cn.iocoder.yudao.framework.datapermission.config.YudaoDataPermissionAutoConfiguration' of type [cn.iocoder.yudao.framework.datapermission.config.YudaoDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:03:34.886 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'dataPermissionAnnotationAdvisor' of type [cn.iocoder.yudao.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:03:34.936 WARN  [elegate$BeanPostProcessorChecker:429] [] - Bean 'cn.iocoder.yudao.framework.quartz.config.YudaoAsyncAutoConfiguration' of type [cn.iocoder.yudao.framework.quartz.config.YudaoAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
20:03:34.951 WARN  [elegate$BeanPostProcessorChecker:429] [] - Bean 'cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration' of type [cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [tenantRocketMQInitializer] is declared through a non-static factory method on that class; consider declaring it as static instead.
20:03:34.971 INFO  [ocketmq.TenantRocketMQInitializer:46] [] - 【多环境】TenantRocketMQInitializer 初始化成功！
20:03:35.049 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:03:35.077 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:03:35.355 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:03:35.437 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:03:35.458 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:03:35.514 INFO  [ocketmq.TenantRocketMQInitializer:46] [] - 【多环境】TenantRocketMQInitializer 初始化成功！
20:03:37.574 INFO  [.embedded.tomcat.TomcatWebServer:111] [] - Tomcat initialized with port 48081 (http)
20:03:37.622 INFO  [.coyote.http11.Http11NioProtocol:173] [] - Initializing ProtocolHandler ["http-nio-48081"]
20:03:37.634 INFO  [he.catalina.core.StandardService:173] [] - Starting service [Tomcat]
20:03:37.634 INFO  [che.catalina.core.StandardEngine:173] [] - Starting Servlet engine: [Apache Tomcat/10.1.30]
20:03:37.987 INFO  [nerBase.[Tomcat].[localhost].[/]:173] [] - Initializing Spring embedded WebApplicationContext
20:03:37.988 INFO  [rvletWebServerApplicationContext:296] [] - Root WebApplicationContext: initialization completed in 19428 ms
20:03:40.906 WARN  [rvletWebServerApplicationContext:633] [] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'easyExcelUtil': Injection of resource dependencies failed
20:03:40.913 INFO  [he.catalina.core.StandardService:173] [] - Stopping service [Tomcat]
20:03:41.155 INFO  [g.ConditionEvaluationReportLogger:82] [] - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
20:03:41.201 ERROR [cs.LoggingFailureAnalysisReporter:40] [] - 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'cn.iocoder.yudao.module.infra.api.file.FileApi' that could not be found.


Action:

Consider defining a bean of type 'cn.iocoder.yudao.module.infra.api.file.FileApi' in your configuration.

20:05:38.132 INFO  [e.validator.internal.util.Version:21] [] - HV000001: Hibernate Validator 8.0.1.Final
20:05:38.268 INFO  [ocalserver.LocalserverApplication:50] [] - Starting LocalserverApplication using Java 21.0.3 with PID 28407 (/Users/<USER>/codes/xyy/ls/saas-localserver/saas-localserver-application/target/classes started by caojialin in /Users/<USER>/codes/xyy/ls)
20:05:38.269 DEBUG [ocalserver.LocalserverApplication:51] [] - Running with Spring Boot v3.3.4, Spring v6.1.13
20:05:38.270 INFO  [calserver.LocalserverApplication:660] [] - The following 1 profile is active: "test"
20:05:42.284 WARN  [NamingStrategy$SnakeCaseStrategy:207] [] - PropertyNamingStrategy.SnakeCaseStrategy is used but it has been deprecated due to risk of deadlock. Consider using PropertyNamingStrategies.SnakeCaseStrategy instead. See https://github.com/FasterXML/jackson-databind/issues/2715 for more details.
20:05:42.285 WARN  [gStrategy$UpperCamelCaseStrategy:207] [] - PropertyNamingStrategy.UpperCamelCaseStrategy is used but it has been deprecated due to risk of deadlock. Consider using PropertyNamingStrategies.UpperCamelCaseStrategy instead. See https://github.com/FasterXML/jackson-databind/issues/2715 for more details.
20:05:42.303 DEBUG [y.ClassPathDataSyncEntityScanner:116] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.localserver.entity.merchant.MedicareMerchant]
20:05:42.308 DEBUG [y.ClassPathDataSyncEntityScanner:116] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.localserver.entity.product.MedicareProduct]
20:05:42.310 INFO  [ty.ClassPathDataSyncEntityScanner:61] [] - 数据同步扫描basePackage:[[com.xyy.saas.localserver.entity]],扫描到@DataSyncEntity的class size:[2]
20:05:45.152 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'cn.iocoder.yudao.framework.lock4j.config.YudaoLock4jConfiguration' of type [cn.iocoder.yudao.framework.lock4j.config.YudaoLock4jConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:05:45.158 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'lockFailureStrategy' of type [cn.iocoder.yudao.framework.lock4j.core.DefaultLockFailureStrategy] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:05:45.190 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:05:45.201 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'stringOrNumberMigrationVersionConverter' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$StringOrNumberToMigrationVersionConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:05:45.866 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'cn.iocoder.yudao.framework.datapermission.config.YudaoDataPermissionAutoConfiguration' of type [cn.iocoder.yudao.framework.datapermission.config.YudaoDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:05:45.877 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'dataPermissionAnnotationAdvisor' of type [cn.iocoder.yudao.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:05:45.928 WARN  [elegate$BeanPostProcessorChecker:429] [] - Bean 'cn.iocoder.yudao.framework.quartz.config.YudaoAsyncAutoConfiguration' of type [cn.iocoder.yudao.framework.quartz.config.YudaoAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
20:05:45.940 WARN  [elegate$BeanPostProcessorChecker:429] [] - Bean 'cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration' of type [cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [tenantRocketMQInitializer] is declared through a non-static factory method on that class; consider declaring it as static instead.
20:05:45.956 INFO  [ocketmq.TenantRocketMQInitializer:46] [] - 【多环境】TenantRocketMQInitializer 初始化成功！
20:05:45.991 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:05:46.018 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:05:46.139 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:05:46.153 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:05:46.164 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:05:46.192 INFO  [ocketmq.TenantRocketMQInitializer:46] [] - 【多环境】TenantRocketMQInitializer 初始化成功！
20:05:47.533 INFO  [.embedded.tomcat.TomcatWebServer:111] [] - Tomcat initialized with port 48081 (http)
20:05:47.556 INFO  [.coyote.http11.Http11NioProtocol:173] [] - Initializing ProtocolHandler ["http-nio-48081"]
20:05:47.566 INFO  [he.catalina.core.StandardService:173] [] - Starting service [Tomcat]
20:05:47.566 INFO  [che.catalina.core.StandardEngine:173] [] - Starting Servlet engine: [Apache Tomcat/10.1.30]
20:05:47.721 INFO  [nerBase.[Tomcat].[localhost].[/]:173] [] - Initializing Spring embedded WebApplicationContext
20:05:47.722 INFO  [rvletWebServerApplicationContext:296] [] - Root WebApplicationContext: initialization completed in 9318 ms
20:05:49.766 WARN  [rvletWebServerApplicationContext:633] [] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'easyExcelUtil': Injection of resource dependencies failed
20:05:49.791 INFO  [he.catalina.core.StandardService:173] [] - Stopping service [Tomcat]
20:05:50.039 INFO  [g.ConditionEvaluationReportLogger:82] [] - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
20:05:50.079 ERROR [cs.LoggingFailureAnalysisReporter:40] [] - 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'cn.iocoder.yudao.module.infra.api.file.FileApi' that could not be found.


Action:

Consider defining a bean of type 'cn.iocoder.yudao.module.infra.api.file.FileApi' in your configuration.

20:06:06.291 INFO  [e.validator.internal.util.Version:21] [] - HV000001: Hibernate Validator 8.0.1.Final
20:06:06.460 INFO  [ocalserver.LocalserverApplication:50] [] - Starting LocalserverApplication using Java 21.0.3 with PID 28430 (/Users/<USER>/codes/xyy/ls/saas-localserver/saas-localserver-application/target/classes started by caojialin in /Users/<USER>/codes/xyy/ls)
20:06:06.462 DEBUG [ocalserver.LocalserverApplication:51] [] - Running with Spring Boot v3.3.4, Spring v6.1.13
20:06:06.463 INFO  [calserver.LocalserverApplication:660] [] - The following 2 profiles are active: "test", "local"
20:06:12.711 WARN  [NamingStrategy$SnakeCaseStrategy:207] [] - PropertyNamingStrategy.SnakeCaseStrategy is used but it has been deprecated due to risk of deadlock. Consider using PropertyNamingStrategies.SnakeCaseStrategy instead. See https://github.com/FasterXML/jackson-databind/issues/2715 for more details.
20:06:12.712 WARN  [gStrategy$UpperCamelCaseStrategy:207] [] - PropertyNamingStrategy.UpperCamelCaseStrategy is used but it has been deprecated due to risk of deadlock. Consider using PropertyNamingStrategies.UpperCamelCaseStrategy instead. See https://github.com/FasterXML/jackson-databind/issues/2715 for more details.
20:06:12.729 DEBUG [y.ClassPathDataSyncEntityScanner:116] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.localserver.entity.merchant.MedicareMerchant]
20:06:12.734 DEBUG [y.ClassPathDataSyncEntityScanner:116] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.localserver.entity.product.MedicareProduct]
20:06:12.736 INFO  [ty.ClassPathDataSyncEntityScanner:61] [] - 数据同步扫描basePackage:[[com.xyy.saas.localserver.entity]],扫描到@DataSyncEntity的class size:[2]
20:06:16.131 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'cn.iocoder.yudao.framework.lock4j.config.YudaoLock4jConfiguration' of type [cn.iocoder.yudao.framework.lock4j.config.YudaoLock4jConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:06:16.137 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'lockFailureStrategy' of type [cn.iocoder.yudao.framework.lock4j.core.DefaultLockFailureStrategy] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:06:16.171 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:06:16.192 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'stringOrNumberMigrationVersionConverter' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$StringOrNumberToMigrationVersionConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:06:16.875 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'cn.iocoder.yudao.framework.datapermission.config.YudaoDataPermissionAutoConfiguration' of type [cn.iocoder.yudao.framework.datapermission.config.YudaoDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:06:16.883 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'dataPermissionAnnotationAdvisor' of type [cn.iocoder.yudao.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:06:16.929 WARN  [elegate$BeanPostProcessorChecker:429] [] - Bean 'cn.iocoder.yudao.framework.quartz.config.YudaoAsyncAutoConfiguration' of type [cn.iocoder.yudao.framework.quartz.config.YudaoAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
20:06:16.938 WARN  [elegate$BeanPostProcessorChecker:429] [] - Bean 'cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration' of type [cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [tenantRocketMQInitializer] is declared through a non-static factory method on that class; consider declaring it as static instead.
20:06:16.949 INFO  [ocketmq.TenantRocketMQInitializer:46] [] - 【多环境】TenantRocketMQInitializer 初始化成功！
20:06:16.981 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:06:16.990 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:06:17.120 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:06:17.139 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:06:17.154 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:06:18.358 INFO  [.embedded.tomcat.TomcatWebServer:111] [] - Tomcat initialized with port 48081 (http)
20:06:18.381 INFO  [.coyote.http11.Http11NioProtocol:173] [] - Initializing ProtocolHandler ["http-nio-48081"]
20:06:18.390 INFO  [he.catalina.core.StandardService:173] [] - Starting service [Tomcat]
20:06:18.390 INFO  [che.catalina.core.StandardEngine:173] [] - Starting Servlet engine: [Apache Tomcat/10.1.30]
20:06:18.554 INFO  [nerBase.[Tomcat].[localhost].[/]:173] [] - Initializing Spring embedded WebApplicationContext
20:06:18.554 INFO  [rvletWebServerApplicationContext:296] [] - Root WebApplicationContext: initialization completed in 11927 ms
20:06:20.883 WARN  [rvletWebServerApplicationContext:633] [] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'easyExcelUtil': Injection of resource dependencies failed
20:06:20.889 INFO  [he.catalina.core.StandardService:173] [] - Stopping service [Tomcat]
20:06:21.225 INFO  [g.ConditionEvaluationReportLogger:82] [] - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
20:06:21.306 ERROR [cs.LoggingFailureAnalysisReporter:40] [] - 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'cn.iocoder.yudao.module.infra.api.file.FileApi' that could not be found.


Action:

Consider defining a bean of type 'cn.iocoder.yudao.module.infra.api.file.FileApi' in your configuration.

20:08:50.712 INFO  [e.validator.internal.util.Version:21] [] - HV000001: Hibernate Validator 8.0.1.Final
20:08:51.189 INFO  [ocalserver.LocalserverApplication:50] [] - Starting LocalserverApplication using Java 21.0.3 with PID 28588 (/Users/<USER>/codes/xyy/ls/saas-localserver/saas-localserver-application/target/classes started by caojialin in /Users/<USER>/codes/xyy/ls)
20:08:51.191 DEBUG [ocalserver.LocalserverApplication:51] [] - Running with Spring Boot v3.3.4, Spring v6.1.13
20:08:51.196 INFO  [calserver.LocalserverApplication:660] [] - The following 2 profiles are active: "test", "local"
20:09:05.543 WARN  [NamingStrategy$SnakeCaseStrategy:207] [] - PropertyNamingStrategy.SnakeCaseStrategy is used but it has been deprecated due to risk of deadlock. Consider using PropertyNamingStrategies.SnakeCaseStrategy instead. See https://github.com/FasterXML/jackson-databind/issues/2715 for more details.
20:09:05.544 WARN  [gStrategy$UpperCamelCaseStrategy:207] [] - PropertyNamingStrategy.UpperCamelCaseStrategy is used but it has been deprecated due to risk of deadlock. Consider using PropertyNamingStrategies.UpperCamelCaseStrategy instead. See https://github.com/FasterXML/jackson-databind/issues/2715 for more details.
20:09:05.556 DEBUG [y.ClassPathDataSyncEntityScanner:116] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.localserver.entity.merchant.MedicareMerchant]
20:09:05.561 DEBUG [y.ClassPathDataSyncEntityScanner:116] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.localserver.entity.product.MedicareProduct]
20:09:05.563 INFO  [ty.ClassPathDataSyncEntityScanner:61] [] - 数据同步扫描basePackage:[[com.xyy.saas.localserver.entity]],扫描到@DataSyncEntity的class size:[2]
20:09:09.194 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'cn.iocoder.yudao.framework.lock4j.config.YudaoLock4jConfiguration' of type [cn.iocoder.yudao.framework.lock4j.config.YudaoLock4jConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:09:09.200 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'lockFailureStrategy' of type [cn.iocoder.yudao.framework.lock4j.core.DefaultLockFailureStrategy] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:09:09.242 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:09:09.254 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'stringOrNumberMigrationVersionConverter' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$StringOrNumberToMigrationVersionConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:09:09.866 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'cn.iocoder.yudao.framework.datapermission.config.YudaoDataPermissionAutoConfiguration' of type [cn.iocoder.yudao.framework.datapermission.config.YudaoDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:09:09.874 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'dataPermissionAnnotationAdvisor' of type [cn.iocoder.yudao.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:09:09.923 WARN  [elegate$BeanPostProcessorChecker:429] [] - Bean 'cn.iocoder.yudao.framework.quartz.config.YudaoAsyncAutoConfiguration' of type [cn.iocoder.yudao.framework.quartz.config.YudaoAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
20:09:09.931 WARN  [elegate$BeanPostProcessorChecker:429] [] - Bean 'cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration' of type [cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [tenantRocketMQInitializer] is declared through a non-static factory method on that class; consider declaring it as static instead.
20:09:09.940 INFO  [ocketmq.TenantRocketMQInitializer:46] [] - 【多环境】TenantRocketMQInitializer 初始化成功！
20:09:09.968 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:09:09.978 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:09:10.080 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:09:10.091 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:09:10.101 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:09:11.140 INFO  [.embedded.tomcat.TomcatWebServer:111] [] - Tomcat initialized with port 48081 (http)
20:09:11.168 INFO  [.coyote.http11.Http11NioProtocol:173] [] - Initializing ProtocolHandler ["http-nio-48081"]
20:09:11.178 INFO  [he.catalina.core.StandardService:173] [] - Starting service [Tomcat]
20:09:11.179 INFO  [che.catalina.core.StandardEngine:173] [] - Starting Servlet engine: [Apache Tomcat/10.1.30]
20:09:11.329 INFO  [nerBase.[Tomcat].[localhost].[/]:173] [] - Initializing Spring embedded WebApplicationContext
20:09:11.330 INFO  [rvletWebServerApplicationContext:296] [] - Root WebApplicationContext: initialization completed in 19854 ms
20:09:13.433 INFO  [m.zaxxer.hikari.HikariDataSource:109] [] - HikariPool-1 - Starting...
20:09:14.299 INFO  [om.zaxxer.hikari.pool.HikariPool:554] [] - HikariPool-1 - Added connection org.sqlite.jdbc4.JDBC4Connection@74d3c448
20:09:14.304 INFO  [m.zaxxer.hikari.HikariDataSource:122] [] - HikariPool-1 - Start completed.
20:09:14.348 INFO  [al.resource.ResourceNameValidator:37] [] - 3 SQL migrations were detected but not run because they did not follow the filename convention.
20:09:14.349 INFO  [al.resource.ResourceNameValidator:37] [] - Set 'validateMigrationNaming' to true to fail fast and see a list of the invalid file names.
20:09:14.364 INFO  [ org.flywaydb.core.FlywayExecutor:37] [] - Database: jdbc:sqlite:/Users/<USER>/codes/xyy/ls/db/saas-local.db (SQLite 3.45)
20:09:14.448 INFO  [b.core.internal.command.DbMigrate:37] [] - Current version of schema "main": << Empty Schema >>
20:09:14.453 INFO  [b.core.internal.command.DbMigrate:37] [] - Schema "main" is up to date. No migration necessary.
20:09:14.759 INFO  [MybatisPlusJoinAutoConfiguration:139] [] - mybatis plus join properties config complete
20:09:14.966 INFO  [MybatisPlusJoinAutoConfiguration:139] [] - mybatis plus join SqlInjector init
20:09:25.873 INFO  [y.EventRegistryAutoConfiguration:104] [] - No deployment resources were found for autodeployment
20:09:26.833 INFO  [t.ProcessEngineAutoConfiguration:104] [] - No deployment resources were found for autodeployment
20:09:28.404 INFO  [pringProcessEngineConfiguration:1086] [] - Found 2 Engine Configurators in total:
20:09:28.405 INFO  [pringProcessEngineConfiguration:1088] [] - class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
20:09:28.405 INFO  [pringProcessEngineConfiguration:1088] [] - class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
20:09:28.406 INFO  [pringProcessEngineConfiguration:1114] [] - Executing beforeInit() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
20:09:28.411 INFO  [pringProcessEngineConfiguration:1114] [] - Executing beforeInit() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
20:09:28.503 WARN  [rvletWebServerApplicationContext:633] [] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'appProductPresentController': Injection of resource dependencies failed
20:09:28.513 INFO  [m.zaxxer.hikari.HikariDataSource:349] [] - HikariPool-1 - Shutdown initiated...
20:09:28.517 INFO  [m.zaxxer.hikari.HikariDataSource:351] [] - HikariPool-1 - Shutdown completed.
20:09:28.522 INFO  [he.catalina.core.StandardService:173] [] - Stopping service [Tomcat]
20:09:28.563 INFO  [g.ConditionEvaluationReportLogger:82] [] - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
20:09:28.630 ERROR [framework.boot.SpringApplication:859] [] - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'appProductPresentController': Injection of resource dependencies failed
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:371)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.xyy.saas.localserver.LocalserverApplication.main(LocalserverApplication.java:22)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'productInfoServiceImpl': Injection of resource dependencies failed
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:371)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	... 17 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'bpmBusinessRelationServiceImpl': Injection of resource dependencies failed
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:371)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	... 33 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'bpmProcessInstanceApiImpl': Injection of resource dependencies failed
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:371)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	... 49 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'bpmProcessInstanceServiceImpl': Injection of resource dependencies failed
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:371)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	... 65 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'runtimeServiceBean' defined in class path resource [org/flowable/spring/boot/ProcessEngineServicesAutoConfiguration.class]: Unsatisfied dependency expressed through method 'runtimeServiceBean' parameter 0: Error creating bean with name 'processEngine': FactoryBean threw exception on object creation
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:542)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1185)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	... 81 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'processEngine': FactoryBean threw exception on object creation
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.doGetObjectFromFactoryBean(FactoryBeanRegistrySupport.java:188)
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.getObjectFromFactoryBean(FactoryBeanRegistrySupport.java:124)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getObjectForBeanInstance(AbstractBeanFactory.java:1867)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getObjectForBeanInstance(AbstractAutowireCapableBeanFactory.java:1296)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	... 99 common frames omitted
Caused by: org.flowable.common.engine.api.FlowableException: couldn't deduct database type from database product name 'SQLite'
	at org.flowable.common.engine.impl.AbstractEngineConfiguration.initDatabaseType(AbstractEngineConfiguration.java:523)
	at org.flowable.common.engine.impl.AbstractEngineConfiguration.initDataSource(AbstractEngineConfiguration.java:493)
	at org.flowable.engine.impl.cfg.ProcessEngineConfigurationImpl.init(ProcessEngineConfigurationImpl.java:921)
	at org.flowable.engine.impl.cfg.ProcessEngineConfigurationImpl.buildProcessEngine(ProcessEngineConfigurationImpl.java:873)
	at org.flowable.spring.SpringProcessEngineConfiguration.buildProcessEngine(SpringProcessEngineConfiguration.java:76)
	at org.flowable.spring.ProcessEngineFactoryBean.getObject(ProcessEngineFactoryBean.java:59)
	at org.flowable.spring.ProcessEngineFactoryBean.getObject(ProcessEngineFactoryBean.java:32)
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.doGetObjectFromFactoryBean(FactoryBeanRegistrySupport.java:182)
	... 109 common frames omitted
20:49:12.389 INFO  [e.validator.internal.util.Version:21] [] - HV000001: Hibernate Validator 8.0.1.Final
20:49:12.506 INFO  [ocalserver.LocalserverApplication:50] [] - Starting LocalserverApplication using Java 21.0.3 with PID 31708 (/Users/<USER>/codes/xyy/ls/saas-localserver/saas-localserver-application/target/classes started by caojialin in /Users/<USER>/codes/xyy/ls)
20:49:12.506 DEBUG [ocalserver.LocalserverApplication:51] [] - Running with Spring Boot v3.3.4, Spring v6.1.13
20:49:12.507 INFO  [calserver.LocalserverApplication:660] [] - The following 2 profiles are active: "test", "local"
20:49:16.296 WARN  [NamingStrategy$SnakeCaseStrategy:207] [] - PropertyNamingStrategy.SnakeCaseStrategy is used but it has been deprecated due to risk of deadlock. Consider using PropertyNamingStrategies.SnakeCaseStrategy instead. See https://github.com/FasterXML/jackson-databind/issues/2715 for more details.
20:49:16.297 WARN  [gStrategy$UpperCamelCaseStrategy:207] [] - PropertyNamingStrategy.UpperCamelCaseStrategy is used but it has been deprecated due to risk of deadlock. Consider using PropertyNamingStrategies.UpperCamelCaseStrategy instead. See https://github.com/FasterXML/jackson-databind/issues/2715 for more details.
20:49:16.306 DEBUG [y.ClassPathDataSyncEntityScanner:116] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.localserver.entity.merchant.MedicareMerchant]
20:49:16.310 DEBUG [y.ClassPathDataSyncEntityScanner:116] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.localserver.entity.product.MedicareProduct]
20:49:16.312 INFO  [ty.ClassPathDataSyncEntityScanner:61] [] - 数据同步扫描basePackage:[[com.xyy.saas.localserver.entity]],扫描到@DataSyncEntity的class size:[2]
20:49:18.714 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'cn.iocoder.yudao.framework.lock4j.config.YudaoLock4jConfiguration' of type [cn.iocoder.yudao.framework.lock4j.config.YudaoLock4jConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:49:18.718 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'lockFailureStrategy' of type [cn.iocoder.yudao.framework.lock4j.core.DefaultLockFailureStrategy] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:49:18.741 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:49:18.749 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'stringOrNumberMigrationVersionConverter' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$StringOrNumberToMigrationVersionConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:49:19.303 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'cn.iocoder.yudao.framework.datapermission.config.YudaoDataPermissionAutoConfiguration' of type [cn.iocoder.yudao.framework.datapermission.config.YudaoDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:49:19.309 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'dataPermissionAnnotationAdvisor' of type [cn.iocoder.yudao.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:49:19.341 WARN  [elegate$BeanPostProcessorChecker:429] [] - Bean 'cn.iocoder.yudao.framework.quartz.config.YudaoAsyncAutoConfiguration' of type [cn.iocoder.yudao.framework.quartz.config.YudaoAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
20:49:19.348 WARN  [elegate$BeanPostProcessorChecker:429] [] - Bean 'cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration' of type [cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [tenantRocketMQInitializer] is declared through a non-static factory method on that class; consider declaring it as static instead.
20:49:19.357 INFO  [ocketmq.TenantRocketMQInitializer:46] [] - 【多环境】TenantRocketMQInitializer 初始化成功！
20:49:19.380 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:49:19.387 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:49:19.466 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:49:19.476 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:49:19.483 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:49:20.239 INFO  [.embedded.tomcat.TomcatWebServer:111] [] - Tomcat initialized with port 48081 (http)
20:49:20.256 INFO  [.coyote.http11.Http11NioProtocol:173] [] - Initializing ProtocolHandler ["http-nio-48081"]
20:49:20.263 INFO  [he.catalina.core.StandardService:173] [] - Starting service [Tomcat]
20:49:20.264 INFO  [che.catalina.core.StandardEngine:173] [] - Starting Servlet engine: [Apache Tomcat/10.1.30]
20:49:20.371 INFO  [nerBase.[Tomcat].[localhost].[/]:173] [] - Initializing Spring embedded WebApplicationContext
20:49:20.371 INFO  [rvletWebServerApplicationContext:296] [] - Root WebApplicationContext: initialization completed in 7791 ms
20:49:22.061 INFO  [m.zaxxer.hikari.HikariDataSource:109] [] - HikariPool-1 - Starting...
20:49:25.421 INFO  [om.zaxxer.hikari.pool.HikariPool:554] [] - HikariPool-1 - Added connection org.sqlite.jdbc4.JDBC4Connection@26647ac8
20:49:25.426 INFO  [m.zaxxer.hikari.HikariDataSource:122] [] - HikariPool-1 - Start completed.
20:49:25.461 INFO  [al.resource.ResourceNameValidator:37] [] - 3 SQL migrations were detected but not run because they did not follow the filename convention.
20:49:25.461 INFO  [al.resource.ResourceNameValidator:37] [] - Set 'validateMigrationNaming' to true to fail fast and see a list of the invalid file names.
20:49:25.472 INFO  [ org.flywaydb.core.FlywayExecutor:37] [] - Database: jdbc:sqlite:/Users/<USER>/codes/xyy/ls/db/saas-local.db (SQLite 3.45)
20:49:25.565 INFO  [b.core.internal.command.DbMigrate:37] [] - Current version of schema "main": << Empty Schema >>
20:49:25.571 INFO  [b.core.internal.command.DbMigrate:37] [] - Schema "main" is up to date. No migration necessary.
20:49:25.917 INFO  [MybatisPlusJoinAutoConfiguration:139] [] - mybatis plus join properties config complete
20:49:26.079 INFO  [MybatisPlusJoinAutoConfiguration:139] [] - mybatis plus join SqlInjector init
20:49:34.789 INFO  [y.EventRegistryAutoConfiguration:104] [] - No deployment resources were found for autodeployment
20:49:35.565 INFO  [t.ProcessEngineAutoConfiguration:104] [] - No deployment resources were found for autodeployment
20:49:36.834 INFO  [pringProcessEngineConfiguration:1086] [] - Found 2 Engine Configurators in total:
20:49:36.834 INFO  [pringProcessEngineConfiguration:1088] [] - class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
20:49:36.835 INFO  [pringProcessEngineConfiguration:1088] [] - class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
20:49:36.835 INFO  [pringProcessEngineConfiguration:1114] [] - Executing beforeInit() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
20:49:36.838 INFO  [pringProcessEngineConfiguration:1114] [] - Executing beforeInit() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
20:49:36.914 WARN  [rvletWebServerApplicationContext:633] [] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'appProductPresentController': Injection of resource dependencies failed
20:49:36.922 INFO  [m.zaxxer.hikari.HikariDataSource:349] [] - HikariPool-1 - Shutdown initiated...
20:49:36.926 INFO  [m.zaxxer.hikari.HikariDataSource:351] [] - HikariPool-1 - Shutdown completed.
20:49:36.932 INFO  [he.catalina.core.StandardService:173] [] - Stopping service [Tomcat]
20:49:36.978 INFO  [g.ConditionEvaluationReportLogger:82] [] - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
20:49:37.013 ERROR [framework.boot.SpringApplication:859] [] - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'appProductPresentController': Injection of resource dependencies failed
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:371)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.xyy.saas.localserver.LocalserverApplication.main(LocalserverApplication.java:22)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'productInfoServiceImpl': Injection of resource dependencies failed
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:371)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	... 17 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'bpmBusinessRelationServiceImpl': Injection of resource dependencies failed
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:371)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	... 33 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'bpmProcessInstanceApiImpl': Injection of resource dependencies failed
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:371)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	... 49 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'bpmProcessInstanceServiceImpl': Injection of resource dependencies failed
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:371)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	... 65 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'runtimeServiceBean' defined in class path resource [org/flowable/spring/boot/ProcessEngineServicesAutoConfiguration.class]: Unsatisfied dependency expressed through method 'runtimeServiceBean' parameter 0: Error creating bean with name 'processEngine': FactoryBean threw exception on object creation
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:542)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1185)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	... 81 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'processEngine': FactoryBean threw exception on object creation
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.doGetObjectFromFactoryBean(FactoryBeanRegistrySupport.java:188)
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.getObjectFromFactoryBean(FactoryBeanRegistrySupport.java:124)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getObjectForBeanInstance(AbstractBeanFactory.java:1867)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getObjectForBeanInstance(AbstractAutowireCapableBeanFactory.java:1296)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	... 99 common frames omitted
Caused by: org.flowable.common.engine.api.FlowableException: couldn't deduct database type from database product name 'SQLite'
	at org.flowable.common.engine.impl.AbstractEngineConfiguration.initDatabaseType(AbstractEngineConfiguration.java:523)
	at org.flowable.common.engine.impl.AbstractEngineConfiguration.initDataSource(AbstractEngineConfiguration.java:493)
	at org.flowable.engine.impl.cfg.ProcessEngineConfigurationImpl.init(ProcessEngineConfigurationImpl.java:921)
	at org.flowable.engine.impl.cfg.ProcessEngineConfigurationImpl.buildProcessEngine(ProcessEngineConfigurationImpl.java:873)
	at org.flowable.spring.SpringProcessEngineConfiguration.buildProcessEngine(SpringProcessEngineConfiguration.java:76)
	at org.flowable.spring.ProcessEngineFactoryBean.getObject(ProcessEngineFactoryBean.java:59)
	at org.flowable.spring.ProcessEngineFactoryBean.getObject(ProcessEngineFactoryBean.java:32)
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.doGetObjectFromFactoryBean(FactoryBeanRegistrySupport.java:182)
	... 109 common frames omitted
20:52:06.075 INFO  [e.validator.internal.util.Version:21] [] - HV000001: Hibernate Validator 8.0.1.Final
20:52:06.190 INFO  [ocalserver.LocalserverApplication:50] [] - Starting LocalserverApplication using Java 21.0.3 with PID 31840 (/Users/<USER>/codes/xyy/ls/saas-localserver/saas-localserver-application/target/classes started by caojialin in /Users/<USER>/codes/xyy/ls)
20:52:06.190 DEBUG [ocalserver.LocalserverApplication:51] [] - Running with Spring Boot v3.3.4, Spring v6.1.13
20:52:06.192 INFO  [calserver.LocalserverApplication:660] [] - The following 2 profiles are active: "test", "local"
20:52:10.060 WARN  [NamingStrategy$SnakeCaseStrategy:207] [] - PropertyNamingStrategy.SnakeCaseStrategy is used but it has been deprecated due to risk of deadlock. Consider using PropertyNamingStrategies.SnakeCaseStrategy instead. See https://github.com/FasterXML/jackson-databind/issues/2715 for more details.
20:52:10.061 WARN  [gStrategy$UpperCamelCaseStrategy:207] [] - PropertyNamingStrategy.UpperCamelCaseStrategy is used but it has been deprecated due to risk of deadlock. Consider using PropertyNamingStrategies.UpperCamelCaseStrategy instead. See https://github.com/FasterXML/jackson-databind/issues/2715 for more details.
20:52:10.070 DEBUG [y.ClassPathDataSyncEntityScanner:116] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.localserver.entity.merchant.MedicareMerchant]
20:52:10.073 DEBUG [y.ClassPathDataSyncEntityScanner:116] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.localserver.entity.product.MedicareProduct]
20:52:10.074 INFO  [ty.ClassPathDataSyncEntityScanner:61] [] - 数据同步扫描basePackage:[[com.xyy.saas.localserver.entity]],扫描到@DataSyncEntity的class size:[2]
20:52:12.174 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'cn.iocoder.yudao.framework.lock4j.config.YudaoLock4jConfiguration' of type [cn.iocoder.yudao.framework.lock4j.config.YudaoLock4jConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:52:12.178 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'lockFailureStrategy' of type [cn.iocoder.yudao.framework.lock4j.core.DefaultLockFailureStrategy] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:52:12.200 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:52:12.206 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'stringOrNumberMigrationVersionConverter' of type [org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration$StringOrNumberToMigrationVersionConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:52:12.726 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'cn.iocoder.yudao.framework.datapermission.config.YudaoDataPermissionAutoConfiguration' of type [cn.iocoder.yudao.framework.datapermission.config.YudaoDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:52:12.732 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'dataPermissionAnnotationAdvisor' of type [cn.iocoder.yudao.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:52:12.763 WARN  [elegate$BeanPostProcessorChecker:429] [] - Bean 'cn.iocoder.yudao.framework.quartz.config.YudaoAsyncAutoConfiguration' of type [cn.iocoder.yudao.framework.quartz.config.YudaoAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
20:52:12.768 WARN  [elegate$BeanPostProcessorChecker:429] [] - Bean 'cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration' of type [cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [tenantRocketMQInitializer] is declared through a non-static factory method on that class; consider declaring it as static instead.
20:52:12.775 INFO  [ocketmq.TenantRocketMQInitializer:46] [] - 【多环境】TenantRocketMQInitializer 初始化成功！
20:52:12.796 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:52:12.801 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:52:12.881 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:52:12.890 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:52:12.897 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
20:52:13.652 INFO  [.embedded.tomcat.TomcatWebServer:111] [] - Tomcat initialized with port 48081 (http)
20:52:13.672 INFO  [.coyote.http11.Http11NioProtocol:173] [] - Initializing ProtocolHandler ["http-nio-48081"]
20:52:13.678 INFO  [he.catalina.core.StandardService:173] [] - Starting service [Tomcat]
20:52:13.678 INFO  [che.catalina.core.StandardEngine:173] [] - Starting Servlet engine: [Apache Tomcat/10.1.30]
20:52:13.792 INFO  [nerBase.[Tomcat].[localhost].[/]:173] [] - Initializing Spring embedded WebApplicationContext
20:52:13.792 INFO  [rvletWebServerApplicationContext:296] [] - Root WebApplicationContext: initialization completed in 7519 ms
20:52:15.513 INFO  [m.zaxxer.hikari.HikariDataSource:109] [] - HikariPool-1 - Starting...
20:52:16.193 INFO  [om.zaxxer.hikari.pool.HikariPool:554] [] - HikariPool-1 - Added connection org.sqlite.jdbc4.JDBC4Connection@40f6e092
20:52:16.197 INFO  [m.zaxxer.hikari.HikariDataSource:122] [] - HikariPool-1 - Start completed.
20:52:16.218 INFO  [al.resource.ResourceNameValidator:37] [] - 3 SQL migrations were detected but not run because they did not follow the filename convention.
20:52:16.219 INFO  [al.resource.ResourceNameValidator:37] [] - Set 'validateMigrationNaming' to true to fail fast and see a list of the invalid file names.
20:52:16.227 INFO  [ org.flywaydb.core.FlywayExecutor:37] [] - Database: jdbc:sqlite:/Users/<USER>/codes/xyy/ls/db/saas-local.db (SQLite 3.45)
20:52:16.275 INFO  [b.core.internal.command.DbMigrate:37] [] - Current version of schema "main": << Empty Schema >>
20:52:16.279 INFO  [b.core.internal.command.DbMigrate:37] [] - Schema "main" is up to date. No migration necessary.
20:52:16.629 INFO  [MybatisPlusJoinAutoConfiguration:139] [] - mybatis plus join properties config complete
20:52:16.845 INFO  [MybatisPlusJoinAutoConfiguration:139] [] - mybatis plus join SqlInjector init
20:52:25.953 INFO  [y.EventRegistryAutoConfiguration:104] [] - No deployment resources were found for autodeployment
20:52:26.697 INFO  [t.ProcessEngineAutoConfiguration:104] [] - No deployment resources were found for autodeployment
20:52:27.722 INFO  [pringProcessEngineConfiguration:1086] [] - Found 2 Engine Configurators in total:
20:52:27.723 INFO  [pringProcessEngineConfiguration:1088] [] - class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
20:52:27.723 INFO  [pringProcessEngineConfiguration:1088] [] - class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
20:52:27.723 INFO  [pringProcessEngineConfiguration:1114] [] - Executing beforeInit() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
20:52:27.727 INFO  [pringProcessEngineConfiguration:1114] [] - Executing beforeInit() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
20:53:06.054 INFO  [pringProcessEngineConfiguration:1121] [] - Executing configure() of class org.flowable.eventregistry.spring.configurator.SpringEventRegistryConfigurator (priority:100000)
20:53:06.249 INFO  [ne.impl.db.CommonDbSchemaManager:247] [] - performing create on common with resource org/flowable/common/db/create/flowable.h2.create.common.sql
20:53:07.331 INFO  [              liquibase.changelog:37] [] - Creating database history table with name: FLW_EV_DATABASECHANGELOG
20:53:07.496 INFO  [              liquibase.changelog:37] [] - Reading from FLW_EV_DATABASECHANGELOG
20:53:07.676 INFO  [            liquibase.lockservice:37] [] - Successfully acquired change log lock
20:53:07.678 INFO  [                liquibase.command:37] [] - Using deploymentId: 8436787678
20:53:07.680 INFO  [              liquibase.changelog:37] [] - Reading from FLW_EV_DATABASECHANGELOG
20:53:07.752 INFO  [              liquibase.changelog:37] [] - Table FLW_EVENT_DEPLOYMENT created
20:53:07.757 INFO  [              liquibase.changelog:37] [] - Table FLW_EVENT_RESOURCE created
20:53:07.762 INFO  [              liquibase.changelog:37] [] - Table FLW_EVENT_DEFINITION created
20:53:07.764 INFO  [              liquibase.changelog:37] [] - Index ACT_IDX_EVENT_DEF_UNIQ created
20:53:07.769 INFO  [              liquibase.changelog:37] [] - Table FLW_CHANNEL_DEFINITION created
20:53:07.771 INFO  [              liquibase.changelog:37] [] - Index ACT_IDX_CHANNEL_DEF_UNIQ created
20:53:07.776 INFO  [              liquibase.changelog:37] [] - ChangeSet org/flowable/eventregistry/db/liquibase/flowable-eventregistry-db-changelog.xml::1::flowable ran successfully in 30ms
20:53:08.141 INFO  [               liquibase.snapshot:37] [] - Creating snapshot
20:53:08.290 INFO  [               liquibase.snapshot:37] [] - Creating snapshot
20:53:08.352 INFO  [              liquibase.changelog:37] [] - Columns TYPE_(varchar(255)) added to FLW_CHANNEL_DEFINITION
20:53:08.487 INFO  [               liquibase.snapshot:37] [] - Creating snapshot
20:53:08.636 INFO  [               liquibase.snapshot:37] [] - Creating snapshot
20:53:08.679 INFO  [              liquibase.changelog:37] [] - Columns IMPLEMENTATION_(varchar(255)) added to FLW_CHANNEL_DEFINITION
20:53:08.684 INFO  [              liquibase.changelog:37] [] - ChangeSet org/flowable/eventregistry/db/liquibase/flowable-eventregistry-db-changelog.xml::2::flowable ran successfully in 899ms
20:53:08.838 INFO  [              liquibase.changelog:37] [] - Set Channel Definition type and implementation columns
20:53:08.839 INFO  [              liquibase.changelog:37] [] - ChangeSet org/flowable/eventregistry/db/liquibase/flowable-eventregistry-db-changelog.xml::3::flowable ran successfully in 146ms
20:53:08.853 INFO  [                   liquibase.util:37] [] - UPDATE SUMMARY
20:53:08.855 INFO  [                   liquibase.util:37] [] - Run:                          3
20:53:08.856 INFO  [                   liquibase.util:37] [] - Previously run:               0
20:53:08.857 INFO  [                   liquibase.util:37] [] - Filtered out:                 0
20:53:08.858 INFO  [                   liquibase.util:37] [] - -------------------------------
20:53:08.859 INFO  [                   liquibase.util:37] [] - Total change sets:            3
20:53:08.863 INFO  [                   liquibase.util:37] [] - Update summary generated
20:53:08.881 INFO  [                liquibase.command:37] [] - Update command completed successfully.
20:53:08.892 INFO  [            liquibase.lockservice:37] [] - Successfully released change log lock
20:53:08.896 INFO  [                liquibase.command:37] [] - Command execution complete
20:53:08.901 INFO  [stry.impl.EventRegistryEngineImpl:53] [] - EventRegistryEngine default created
20:53:08.903 INFO  [pringProcessEngineConfiguration:1121] [] - Executing configure() of class org.flowable.idm.engine.configurator.IdmEngineConfigurator (priority:150000)
20:53:08.945 INFO  [ngine.impl.db.IdmDbSchemaManager:247] [] - performing create on identity with resource org/flowable/idm/db/create/flowable.h2.create.identity.sql
20:53:08.972 ERROR [ngine.impl.db.IdmDbSchemaManager:333] [] - problem during schema create, statement alter table ACT_ID_MEMBERSHIP 
add constraint ACT_FK_MEMB_GROUP 
foreign key (GROUP_ID_) 
references ACT_ID_GROUP
org.sqlite.SQLiteException: [SQLITE_ERROR] SQL error or missing database (near "constraint": syntax error)
	at org.sqlite.core.DB.newSQLException(DB.java:1179)
	at org.sqlite.core.DB.newSQLException(DB.java:1190)
	at org.sqlite.core.DB.throwex(DB.java:1150)
	at org.sqlite.core.NativeDB.prepare_utf8(Native Method)
	at org.sqlite.core.NativeDB.prepare(NativeDB.java:132)
	at org.sqlite.core.DB.prepare(DB.java:264)
	at org.sqlite.jdbc3.JDBC3Statement.lambda$execute$0(JDBC3Statement.java:54)
	at org.sqlite.jdbc3.JDBC3Statement.withConnectionTimeout(JDBC3Statement.java:454)
	at org.sqlite.jdbc3.JDBC3Statement.execute(JDBC3Statement.java:43)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.flowable.common.engine.impl.db.AbstractSqlScriptBasedDbSchemaManager.executeSchemaResource(AbstractSqlScriptBasedDbSchemaManager.java:325)
	at org.flowable.common.engine.impl.db.AbstractSqlScriptBasedDbSchemaManager.executeSchemaResource(AbstractSqlScriptBasedDbSchemaManager.java:238)
	at org.flowable.common.engine.impl.db.AbstractSqlScriptBasedDbSchemaManager.executeMandatorySchemaResource(AbstractSqlScriptBasedDbSchemaManager.java:226)
	at org.flowable.common.engine.impl.db.ServiceSqlScriptBasedDbSchemaManager.internalDbSchemaCreate(ServiceSqlScriptBasedDbSchemaManager.java:49)
	at org.flowable.idm.engine.impl.db.IdmDbSchemaManager.internalDbSchemaCreate(IdmDbSchemaManager.java:55)
	at org.flowable.common.engine.impl.db.ServiceSqlScriptBasedDbSchemaManager.schemaCreate(ServiceSqlScriptBasedDbSchemaManager.java:44)
	at org.flowable.common.engine.impl.db.ServiceSqlScriptBasedDbSchemaManager.schemaUpdate(ServiceSqlScriptBasedDbSchemaManager.java:91)
	at org.flowable.common.engine.impl.db.ServiceSqlScriptBasedDbSchemaManager.schemaUpdate(ServiceSqlScriptBasedDbSchemaManager.java:65)
	at org.flowable.idm.engine.impl.db.IdmDbSchemaManager.performSchemaOperationsIdmEngineBuild(IdmDbSchemaManager.java:151)
	at org.flowable.idm.engine.impl.SchemaOperationsIdmEngineBuild.execute(SchemaOperationsIdmEngineBuild.java:29)
	at org.flowable.idm.engine.impl.SchemaOperationsIdmEngineBuild.execute(SchemaOperationsIdmEngineBuild.java:24)
	at org.flowable.common.engine.impl.interceptor.DefaultCommandInvoker.execute(DefaultCommandInvoker.java:22)
	at org.flowable.common.engine.impl.interceptor.TransactionContextInterceptor.execute(TransactionContextInterceptor.java:53)
	at org.flowable.common.engine.impl.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:105)
	at org.flowable.common.engine.impl.interceptor.LogInterceptor.execute(LogInterceptor.java:30)
	at org.flowable.common.engine.impl.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:56)
	at org.flowable.idm.engine.impl.IdmEngineImpl.<init>(IdmEngineImpl.java:46)
	at org.flowable.idm.engine.IdmEngineConfiguration.buildIdmEngine(IdmEngineConfiguration.java:164)
	at org.flowable.idm.engine.configurator.IdmEngineConfigurator.configure(IdmEngineConfigurator.java:57)
	at org.flowable.common.engine.impl.AbstractEngineConfiguration.configuratorsAfterInit(AbstractEngineConfiguration.java:1122)
	at org.flowable.engine.impl.cfg.ProcessEngineConfigurationImpl.init(ProcessEngineConfigurationImpl.java:992)
	at org.flowable.engine.impl.cfg.ProcessEngineConfigurationImpl.buildProcessEngine(ProcessEngineConfigurationImpl.java:873)
	at org.flowable.spring.SpringProcessEngineConfiguration.buildProcessEngine(SpringProcessEngineConfiguration.java:76)
	at org.flowable.spring.ProcessEngineFactoryBean.getObject(ProcessEngineFactoryBean.java:59)
	at org.flowable.spring.ProcessEngineFactoryBean.getObject(ProcessEngineFactoryBean.java:32)
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.doGetObjectFromFactoryBean(FactoryBeanRegistrySupport.java:182)
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.getObjectFromFactoryBean(FactoryBeanRegistrySupport.java:124)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getObjectForBeanInstance(AbstractBeanFactory.java:1867)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getObjectForBeanInstance(AbstractAutowireCapableBeanFactory.java:1296)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:542)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1185)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.xyy.saas.localserver.LocalserverApplication.main(LocalserverApplication.java:22)
20:53:08.978 ERROR [ngine.impl.db.IdmDbSchemaManager:333] [] - problem during schema create, statement alter table ACT_ID_MEMBERSHIP 
add constraint ACT_FK_MEMB_USER 
foreign key (USER_ID_) 
references ACT_ID_USER
org.sqlite.SQLiteException: [SQLITE_ERROR] SQL error or missing database (near "constraint": syntax error)
	at org.sqlite.core.DB.newSQLException(DB.java:1179)
	at org.sqlite.core.DB.newSQLException(DB.java:1190)
	at org.sqlite.core.DB.throwex(DB.java:1150)
	at org.sqlite.core.NativeDB.prepare_utf8(Native Method)
	at org.sqlite.core.NativeDB.prepare(NativeDB.java:132)
	at org.sqlite.core.DB.prepare(DB.java:264)
	at org.sqlite.jdbc3.JDBC3Statement.lambda$execute$0(JDBC3Statement.java:54)
	at org.sqlite.jdbc3.JDBC3Statement.withConnectionTimeout(JDBC3Statement.java:454)
	at org.sqlite.jdbc3.JDBC3Statement.execute(JDBC3Statement.java:43)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.flowable.common.engine.impl.db.AbstractSqlScriptBasedDbSchemaManager.executeSchemaResource(AbstractSqlScriptBasedDbSchemaManager.java:325)
	at org.flowable.common.engine.impl.db.AbstractSqlScriptBasedDbSchemaManager.executeSchemaResource(AbstractSqlScriptBasedDbSchemaManager.java:238)
	at org.flowable.common.engine.impl.db.AbstractSqlScriptBasedDbSchemaManager.executeMandatorySchemaResource(AbstractSqlScriptBasedDbSchemaManager.java:226)
	at org.flowable.common.engine.impl.db.ServiceSqlScriptBasedDbSchemaManager.internalDbSchemaCreate(ServiceSqlScriptBasedDbSchemaManager.java:49)
	at org.flowable.idm.engine.impl.db.IdmDbSchemaManager.internalDbSchemaCreate(IdmDbSchemaManager.java:55)
	at org.flowable.common.engine.impl.db.ServiceSqlScriptBasedDbSchemaManager.schemaCreate(ServiceSqlScriptBasedDbSchemaManager.java:44)
	at org.flowable.common.engine.impl.db.ServiceSqlScriptBasedDbSchemaManager.schemaUpdate(ServiceSqlScriptBasedDbSchemaManager.java:91)
	at org.flowable.common.engine.impl.db.ServiceSqlScriptBasedDbSchemaManager.schemaUpdate(ServiceSqlScriptBasedDbSchemaManager.java:65)
	at org.flowable.idm.engine.impl.db.IdmDbSchemaManager.performSchemaOperationsIdmEngineBuild(IdmDbSchemaManager.java:151)
	at org.flowable.idm.engine.impl.SchemaOperationsIdmEngineBuild.execute(SchemaOperationsIdmEngineBuild.java:29)
	at org.flowable.idm.engine.impl.SchemaOperationsIdmEngineBuild.execute(SchemaOperationsIdmEngineBuild.java:24)
	at org.flowable.common.engine.impl.interceptor.DefaultCommandInvoker.execute(DefaultCommandInvoker.java:22)
	at org.flowable.common.engine.impl.interceptor.TransactionContextInterceptor.execute(TransactionContextInterceptor.java:53)
	at org.flowable.common.engine.impl.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:105)
	at org.flowable.common.engine.impl.interceptor.LogInterceptor.execute(LogInterceptor.java:30)
	at org.flowable.common.engine.impl.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:56)
	at org.flowable.idm.engine.impl.IdmEngineImpl.<init>(IdmEngineImpl.java:46)
	at org.flowable.idm.engine.IdmEngineConfiguration.buildIdmEngine(IdmEngineConfiguration.java:164)
	at org.flowable.idm.engine.configurator.IdmEngineConfigurator.configure(IdmEngineConfigurator.java:57)
	at org.flowable.common.engine.impl.AbstractEngineConfiguration.configuratorsAfterInit(AbstractEngineConfiguration.java:1122)
	at org.flowable.engine.impl.cfg.ProcessEngineConfigurationImpl.init(ProcessEngineConfigurationImpl.java:992)
	at org.flowable.engine.impl.cfg.ProcessEngineConfigurationImpl.buildProcessEngine(ProcessEngineConfigurationImpl.java:873)
	at org.flowable.spring.SpringProcessEngineConfiguration.buildProcessEngine(SpringProcessEngineConfiguration.java:76)
	at org.flowable.spring.ProcessEngineFactoryBean.getObject(ProcessEngineFactoryBean.java:59)
	at org.flowable.spring.ProcessEngineFactoryBean.getObject(ProcessEngineFactoryBean.java:32)
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.doGetObjectFromFactoryBean(FactoryBeanRegistrySupport.java:182)
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.getObjectFromFactoryBean(FactoryBeanRegistrySupport.java:124)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getObjectForBeanInstance(AbstractBeanFactory.java:1867)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getObjectForBeanInstance(AbstractAutowireCapableBeanFactory.java:1296)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:542)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1185)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.xyy.saas.localserver.LocalserverApplication.main(LocalserverApplication.java:22)
20:53:08.981 ERROR [ngine.impl.db.IdmDbSchemaManager:333] [] - problem during schema create, statement alter table ACT_ID_PRIV_MAPPING 
add constraint ACT_FK_PRIV_MAPPING 
foreign key (PRIV_ID_) 
references ACT_ID_PRIV
org.sqlite.SQLiteException: [SQLITE_ERROR] SQL error or missing database (near "constraint": syntax error)
	at org.sqlite.core.DB.newSQLException(DB.java:1179)
	at org.sqlite.core.DB.newSQLException(DB.java:1190)
	at org.sqlite.core.DB.throwex(DB.java:1150)
	at org.sqlite.core.NativeDB.prepare_utf8(Native Method)
	at org.sqlite.core.NativeDB.prepare(NativeDB.java:132)
	at org.sqlite.core.DB.prepare(DB.java:264)
	at org.sqlite.jdbc3.JDBC3Statement.lambda$execute$0(JDBC3Statement.java:54)
	at org.sqlite.jdbc3.JDBC3Statement.withConnectionTimeout(JDBC3Statement.java:454)
	at org.sqlite.jdbc3.JDBC3Statement.execute(JDBC3Statement.java:43)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.flowable.common.engine.impl.db.AbstractSqlScriptBasedDbSchemaManager.executeSchemaResource(AbstractSqlScriptBasedDbSchemaManager.java:325)
	at org.flowable.common.engine.impl.db.AbstractSqlScriptBasedDbSchemaManager.executeSchemaResource(AbstractSqlScriptBasedDbSchemaManager.java:238)
	at org.flowable.common.engine.impl.db.AbstractSqlScriptBasedDbSchemaManager.executeMandatorySchemaResource(AbstractSqlScriptBasedDbSchemaManager.java:226)
	at org.flowable.common.engine.impl.db.ServiceSqlScriptBasedDbSchemaManager.internalDbSchemaCreate(ServiceSqlScriptBasedDbSchemaManager.java:49)
	at org.flowable.idm.engine.impl.db.IdmDbSchemaManager.internalDbSchemaCreate(IdmDbSchemaManager.java:55)
	at org.flowable.common.engine.impl.db.ServiceSqlScriptBasedDbSchemaManager.schemaCreate(ServiceSqlScriptBasedDbSchemaManager.java:44)
	at org.flowable.common.engine.impl.db.ServiceSqlScriptBasedDbSchemaManager.schemaUpdate(ServiceSqlScriptBasedDbSchemaManager.java:91)
	at org.flowable.common.engine.impl.db.ServiceSqlScriptBasedDbSchemaManager.schemaUpdate(ServiceSqlScriptBasedDbSchemaManager.java:65)
	at org.flowable.idm.engine.impl.db.IdmDbSchemaManager.performSchemaOperationsIdmEngineBuild(IdmDbSchemaManager.java:151)
	at org.flowable.idm.engine.impl.SchemaOperationsIdmEngineBuild.execute(SchemaOperationsIdmEngineBuild.java:29)
	at org.flowable.idm.engine.impl.SchemaOperationsIdmEngineBuild.execute(SchemaOperationsIdmEngineBuild.java:24)
	at org.flowable.common.engine.impl.interceptor.DefaultCommandInvoker.execute(DefaultCommandInvoker.java:22)
	at org.flowable.common.engine.impl.interceptor.TransactionContextInterceptor.execute(TransactionContextInterceptor.java:53)
	at org.flowable.common.engine.impl.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:105)
	at org.flowable.common.engine.impl.interceptor.LogInterceptor.execute(LogInterceptor.java:30)
	at org.flowable.common.engine.impl.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:56)
	at org.flowable.idm.engine.impl.IdmEngineImpl.<init>(IdmEngineImpl.java:46)
	at org.flowable.idm.engine.IdmEngineConfiguration.buildIdmEngine(IdmEngineConfiguration.java:164)
	at org.flowable.idm.engine.configurator.IdmEngineConfigurator.configure(IdmEngineConfigurator.java:57)
	at org.flowable.common.engine.impl.AbstractEngineConfiguration.configuratorsAfterInit(AbstractEngineConfiguration.java:1122)
	at org.flowable.engine.impl.cfg.ProcessEngineConfigurationImpl.init(ProcessEngineConfigurationImpl.java:992)
	at org.flowable.engine.impl.cfg.ProcessEngineConfigurationImpl.buildProcessEngine(ProcessEngineConfigurationImpl.java:873)
	at org.flowable.spring.SpringProcessEngineConfiguration.buildProcessEngine(SpringProcessEngineConfiguration.java:76)
	at org.flowable.spring.ProcessEngineFactoryBean.getObject(ProcessEngineFactoryBean.java:59)
	at org.flowable.spring.ProcessEngineFactoryBean.getObject(ProcessEngineFactoryBean.java:32)
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.doGetObjectFromFactoryBean(FactoryBeanRegistrySupport.java:182)
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.getObjectFromFactoryBean(FactoryBeanRegistrySupport.java:124)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getObjectForBeanInstance(AbstractBeanFactory.java:1867)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getObjectForBeanInstance(AbstractAutowireCapableBeanFactory.java:1296)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:542)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1185)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.xyy.saas.localserver.LocalserverApplication.main(LocalserverApplication.java:22)
20:53:08.990 ERROR [ngine.impl.db.IdmDbSchemaManager:333] [] - problem during schema create, statement alter table ACT_ID_PRIV 
add constraint ACT_UNIQ_PRIV_NAME 
unique (NAME_)
org.sqlite.SQLiteException: [SQLITE_ERROR] SQL error or missing database (near "constraint": syntax error)
	at org.sqlite.core.DB.newSQLException(DB.java:1179)
	at org.sqlite.core.DB.newSQLException(DB.java:1190)
	at org.sqlite.core.DB.throwex(DB.java:1150)
	at org.sqlite.core.NativeDB.prepare_utf8(Native Method)
	at org.sqlite.core.NativeDB.prepare(NativeDB.java:132)
	at org.sqlite.core.DB.prepare(DB.java:264)
	at org.sqlite.jdbc3.JDBC3Statement.lambda$execute$0(JDBC3Statement.java:54)
	at org.sqlite.jdbc3.JDBC3Statement.withConnectionTimeout(JDBC3Statement.java:454)
	at org.sqlite.jdbc3.JDBC3Statement.execute(JDBC3Statement.java:43)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.flowable.common.engine.impl.db.AbstractSqlScriptBasedDbSchemaManager.executeSchemaResource(AbstractSqlScriptBasedDbSchemaManager.java:325)
	at org.flowable.common.engine.impl.db.AbstractSqlScriptBasedDbSchemaManager.executeSchemaResource(AbstractSqlScriptBasedDbSchemaManager.java:238)
	at org.flowable.common.engine.impl.db.AbstractSqlScriptBasedDbSchemaManager.executeMandatorySchemaResource(AbstractSqlScriptBasedDbSchemaManager.java:226)
	at org.flowable.common.engine.impl.db.ServiceSqlScriptBasedDbSchemaManager.internalDbSchemaCreate(ServiceSqlScriptBasedDbSchemaManager.java:49)
	at org.flowable.idm.engine.impl.db.IdmDbSchemaManager.internalDbSchemaCreate(IdmDbSchemaManager.java:55)
	at org.flowable.common.engine.impl.db.ServiceSqlScriptBasedDbSchemaManager.schemaCreate(ServiceSqlScriptBasedDbSchemaManager.java:44)
	at org.flowable.common.engine.impl.db.ServiceSqlScriptBasedDbSchemaManager.schemaUpdate(ServiceSqlScriptBasedDbSchemaManager.java:91)
	at org.flowable.common.engine.impl.db.ServiceSqlScriptBasedDbSchemaManager.schemaUpdate(ServiceSqlScriptBasedDbSchemaManager.java:65)
	at org.flowable.idm.engine.impl.db.IdmDbSchemaManager.performSchemaOperationsIdmEngineBuild(IdmDbSchemaManager.java:151)
	at org.flowable.idm.engine.impl.SchemaOperationsIdmEngineBuild.execute(SchemaOperationsIdmEngineBuild.java:29)
	at org.flowable.idm.engine.impl.SchemaOperationsIdmEngineBuild.execute(SchemaOperationsIdmEngineBuild.java:24)
	at org.flowable.common.engine.impl.interceptor.DefaultCommandInvoker.execute(DefaultCommandInvoker.java:22)
	at org.flowable.common.engine.impl.interceptor.TransactionContextInterceptor.execute(TransactionContextInterceptor.java:53)
	at org.flowable.common.engine.impl.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:105)
	at org.flowable.common.engine.impl.interceptor.LogInterceptor.execute(LogInterceptor.java:30)
	at org.flowable.common.engine.impl.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:56)
	at org.flowable.idm.engine.impl.IdmEngineImpl.<init>(IdmEngineImpl.java:46)
	at org.flowable.idm.engine.IdmEngineConfiguration.buildIdmEngine(IdmEngineConfiguration.java:164)
	at org.flowable.idm.engine.configurator.IdmEngineConfigurator.configure(IdmEngineConfigurator.java:57)
	at org.flowable.common.engine.impl.AbstractEngineConfiguration.configuratorsAfterInit(AbstractEngineConfiguration.java:1122)
	at org.flowable.engine.impl.cfg.ProcessEngineConfigurationImpl.init(ProcessEngineConfigurationImpl.java:992)
	at org.flowable.engine.impl.cfg.ProcessEngineConfigurationImpl.buildProcessEngine(ProcessEngineConfigurationImpl.java:873)
	at org.flowable.spring.SpringProcessEngineConfiguration.buildProcessEngine(SpringProcessEngineConfiguration.java:76)
	at org.flowable.spring.ProcessEngineFactoryBean.getObject(ProcessEngineFactoryBean.java:59)
	at org.flowable.spring.ProcessEngineFactoryBean.getObject(ProcessEngineFactoryBean.java:32)
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.doGetObjectFromFactoryBean(FactoryBeanRegistrySupport.java:182)
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.getObjectFromFactoryBean(FactoryBeanRegistrySupport.java:124)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getObjectForBeanInstance(AbstractBeanFactory.java:1867)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getObjectForBeanInstance(AbstractAutowireCapableBeanFactory.java:1296)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:542)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1185)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.xyy.saas.localserver.LocalserverApplication.main(LocalserverApplication.java:22)
20:53:08.996 WARN  [rvletWebServerApplicationContext:633] [] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'appProductPresentController': Injection of resource dependencies failed
20:53:09.007 INFO  [m.zaxxer.hikari.HikariDataSource:349] [] - HikariPool-1 - Shutdown initiated...
20:53:09.014 INFO  [m.zaxxer.hikari.HikariDataSource:351] [] - HikariPool-1 - Shutdown completed.
20:53:09.021 INFO  [he.catalina.core.StandardService:173] [] - Stopping service [Tomcat]
20:53:09.070 INFO  [g.ConditionEvaluationReportLogger:82] [] - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
20:53:09.123 ERROR [framework.boot.SpringApplication:859] [] - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'appProductPresentController': Injection of resource dependencies failed
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:371)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.xyy.saas.localserver.LocalserverApplication.main(LocalserverApplication.java:22)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'productInfoServiceImpl': Injection of resource dependencies failed
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:371)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	... 17 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'bpmBusinessRelationServiceImpl': Injection of resource dependencies failed
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:371)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	... 33 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'bpmProcessInstanceApiImpl': Injection of resource dependencies failed
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:371)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	... 49 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'bpmProcessInstanceServiceImpl': Injection of resource dependencies failed
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:371)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1439)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	... 65 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'runtimeServiceBean' defined in class path resource [org/flowable/spring/boot/ProcessEngineServicesAutoConfiguration.class]: Unsatisfied dependency expressed through method 'runtimeServiceBean' parameter 0: Error creating bean with name 'processEngine': FactoryBean threw exception on object creation
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:795)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:542)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1185)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:598)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:576)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:738)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:270)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:368)
	... 81 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'processEngine': FactoryBean threw exception on object creation
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.doGetObjectFromFactoryBean(FactoryBeanRegistrySupport.java:188)
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.getObjectFromFactoryBean(FactoryBeanRegistrySupport.java:124)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getObjectForBeanInstance(AbstractBeanFactory.java:1867)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.getObjectForBeanInstance(AbstractAutowireCapableBeanFactory.java:1296)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
	... 99 common frames omitted
Caused by: org.flowable.common.engine.api.FlowableException: couldn't create db schema: alter table ACT_ID_MEMBERSHIP 
add constraint ACT_FK_MEMB_GROUP 
foreign key (GROUP_ID_) 
references ACT_ID_GROUP
	at org.flowable.common.engine.impl.db.AbstractSqlScriptBasedDbSchemaManager.executeSchemaResource(AbstractSqlScriptBasedDbSchemaManager.java:354)
	at org.flowable.common.engine.impl.db.AbstractSqlScriptBasedDbSchemaManager.executeSchemaResource(AbstractSqlScriptBasedDbSchemaManager.java:238)
	at org.flowable.common.engine.impl.db.AbstractSqlScriptBasedDbSchemaManager.executeMandatorySchemaResource(AbstractSqlScriptBasedDbSchemaManager.java:226)
	at org.flowable.common.engine.impl.db.ServiceSqlScriptBasedDbSchemaManager.internalDbSchemaCreate(ServiceSqlScriptBasedDbSchemaManager.java:49)
	at org.flowable.idm.engine.impl.db.IdmDbSchemaManager.internalDbSchemaCreate(IdmDbSchemaManager.java:55)
	at org.flowable.common.engine.impl.db.ServiceSqlScriptBasedDbSchemaManager.schemaCreate(ServiceSqlScriptBasedDbSchemaManager.java:44)
	at org.flowable.common.engine.impl.db.ServiceSqlScriptBasedDbSchemaManager.schemaUpdate(ServiceSqlScriptBasedDbSchemaManager.java:91)
	at org.flowable.common.engine.impl.db.ServiceSqlScriptBasedDbSchemaManager.schemaUpdate(ServiceSqlScriptBasedDbSchemaManager.java:65)
	at org.flowable.idm.engine.impl.db.IdmDbSchemaManager.performSchemaOperationsIdmEngineBuild(IdmDbSchemaManager.java:151)
	at org.flowable.idm.engine.impl.SchemaOperationsIdmEngineBuild.execute(SchemaOperationsIdmEngineBuild.java:29)
	at org.flowable.idm.engine.impl.SchemaOperationsIdmEngineBuild.execute(SchemaOperationsIdmEngineBuild.java:24)
	at org.flowable.common.engine.impl.interceptor.DefaultCommandInvoker.execute(DefaultCommandInvoker.java:22)
	at org.flowable.common.engine.impl.interceptor.TransactionContextInterceptor.execute(TransactionContextInterceptor.java:53)
	at org.flowable.common.engine.impl.interceptor.CommandContextInterceptor.execute(CommandContextInterceptor.java:105)
	at org.flowable.common.engine.impl.interceptor.LogInterceptor.execute(LogInterceptor.java:30)
	at org.flowable.common.engine.impl.cfg.CommandExecutorImpl.execute(CommandExecutorImpl.java:56)
	at org.flowable.idm.engine.impl.IdmEngineImpl.<init>(IdmEngineImpl.java:46)
	at org.flowable.idm.engine.IdmEngineConfiguration.buildIdmEngine(IdmEngineConfiguration.java:164)
	at org.flowable.idm.engine.configurator.IdmEngineConfigurator.configure(IdmEngineConfigurator.java:57)
	at org.flowable.common.engine.impl.AbstractEngineConfiguration.configuratorsAfterInit(AbstractEngineConfiguration.java:1122)
	at org.flowable.engine.impl.cfg.ProcessEngineConfigurationImpl.init(ProcessEngineConfigurationImpl.java:992)
	at org.flowable.engine.impl.cfg.ProcessEngineConfigurationImpl.buildProcessEngine(ProcessEngineConfigurationImpl.java:873)
	at org.flowable.spring.SpringProcessEngineConfiguration.buildProcessEngine(SpringProcessEngineConfiguration.java:76)
	at org.flowable.spring.ProcessEngineFactoryBean.getObject(ProcessEngineFactoryBean.java:59)
	at org.flowable.spring.ProcessEngineFactoryBean.getObject(ProcessEngineFactoryBean.java:32)
	at org.springframework.beans.factory.support.FactoryBeanRegistrySupport.doGetObjectFromFactoryBean(FactoryBeanRegistrySupport.java:182)
	... 109 common frames omitted
Caused by: org.sqlite.SQLiteException: [SQLITE_ERROR] SQL error or missing database (near "constraint": syntax error)
	at org.sqlite.core.DB.newSQLException(DB.java:1179)
	at org.sqlite.core.DB.newSQLException(DB.java:1190)
	at org.sqlite.core.DB.throwex(DB.java:1150)
	at org.sqlite.core.NativeDB.prepare_utf8(Native Method)
	at org.sqlite.core.NativeDB.prepare(NativeDB.java:132)
	at org.sqlite.core.DB.prepare(DB.java:264)
	at org.sqlite.jdbc3.JDBC3Statement.lambda$execute$0(JDBC3Statement.java:54)
	at org.sqlite.jdbc3.JDBC3Statement.withConnectionTimeout(JDBC3Statement.java:454)
	at org.sqlite.jdbc3.JDBC3Statement.execute(JDBC3Statement.java:43)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at org.flowable.common.engine.impl.db.AbstractSqlScriptBasedDbSchemaManager.executeSchemaResource(AbstractSqlScriptBasedDbSchemaManager.java:325)
	... 134 common frames omitted

00:03:02.901 INFO  [ingleThreadPullSubscriberExecutor:40] [] - 数据同步,单线程拉取任务开始, 机构号:[1].
00:03:02.934 DEBUG [aas.localserver.utils.EventPusher:39] [] - source [com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor@6cbcfadd] syncPost event[com.xyy.saas.datasync.client.event.PullDataStartedEvent]=[{}]
00:03:02.934 INFO  [.CycleLoopPullSubscriberExecutor:107] [] - 数据同步,开始从云端pull数据,待同步表数量:[136], 机构号:[1]
00:03:02.935 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_level_record]
00:03:02.935 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_level_record], pull次数:[1]
00:03:02.947 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_level_record], pull次数:[1], 总计条数:[0]
00:03:02.947 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_level_record], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:02.947 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_data_source_config]
00:03:02.947 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_data_source_config], pull次数:[1]
00:03:03.050 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[infra_data_source_config], pull次数:[1], 总计条数:[0]
00:03:03.050 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_data_source_config], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.050 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_codegen_table]
00:03:03.051 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_codegen_table], pull次数:[1]
00:03:03.122 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[infra_codegen_table], pull次数:[1], 总计条数:[0]
00:03:03.122 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_codegen_table], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.122 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_filing]
00:03:03.122 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_filing], pull次数:[1]
00:03:03.127 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_filing], pull次数:[1], 总计条数:[0]
00:03:03.127 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_filing], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.127 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_point_record]
00:03:03.127 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_point_record], pull次数:[1]
00:03:03.132 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_point_record], pull次数:[1], 总计条数:[0]
00:03:03.132 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_point_record], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.132 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_transfer_record]
00:03:03.132 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_transfer_record], pull次数:[1]
00:03:03.136 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_transfer_record], pull次数:[1], 总计条数:[0]
00:03:03.136 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_transfer_record], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.136 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_tenant_param_config]
00:03:03.136 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_tenant_param_config], pull次数:[1]
00:03:03.140 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_tenant_param_config], pull次数:[1], 总计条数:[0]
00:03:03.140 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_tenant_param_config], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.140 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_prescription_detail]
00:03:03.140 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_prescription_detail], pull次数:[1]
00:03:03.144 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_prescription_detail], pull次数:[1], 总计条数:[0]
00:03:03.145 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_prescription_detail], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.145 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_doctor]
00:03:03.145 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_doctor], pull次数:[1]
00:03:03.149 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_doctor], pull次数:[1], 总计条数:[0]
00:03:03.149 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_doctor], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.149 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[bpm_user_group]
00:03:03.149 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[bpm_user_group], pull次数:[1]
00:03:03.154 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[bpm_user_group], pull次数:[1], 总计条数:[0]
00:03:03.154 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[bpm_user_group], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.154 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_main_suit]
00:03:03.154 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_main_suit], pull次数:[1]
00:03:03.158 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_main_suit], pull次数:[1], 总计条数:[0]
00:03:03.159 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_main_suit], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.159 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_social_user]
00:03:03.159 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_social_user], pull次数:[1]
00:03:03.264 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_social_user], pull次数:[1], 总计条数:[0]
00:03:03.264 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_social_user], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.265 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_price_adjustment_record]
00:03:03.265 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_price_adjustment_record], pull次数:[1]
00:03:03.270 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_price_adjustment_record], pull次数:[1], 总计条数:[0]
00:03:03.270 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_price_adjustment_record], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.270 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_menu]
00:03:03.270 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_menu], pull次数:[1]
00:03:03.346 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_menu], pull次数:[1], 总计条数:[0]
00:03:03.347 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_menu], pull次数:[1], 总计条数:[0], 最大base版本:[843]
00:03:03.347 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_bpm_business_relation]
00:03:03.347 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_bpm_business_relation], pull次数:[1]
00:03:03.352 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_bpm_business_relation], pull次数:[1], 总计条数:[0]
00:03:03.352 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_bpm_business_relation], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.352 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_oauth2_approve]
00:03:03.352 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_oauth2_approve], pull次数:[1]
00:03:03.424 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_oauth2_approve], pull次数:[1], 总计条数:[0]
00:03:03.424 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_oauth2_approve], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.424 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[bpm_process_expression]
00:03:03.424 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[bpm_process_expression], pull次数:[1]
00:03:03.429 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[bpm_process_expression], pull次数:[1], 总计条数:[0]
00:03:03.429 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[bpm_process_expression], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.429 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_codegen_column]
00:03:03.429 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_codegen_column], pull次数:[1]
00:03:03.505 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[infra_codegen_column], pull次数:[1], 总计条数:[0]
00:03:03.506 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_codegen_column], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.506 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_hospital_dept_doctor_relation]
00:03:03.506 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_hospital_dept_doctor_relation], pull次数:[1]
00:03:03.512 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_hospital_dept_doctor_relation], pull次数:[1], 总计条数:[0]
00:03:03.512 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_hospital_dept_doctor_relation], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.512 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_experience_record]
00:03:03.512 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_experience_record], pull次数:[1]
00:03:03.517 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_experience_record], pull次数:[1], 总计条数:[0]
00:03:03.517 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_experience_record], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.517 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_login_log]
00:03:03.517 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_login_log], pull次数:[1]
00:03:03.596 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_login_log], pull次数:[1], 总计条数:[0]
00:03:03.596 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_login_log], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.596 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_tenant_certificate]
00:03:03.596 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_tenant_certificate], pull次数:[1]
00:03:03.603 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_tenant_certificate], pull次数:[1], 总计条数:[0]
00:03:03.603 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_tenant_certificate], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.603 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_third_party_pre_inquiry]
00:03:03.603 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_third_party_pre_inquiry], pull次数:[1]
00:03:03.608 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_third_party_pre_inquiry], pull次数:[1], 总计条数:[0]
00:03:03.608 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_third_party_pre_inquiry], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.608 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_group]
00:03:03.608 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_group], pull次数:[1]
00:03:03.612 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_group], pull次数:[1], 总计条数:[0]
00:03:03.613 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_group], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.613 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_social_client]
00:03:03.613 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_social_client], pull次数:[1]
00:03:03.690 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_social_client], pull次数:[1], 总计条数:[0]
00:03:03.690 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_social_client], pull次数:[1], 总计条数:[0], 最大base版本:[3]
00:03:03.690 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_clinical_case]
00:03:03.690 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_clinical_case], pull次数:[1]
00:03:03.694 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_clinical_case], pull次数:[1], 总计条数:[0]
00:03:03.694 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_clinical_case], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.694 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_dept]
00:03:03.694 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_dept], pull次数:[1]
00:03:03.766 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_dept], pull次数:[1], 总计条数:[0]
00:03:03.766 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_dept], pull次数:[1], 总计条数:[0], 最大base版本:[12]
00:03:03.766 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_price_adjustment_detail]
00:03:03.766 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_price_adjustment_detail], pull次数:[1]
00:03:03.770 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_price_adjustment_detail], pull次数:[1], 总计条数:[0]
00:03:03.770 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_price_adjustment_detail], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.770 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_user]
00:03:03.770 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_user], pull次数:[1]
00:03:03.774 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_user], pull次数:[1], 总计条数:[0]
00:03:03.774 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_user], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.774 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_im_message]
00:03:03.774 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_im_message], pull次数:[1]
00:03:03.778 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_im_message], pull次数:[1], 总计条数:[0]
00:03:03.778 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_im_message], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.778 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_user_signature_information]
00:03:03.778 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_user_signature_information], pull次数:[1]
00:03:03.781 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_user_signature_information], pull次数:[1], 总计条数:[0]
00:03:03.782 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_user_signature_information], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.782 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_signature_person]
00:03:03.782 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_signature_person], pull次数:[1]
00:03:03.785 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_signature_person], pull次数:[1], 总计条数:[0]
00:03:03.785 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_signature_person], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.785 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_file]
00:03:03.785 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_file], pull次数:[1]
00:03:03.856 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[infra_file], pull次数:[1], 总计条数:[0]
00:03:03.856 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_file], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.856 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_app_version]
00:03:03.856 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_app_version], pull次数:[1]
00:03:03.862 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_app_version], pull次数:[1], 总计条数:[0]
00:03:03.862 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_app_version], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.862 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_quality_change_detail]
00:03:03.862 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_quality_change_detail], pull次数:[1]
00:03:03.866 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_quality_change_detail], pull次数:[1], 总计条数:[0]
00:03:03.866 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_quality_change_detail], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.867 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_doctor_reviews]
00:03:03.867 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_doctor_reviews], pull次数:[1]
00:03:03.871 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_doctor_reviews], pull次数:[1], 总计条数:[0]
00:03:03.871 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_doctor_reviews], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.871 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_sign_in_record]
00:03:03.871 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_sign_in_record], pull次数:[1]
00:03:03.876 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_sign_in_record], pull次数:[1], 总计条数:[0]
00:03:03.876 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_sign_in_record], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.876 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_doctor_billing]
00:03:03.876 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_doctor_billing], pull次数:[1]
00:03:03.880 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_doctor_billing], pull次数:[1], 总计条数:[0]
00:03:03.880 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_doctor_billing], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.880 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_hospital_department_relation]
00:03:03.880 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_hospital_department_relation], pull次数:[1]
00:03:03.884 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_hospital_department_relation], pull次数:[1], 总计条数:[0]
00:03:03.884 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_hospital_department_relation], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.885 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_medical_insurance_order]
00:03:03.885 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_medical_insurance_order], pull次数:[1]
00:03:03.889 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_medical_insurance_order], pull次数:[1], 总计条数:[0]
00:03:03.889 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_medical_insurance_order], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.889 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_third_party_drug_match_fail_record]
00:03:03.889 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_third_party_drug_match_fail_record], pull次数:[1]
00:03:03.893 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_third_party_drug_match_fail_record], pull次数:[1], 总计条数:[0]
00:03:03.893 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_third_party_drug_match_fail_record], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.894 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_option_config]
00:03:03.894 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_option_config], pull次数:[1]
00:03:03.898 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_option_config], pull次数:[1], 总计条数:[0]
00:03:03.898 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_option_config], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.898 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_config]
00:03:03.898 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_config], pull次数:[1]
00:03:03.970 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[infra_config], pull次数:[1], 总计条数:[0]
00:03:03.970 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_config], pull次数:[1], 总计条数:[0], 最大base版本:[7]
00:03:03.971 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_tenant_third_app]
00:03:03.971 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_tenant_third_app], pull次数:[1]
00:03:03.976 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_tenant_third_app], pull次数:[1], 总计条数:[0]
00:03:03.976 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_tenant_third_app], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:03.976 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_job]
00:03:03.976 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_job], pull次数:[1]
00:03:04.051 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[infra_job], pull次数:[1], 总计条数:[0]
00:03:04.051 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_job], pull次数:[1], 总计条数:[0], 最大base版本:[11]
00:03:04.051 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_tenant_package]
00:03:04.051 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_tenant_package], pull次数:[1]
00:03:04.123 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_tenant_package], pull次数:[1], 总计条数:[0]
00:03:04.123 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_tenant_package], pull次数:[1], 总计条数:[0], 最大base版本:[1]
00:03:04.123 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_sms_code]
00:03:04.123 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_sms_code], pull次数:[1]
00:03:04.195 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_sms_code], pull次数:[1], 总计条数:[0]
00:03:04.195 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_sms_code], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.195 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_sms_channel]
00:03:04.195 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_sms_channel], pull次数:[1]
00:03:04.269 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_sms_channel], pull次数:[1], 总计条数:[0]
00:03:04.269 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_sms_channel], pull次数:[1], 总计条数:[0], 最大base版本:[3]
00:03:04.269 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_tenant]
00:03:04.269 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_tenant], pull次数:[1]
00:03:04.343 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_tenant], pull次数:[1], 总计条数:[0]
00:03:04.343 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_tenant], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.343 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_tag]
00:03:04.343 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_tag], pull次数:[1]
00:03:04.349 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_tag], pull次数:[1], 总计条数:[0]
00:03:04.349 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_tag], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.349 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_oauth2_access_token]
00:03:04.349 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_oauth2_access_token], pull次数:[1]
00:03:04.417 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_oauth2_access_token], pull次数:[1], 总计条数:[0]
00:03:04.417 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_oauth2_access_token], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.417 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_dict_type]
00:03:04.417 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_dict_type], pull次数:[1]
00:03:04.490 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_dict_type], pull次数:[1], 总计条数:[0]
00:03:04.490 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_dict_type], pull次数:[1], 总计条数:[0], 最大base版本:[93]
00:03:04.490 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[medicare_product]
00:03:04.491 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[medicare_product], pull次数:[1]
00:03:04.497 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[medicare_product], pull次数:[1], 总计条数:[0]
00:03:04.497 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[medicare_product], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.497 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_use_info]
00:03:04.497 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_use_info], pull次数:[1]
00:03:04.502 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_use_info], pull次数:[1], 总计条数:[0]
00:03:04.502 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_use_info], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.502 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_stdlib_sync]
00:03:04.502 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_stdlib_sync], pull次数:[1]
00:03:04.506 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_stdlib_sync], pull次数:[1], 总计条数:[0]
00:03:04.507 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_stdlib_sync], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.507 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_api_access_log]
00:03:04.507 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_api_access_log], pull次数:[1]
00:03:04.579 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[infra_api_access_log], pull次数:[1], 总计条数:[0]
00:03:04.580 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_api_access_log], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.580 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_level]
00:03:04.580 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_level], pull次数:[1]
00:03:04.585 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_level], pull次数:[1], 总计条数:[0]
00:03:04.585 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_level], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.585 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_catalog]
00:03:04.585 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_catalog], pull次数:[1]
00:03:04.589 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_catalog], pull次数:[1], 总计条数:[0]
00:03:04.589 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_catalog], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.589 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_diagnosis_department_relation]
00:03:04.589 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_diagnosis_department_relation], pull次数:[1]
00:03:04.593 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_diagnosis_department_relation], pull次数:[1], 总计条数:[0]
00:03:04.593 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_diagnosis_department_relation], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.593 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_medical_registration]
00:03:04.593 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_medical_registration], pull次数:[1]
00:03:04.597 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_medical_registration], pull次数:[1], 总计条数:[0]
00:03:04.597 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_medical_registration], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.597 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[bpm_process_definition_info]
00:03:04.597 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[bpm_process_definition_info], pull次数:[1]
00:03:04.621 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[bpm_process_definition_info], pull次数:[1], 总计条数:[0]
00:03:04.622 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[bpm_process_definition_info], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.622 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_regulatory_catalog_detail]
00:03:04.622 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_regulatory_catalog_detail], pull次数:[1]
00:03:04.626 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_regulatory_catalog_detail], pull次数:[1], 总计条数:[0]
00:03:04.626 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_regulatory_catalog_detail], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.626 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_user_role]
00:03:04.626 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_user_role], pull次数:[1]
00:03:04.715 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_user_role], pull次数:[1], 总计条数:[0]
00:03:04.715 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_user_role], pull次数:[1], 总计条数:[0], 最大base版本:[13]
00:03:04.715 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_tenant_user_finger_print]
00:03:04.715 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_tenant_user_finger_print], pull次数:[1]
00:03:04.720 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_tenant_user_finger_print], pull次数:[1], 总计条数:[0]
00:03:04.720 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_tenant_user_finger_print], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.720 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_tenant_user_clock_in_log]
00:03:04.720 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_tenant_user_clock_in_log], pull次数:[1]
00:03:04.724 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_tenant_user_clock_in_log], pull次数:[1], 总计条数:[0]
00:03:04.724 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_tenant_user_clock_in_log], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.724 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_mail_template]
00:03:04.724 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_mail_template], pull次数:[1]
00:03:04.796 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_mail_template], pull次数:[1], 总计条数:[0]
00:03:04.797 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_mail_template], pull次数:[1], 总计条数:[0], 最大base版本:[3]
00:03:04.797 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_sign_in_config]
00:03:04.797 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_sign_in_config], pull次数:[1]
00:03:04.801 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_sign_in_config], pull次数:[1], 总计条数:[0]
00:03:04.801 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_sign_in_config], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.801 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[bpm_form]
00:03:04.801 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[bpm_form], pull次数:[1]
00:03:04.805 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[bpm_form], pull次数:[1], 总计条数:[0]
00:03:04.805 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[bpm_form], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.805 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_quality_change_record]
00:03:04.805 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_quality_change_record], pull次数:[1]
00:03:04.809 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_quality_change_record], pull次数:[1], 总计条数:[0]
00:03:04.809 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_quality_change_record], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.809 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_users]
00:03:04.809 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_users], pull次数:[1]
00:03:04.880 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_users], pull次数:[1], 总计条数:[0]
00:03:04.880 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_users], pull次数:[1], 总计条数:[0], 最大base版本:[11]
00:03:04.881 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[bpm_process_instance_copy]
00:03:04.881 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[bpm_process_instance_copy], pull次数:[1]
00:03:04.885 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[bpm_process_instance_copy], pull次数:[1], 总计条数:[0]
00:03:04.885 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[bpm_process_instance_copy], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.885 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_prescription]
00:03:04.886 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_prescription], pull次数:[1]
00:03:04.890 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_prescription], pull次数:[1], 总计条数:[0]
00:03:04.890 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_prescription], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.890 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_mail_account]
00:03:04.890 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_mail_account], pull次数:[1]
00:03:04.964 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_mail_account], pull次数:[1], 总计条数:[0]
00:03:04.964 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_mail_account], pull次数:[1], 总计条数:[0], 最大base版本:[4]
00:03:04.964 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_hospital_department]
00:03:04.965 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_hospital_department], pull次数:[1]
00:03:04.969 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_hospital_department], pull次数:[1], 总计条数:[0]
00:03:04.969 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_hospital_department], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.969 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_tenant_package_relation]
00:03:04.969 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_tenant_package_relation], pull次数:[1]
00:03:04.973 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_tenant_package_relation], pull次数:[1], 总计条数:[0]
00:03:04.973 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_tenant_package_relation], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.973 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_doctor_work_record]
00:03:04.973 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_doctor_work_record], pull次数:[1]
00:03:04.977 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_doctor_work_record], pull次数:[1], 总计条数:[0]
00:03:04.977 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_doctor_work_record], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:04.977 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_operate_log]
00:03:04.977 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_operate_log], pull次数:[1]
00:03:05.048 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_operate_log], pull次数:[1], 总计条数:[0]
00:03:05.049 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_operate_log], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.049 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_role_menu]
00:03:05.049 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_role_menu], pull次数:[1]
00:03:05.120 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_role_menu], pull次数:[1], 总计条数:[0]
00:03:05.121 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_role_menu], pull次数:[1], 总计条数:[0], 最大base版本:[403]
00:03:05.121 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_signature_contract]
00:03:05.121 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_signature_contract], pull次数:[1]
00:03:05.129 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_signature_contract], pull次数:[1], 总计条数:[0]
00:03:05.129 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_signature_contract], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.130 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[bpm_process_listener]
00:03:05.130 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[bpm_process_listener], pull次数:[1]
00:03:05.136 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[bpm_process_listener], pull次数:[1], 总计条数:[0]
00:03:05.136 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[bpm_process_listener], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.136 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[medicare_merchant]
00:03:05.136 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[medicare_merchant], pull次数:[1]
00:03:05.153 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[medicare_merchant], pull次数:[1], 总计条数:[0]
00:03:05.153 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[medicare_merchant], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.153 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_pharmacist]
00:03:05.153 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_pharmacist], pull次数:[1]
00:03:05.158 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_pharmacist], pull次数:[1], 总计条数:[0]
00:03:05.158 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_pharmacist], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.158 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_patient_info]
00:03:05.158 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_patient_info], pull次数:[1]
00:03:05.162 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_patient_info], pull次数:[1], 总计条数:[0]
00:03:05.162 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_patient_info], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.162 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_tenant_package_cost_log]
00:03:05.162 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_tenant_package_cost_log], pull次数:[1]
00:03:05.166 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_tenant_package_cost_log], pull次数:[1], 总计条数:[0]
00:03:05.166 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_tenant_package_cost_log], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.166 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_address]
00:03:05.166 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_address], pull次数:[1]
00:03:05.170 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_address], pull次数:[1], 总计条数:[0]
00:03:05.171 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_address], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.171 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_user_post]
00:03:05.171 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_user_post], pull次数:[1]
00:03:05.240 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_user_post], pull次数:[1], 总计条数:[0]
00:03:05.240 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_user_post], pull次数:[1], 总计条数:[0], 最大base版本:[9]
00:03:05.241 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_oauth2_code]
00:03:05.241 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_oauth2_code], pull次数:[1]
00:03:05.316 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_oauth2_code], pull次数:[1], 总计条数:[0]
00:03:05.316 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_oauth2_code], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.317 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_tenant_package_cost]
00:03:05.317 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_tenant_package_cost], pull次数:[1]
00:03:05.321 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_tenant_package_cost], pull次数:[1], 总计条数:[0]
00:03:05.321 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_tenant_package_cost], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.321 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_info]
00:03:05.322 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_info], pull次数:[1]
00:03:05.326 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_info], pull次数:[1], 总计条数:[0]
00:03:05.326 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_info], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.326 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_tenant_pharmacist_relation]
00:03:05.326 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_tenant_pharmacist_relation], pull次数:[1]
00:03:05.331 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_tenant_pharmacist_relation], pull次数:[1], 总计条数:[0]
00:03:05.331 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_tenant_pharmacist_relation], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.331 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_record_detail]
00:03:05.331 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_record_detail], pull次数:[1]
00:03:05.337 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_record_detail], pull次数:[1], 总计条数:[0]
00:03:05.337 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_record_detail], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.337 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_tenant_transmission_service_pack_relation]
00:03:05.337 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_tenant_transmission_service_pack_relation], pull次数:[1]
00:03:05.342 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_tenant_transmission_service_pack_relation], pull次数:[1], 总计条数:[0]
00:03:05.342 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_tenant_transmission_service_pack_relation], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.342 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_transcoding]
00:03:05.342 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_transcoding], pull次数:[1]
00:03:05.347 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_transcoding], pull次数:[1], 总计条数:[0]
00:03:05.347 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_transcoding], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.347 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_social_user_bind]
00:03:05.348 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_social_user_bind], pull次数:[1]
00:03:05.422 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_social_user_bind], pull次数:[1], 总计条数:[0]
00:03:05.423 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_social_user_bind], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.423 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_prescription_template]
00:03:05.423 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_prescription_template], pull次数:[1]
00:03:05.427 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_prescription_template], pull次数:[1], 总计条数:[0]
00:03:05.427 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_prescription_template], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.427 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[bpm_oa_leave]
00:03:05.427 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[bpm_oa_leave], pull次数:[1]
00:03:05.431 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[bpm_oa_leave], pull次数:[1], 总计条数:[0]
00:03:05.431 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[bpm_oa_leave], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.431 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_mail_log]
00:03:05.432 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_mail_log], pull次数:[1]
00:03:05.506 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_mail_log], pull次数:[1], 总计条数:[0]
00:03:05.506 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_mail_log], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.506 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_doctor_video]
00:03:05.507 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_doctor_video], pull次数:[1]
00:03:05.511 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_doctor_video], pull次数:[1], 总计条数:[0]
00:03:05.511 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_doctor_video], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.511 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_api_error_log]
00:03:05.511 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_api_error_log], pull次数:[1]
00:03:05.587 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[infra_api_error_log], pull次数:[1], 总计条数:[0]
00:03:05.587 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_api_error_log], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.587 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_app_version_detail]
00:03:05.587 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_app_version_detail], pull次数:[1]
00:03:05.591 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_app_version_detail], pull次数:[1], 总计条数:[0]
00:03:05.591 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_app_version_detail], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.591 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_oauth2_client]
00:03:05.591 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_oauth2_client], pull次数:[1]
00:03:05.661 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_oauth2_client], pull次数:[1], 总计条数:[0]
00:03:05.661 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_oauth2_client], pull次数:[1], 总计条数:[0], 最大base版本:[4]
00:03:05.661 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_notify_message]
00:03:05.662 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_notify_message], pull次数:[1]
00:03:05.730 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_notify_message], pull次数:[1], 总计条数:[0]
00:03:05.730 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_notify_message], pull次数:[1], 总计条数:[0], 最大base版本:[9]
00:03:05.731 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_signature_ca_auth]
00:03:05.731 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_signature_ca_auth], pull次数:[1]
00:03:05.735 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_signature_ca_auth], pull次数:[1], 总计条数:[0]
00:03:05.735 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_signature_ca_auth], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.735 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_notice]
00:03:05.735 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_notice], pull次数:[1]
00:03:05.803 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_notice], pull次数:[1], 总计条数:[0]
00:03:05.804 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_notice], pull次数:[1], 总计条数:[0], 最大base版本:[2]
00:03:05.804 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_file_config]
00:03:05.804 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_file_config], pull次数:[1]
00:03:05.877 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[infra_file_config], pull次数:[1], 总计条数:[0]
00:03:05.877 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_file_config], pull次数:[1], 总计条数:[0], 最大base版本:[2]
00:03:05.877 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_notify_template]
00:03:05.877 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_notify_template], pull次数:[1]
00:03:05.951 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_notify_template], pull次数:[1], 总计条数:[0]
00:03:05.952 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_notify_template], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:05.952 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_post]
00:03:05.952 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_post], pull次数:[1]
00:03:06.024 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_post], pull次数:[1], 总计条数:[0]
00:03:06.024 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_post], pull次数:[1], 总计条数:[0], 最大base版本:[4]
00:03:06.024 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_doctor_status]
00:03:06.024 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_doctor_status], pull次数:[1]
00:03:06.031 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_doctor_status], pull次数:[1], 总计条数:[0]
00:03:06.031 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_doctor_status], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.031 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_profession_identification]
00:03:06.031 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_profession_identification], pull次数:[1]
00:03:06.036 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_profession_identification], pull次数:[1], 总计条数:[0]
00:03:06.036 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_profession_identification], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.037 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_prescription_audit]
00:03:06.037 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_prescription_audit], pull次数:[1]
00:03:06.041 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_prescription_audit], pull次数:[1], 总计条数:[0]
00:03:06.041 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_prescription_audit], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.041 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[member_config]
00:03:06.042 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[member_config], pull次数:[1]
00:03:06.045 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[member_config], pull次数:[1], 总计条数:[0]
00:03:06.046 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[member_config], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.046 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_hospital]
00:03:06.046 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_hospital], pull次数:[1]
00:03:06.050 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_hospital], pull次数:[1], 总计条数:[0]
00:03:06.050 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_hospital], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.050 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_sms_log]
00:03:06.050 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_sms_log], pull次数:[1]
00:03:06.120 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_sms_log], pull次数:[1], 总计条数:[0]
00:03:06.120 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_sms_log], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.120 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_hospital_setting]
00:03:06.120 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_hospital_setting], pull次数:[1]
00:03:06.125 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_hospital_setting], pull次数:[1], 总计条数:[0]
00:03:06.125 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_hospital_setting], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.125 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_diagnosis]
00:03:06.125 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_diagnosis], pull次数:[1]
00:03:06.129 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_diagnosis], pull次数:[1], 总计条数:[0]
00:03:06.129 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_diagnosis], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.129 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_signature_platform]
00:03:06.129 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_signature_platform], pull次数:[1]
00:03:06.133 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_signature_platform], pull次数:[1], 总计条数:[0]
00:03:06.133 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_signature_platform], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.133 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_qualification_info]
00:03:06.133 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_qualification_info], pull次数:[1]
00:03:06.137 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_qualification_info], pull次数:[1], 总计条数:[0]
00:03:06.137 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_qualification_info], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.137 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_im_user]
00:03:06.137 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_im_user], pull次数:[1]
00:03:06.141 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_im_user], pull次数:[1], 总计条数:[0]
00:03:06.141 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_im_user], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.141 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_stdlib]
00:03:06.141 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_stdlib], pull次数:[1]
00:03:06.145 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_stdlib], pull次数:[1], 总计条数:[0]
00:03:06.145 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_stdlib], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.145 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_oauth2_refresh_token]
00:03:06.145 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_oauth2_refresh_token], pull次数:[1]
00:03:06.218 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_oauth2_refresh_token], pull次数:[1], 总计条数:[0]
00:03:06.218 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_oauth2_refresh_token], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.218 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[bpm_category]
00:03:06.218 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[bpm_category], pull次数:[1]
00:03:06.224 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[bpm_category], pull次数:[1], 总计条数:[0]
00:03:06.224 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[bpm_category], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.224 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_file_content]
00:03:06.224 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_file_content], pull次数:[1]
00:03:06.299 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[infra_file_content], pull次数:[1], 总计条数:[0]
00:03:06.299 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_file_content], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.299 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_complain]
00:03:06.299 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_complain], pull次数:[1]
00:03:06.303 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_complain], pull次数:[1], 总计条数:[0]
00:03:06.303 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_complain], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.303 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[infra_job_log]
00:03:06.303 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[infra_job_log], pull次数:[1]
00:03:06.374 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[infra_job_log], pull次数:[1], 总计条数:[0]
00:03:06.374 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[infra_job_log], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.374 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_tenant_user_relation]
00:03:06.374 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_tenant_user_relation], pull次数:[1]
00:03:06.379 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[system_tenant_user_relation], pull次数:[1], 总计条数:[0]
00:03:06.379 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_tenant_user_relation], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.379 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_dict_data]
00:03:06.379 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_dict_data], pull次数:[1]
00:03:06.453 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_dict_data], pull次数:[1], 总计条数:[0]
00:03:06.453 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_dict_data], pull次数:[1], 总计条数:[0], 最大base版本:[418]
00:03:06.453 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_oa_white_list]
00:03:06.453 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_oa_white_list], pull次数:[1]
00:03:06.457 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_oa_white_list], pull次数:[1], 总计条数:[0]
00:03:06.457 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_oa_white_list], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.458 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_doctor_quick_reply_msg]
00:03:06.458 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_doctor_quick_reply_msg], pull次数:[1]
00:03:06.461 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_doctor_quick_reply_msg], pull次数:[1], 总计条数:[0]
00:03:06.461 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_doctor_quick_reply_msg], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.461 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_product_category]
00:03:06.461 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_product_category], pull次数:[1]
00:03:06.465 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_product_category], pull次数:[1], 总计条数:[0]
00:03:06.465 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_product_category], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.465 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_doctor_practice]
00:03:06.465 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_doctor_practice], pull次数:[1]
00:03:06.469 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_doctor_practice], pull次数:[1], 总计条数:[0]
00:03:06.469 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_doctor_practice], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.469 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_third_party_pre_inquiry_detail]
00:03:06.470 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_third_party_pre_inquiry_detail], pull次数:[1]
00:03:06.473 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_third_party_pre_inquiry_detail], pull次数:[1], 总计条数:[0]
00:03:06.474 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_third_party_pre_inquiry_detail], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.474 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_role]
00:03:06.474 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_role], pull次数:[1]
00:03:06.545 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_role], pull次数:[1], 总计条数:[0]
00:03:06.545 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_role], pull次数:[1], 总计条数:[0], 最大base版本:[5]
00:03:06.546 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_doctor_audited_record]
00:03:06.546 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_doctor_audited_record], pull次数:[1]
00:03:06.551 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_doctor_audited_record], pull次数:[1], 总计条数:[0]
00:03:06.551 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_doctor_audited_record], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.551 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_prescription_external]
00:03:06.551 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_prescription_external], pull次数:[1]
00:03:06.556 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_prescription_external], pull次数:[1], 总计条数:[0]
00:03:06.556 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_prescription_external], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.556 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[saas_inquiry_record]
00:03:06.556 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[saas_inquiry_record], pull次数:[1]
00:03:06.561 ERROR [l.CycleLoopPullSubscriberExecutor:69] [] - 数据同步,从云端pull数据失败, 机构号:[1], 表名:[saas_inquiry_record], pull次数:[1], 总计条数:[0]
00:03:06.561 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[saas_inquiry_record], pull次数:[1], 总计条数:[0], 最大base版本:[0]
00:03:06.561 DEBUG [l.CycleLoopPullSubscriberExecutor:63] [] - 数据同步,机构号:[1],开始同步表名:[system_sms_template]
00:03:06.561 DEBUG [l.CycleLoopPullSubscriberExecutor:65] [] - 数据同步,开始从云端pull数据, 机构号:[1], 表名:[system_sms_template], pull次数:[1]
00:03:06.637 DEBUG [l.CycleLoopPullSubscriberExecutor:76] [] - 数据同步,从云端pull数据结束, 机构号:[1], 表名:[system_sms_template], pull次数:[1], 总计条数:[0]
00:03:06.637 DEBUG [l.CycleLoopPullSubscriberExecutor:96] [] - 数据同步,表数据同步完成, 机构号:[1], 表名:[system_sms_template], pull次数:[1], 总计条数:[0], 最大base版本:[13]
00:03:06.637 INFO  [.CycleLoopPullSubscriberExecutor:112] [] - 数据同步,全部表数据同步完成, 机构号:[1], 最后一个表名:[system_sms_template], 总同步表次数:[136]
00:03:06.637 DEBUG [aas.localserver.utils.EventPusher:39] [] - source [com.xyy.saas.datasync.client.worker.pull.CycleLoopPullSubscriberExecutor@6cbcfadd] syncPost event[com.xyy.saas.datasync.client.event.PullDataFinishedEvent]=[{}]
00:03:06.637 INFO  [ingleThreadPullSubscriberExecutor:44] [] - 数据同步,单线程拉取任务结束,释放信号量, 机构号:[1], 耗时:[3735]ms.
00:03:06.637 INFO  [tasync.client.worker.DataSyncTask:95] [] - 开始下次数据同步任务 DataSyncTask:[pull-task], TenantId: [1], interval : [300]秒

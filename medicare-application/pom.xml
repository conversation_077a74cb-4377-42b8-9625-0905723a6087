<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>org.example</groupId>
  <artifactId>medicare-application</artifactId>
  <version>1.0-SNAPSHOT</version>

  <name>medicare-application</name>
  <!-- FIXME change it to the project's website -->
  <url>http://www.example.com</url>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>web-container</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>
<!--    <dependency>-->
<!--      <groupId>org.projectlombok</groupId>-->
<!--      <artifactId>lombok</artifactId>-->
<!--      <version>1.18.16</version>-->
<!--      <scope>compile</scope>-->
<!--    </dependency>-->
  </dependencies>

  <build>
    <pluginManagement><!-- lock down plugins versions to avoid using Maven defaults (may be moved to parent pom) -->
    </pluginManagement>
  </build>
</project>

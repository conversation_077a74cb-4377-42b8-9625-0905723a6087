package com.xyy.saas.localserver.medicare.dsl.config.base;

import cn.hutool.http.Method;
import com.xyy.saas.localserver.medicare.dsl.format.FormatType;
import com.xyy.saas.localserver.medicare.dsl.protocol.ProtocolMode;
import com.xyy.saas.localserver.medicare.dsl.protocol.request.BodyStrategy;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * @Desc 协议配置, 处理各个地区对接第三方差异化的协议
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/07/24 18:04
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ContractConfig extends DSLConfig {

    private String name;

    private ProtocolMode protocol;

    private BodyStrategy bodyStrategy;

    private FormatType format;

    private String domain;

    private String path;

    private Map<String, String> param;

    private List<FunctionConfig> functions;

    /* 是否有公共参数*/
    private boolean common = true;

    /* 公共参数的文件名*/
    private String commonName = "common";

    private CommonConfig commonConfig;

    private FunctionConfig.ResultConfig result;

    private Map<String, ?> outputFields;

    private Map<String, String> global;

    /* 清空当前Context上下文,getToken等前置全局接口不清空*/
    private boolean clearContext = true;

    @Data
    public static class CommonConfig {

        /* 请求地址*/
        private String domain;

        private String path;

        /* 协议*/
        private ProtocolMode protocol;

        /*body策略 */
        private BodyStrategy bodyStrategy;

        private Map<String, String> header;

        /* 输入参数*/
        private Map<String, Object> input;

        /* 加密策略*/
        private EncryptConfig encrypt;

        /* 解密策略*/
        private DecryptConfig decrypt;

        /* 前置表达式*/
        private String suffixedInExpression;

        /* 后置表达式*/
        private String prefixedOutExpression;

        /* 输出参数*/
        private Map<String, String> output;

        private FunctionConfig.ResultConfig result;

    }

    /**
     * 加密对象
     */
    @Data
    public static class EncryptConfig {

        /*配置对单独的header值加密*/
        private Map<String, String> header;

        /*配置对单独的Body的某个值加密*/
        private Map<String, String> bodys;

        /*配置对整个的Body加密*/
        private String body;

        /*Body参数加密后放入header*/
        private Map<String, String> bodyInHeader;

    }

    @Data
    public static class DecryptConfig {

        /*配置对单独的header值解密*/
        private Map<String, String> header;

        /*配置对单独的Body的某个值解密*/
        private Map<String, String> bodys;

        /*配置对整个的Body解密*/
        private String body;

    }


    @Data
    public static class FunctionConfig {

        private String name;

        private Boolean enable;

        /*请求地址或者域名*/
        private String domain;

        /*路径*/
        private String path = "";

        /*执行条件*/
        private String condition;

        /* 协议*/
        private ProtocolMode protocol = ProtocolMode.http;


        /*是否循环调用*/
        private boolean cycle;

        /*最大循环次数,TODO 可能需要配成String 根据逻辑去解析具体循环的最大次数*/
        private int maxCount = 10;

        /*循环退出条件*/
        private String end;


        private BodyStrategy bodyStrategy = BodyStrategy.jsonNode;

//        private Map<String, String> headers;

//        private Map<String, Object> request;

        private RequestConfig request;

        /* 前置表达式*/
        private String suffixedInExpression;

        /* 后置表达式*/
        private String prefixedOutExpression;


        private Map<String, Object> response;

        /*接口默认30秒超时*/
        private int timeout = 30;

        private ResultConfig result;

        @Data
        public static class RequestConfig {

            private FormatType format;

            private Method method = Method.POST;

            private Map<String, String> header;

            private Map<String, Object> body;

        }

        @Data
        public static class RetryConfig {

            private Boolean enable;

            private String condition;

            private String expression;

        }

        @Data
        public static class ResultConfig {

            /*成功的条件*/
            private String success;

            /*不成功是否跳过, 大部分场景是不成功，提示msg*/
            private boolean skip = false;

            /*不成功后的提示语*/
            private String tips;

            /*hook执行条件*/
            private String hookCondition;

            /*hook*/
            private List<String> hook;

            /*重试策略*/
            private RetryConfig retry;

        }
    }
}

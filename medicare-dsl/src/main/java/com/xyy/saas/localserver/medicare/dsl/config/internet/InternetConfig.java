package com.xyy.saas.localserver.medicare.dsl.config.internet;

import lombok.Data;

/**
 * 网络配置优先级：
 * 收银端配置(如果配置了优先级最高，因为是客户配置的，配置错了可以清空，默认显示医保局绑定网络)
 * function配置网络
 * yml主配置网络
 * 医保局绑定网络
 *
 * @Desc 网络配置
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/03 19:57
 */
@Data
public class InternetConfig {

    /**
     * 配置类型,
     */
    private InternetType type;

    /**
     * 网络地址
     */
    private String url;

    /**
     * 第三方环境
     */
    private evnType evn;

    public enum InternetType {

        /**
         * 读卡
         */
        readcard,


        /**
         * 电子凭证
         */
        nationectrans,

        /**
         * 核心业务
         */
        business,

        /**
         * 电子处方中心
         */
        elec_prescription,

        /**
         * 进销存
         */
        purchase_sell_stock,

        /**
         * 其他
         */
        other,
    }

    public enum evnType {

        prod,

        test,

    }


}

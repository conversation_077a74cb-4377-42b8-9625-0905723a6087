signin:
  dslType: contract
  enable: true
  name: '签到'
  protocol: http
  format: json
  domain: 127.0.0.1

  path: /fsi/api/signInSignOutService/signIn
  input:
    header:
      "infno": "'9001'",
      "mdtrtarea_admvs": "data.merchantInfo.merchantAreaCode",
      "insuplc_admdvs": "data.merchantInfo.merchantAreaCode",
      "opter": "data.operatorInfo.operatorId",
      "opter_name": "data.operatorInfo.operatorName",
      "fixmedins_code": "data.merchantInfo.medicareInstitutionCode",
      "fixmedins_name": "data.merchantInfo.medicareInstitutionName",
      "sign_no": "data.operatorInfo.periodNo"
    basebody:

    body:
      "opterNo": "data.operatorInfo.operatorCode"
      "mac": "data.merchantInfo.macAddress"
      "ip": "data.merchantInfo.privateNetworkIp"
  output:
    "code": "extendMap['out_1']['infcode']",
    "msg": "extendMap['out_1']['err_msg']",
    "result": "extendMap['out_1']['output']['signinoutb']['sign_no']"


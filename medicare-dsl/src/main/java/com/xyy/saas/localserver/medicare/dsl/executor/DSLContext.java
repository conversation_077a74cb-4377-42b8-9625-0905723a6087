package com.xyy.saas.localserver.medicare.dsl.executor;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import com.xyy.saas.localserver.medicare.dsl.config.base.ContractConfig;
import com.xyy.saas.localserver.medicare.dsl.config.internet.InternetConfig;
import com.xyy.saas.localserver.medicare.dsl.executor.value.*;
import com.xyy.saas.localserver.medicare.dsl.parse.JsonParameterParser;
import com.xyy.saas.localserver.medicare.dsl.parse.ObjectValueParser;
import com.xyy.saas.localserver.medicare.dsl.protocol.response.ProtocolResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Supplier;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/07/26 11:40
 */
@Slf4j
public class DSLContext {

    /* 全局参数*/
    @Getter
    private static Map<String, String> globalContext = new HashMap<>();

    /*店信息*/
    @Getter
    private Drugstore drugstore;

    /*设备信息*/
    @Getter
    private Device device;

    /*操作人信息*/
    @Getter
    private Operator operator;

    /*当前参保人信息*/
    @Getter
    private Insurant insurant;
    private final ObjectValueParser valueParser = new JsonParameterParser();

    /*动态业务*/
    @Getter
    @Setter
    public DynamicBusiness business;

    /**
     * 一次yml流程公共的入参,已是解析的值
     */
    @Getter
    private Map<String, Object> paramValue = new HashMap<>();

    /**
     * 公共的请求header入参,已是EL解析后的值
     */
    @Getter
    private Map<String, String> commonHeader = new LinkedHashMap<>();

    /**
     * 公共的请求body入参,已是EL解析后的值
     */
    @Getter
    private Map<String, Object> commonInput = new LinkedHashMap<>();

    /*尝试过解析，但是未解析出来的公共请求参数*/
    @Getter
    private Map<String, Object> unresolvedCommonInput = new HashMap<>();

    @Getter
    private Map<String, String> commonOutput = new LinkedHashMap<>();

    /**
     * 每个请求的入参
     */
    @Getter
    private List<InputObject> input = new ArrayList<>();

    /**
     * 当前的Input参数
     */
    @Getter
    private InputObject currentInput;


    @Getter
    private Map<Integer, List<Map<String, Object>>> outputAll = new LinkedHashMap<>();

    /**
     * 请求的出差
     */
    @Getter
    private Map<String, Object> output = new LinkedHashMap<>();

    /**
     * 获取网络配置
     */

    public static InternetConfig getInternetConfig() {
        //TODO
        return null;
    }

    /*参保人信息*/
    @Getter
    private Visit visit;

    public Object getParamValue(String k) {
        return paramValue.get(k);
    }

//    public void putParamValue(String k, String v) {
//        instance.paramValue.put(k, v);
//    }

    public InputObject getInput(int i) {
        return input.get(i);
    }

    //TODO  初始化&变更

    public <T> DSLContext builder(Supplier<T> supplier) {
        T value = supplier.get();
        if (value instanceof Drugstore) {
            this.drugstore = (Drugstore) value;
        } else if (value instanceof Operator) {
            this.operator = (Operator) value;
        } else if (value instanceof Insurant) {
            this.insurant = (Insurant) value;
        } else if (value instanceof Device) {
            this.device = (Device) value;
        } else if (value instanceof Visit) {
            this.visit = (Visit) value;
        } else if (value instanceof DynamicBusiness) {
            this.business = (DynamicBusiness) value;
        } else {
            throw new IllegalArgumentException("builder 传入非法类型");
        }
        return this;
    }

    /**
     * 完成一次function之后的操作
     */
    public void oneFunctionFinish() {
        this.currentInput = null;
    }

    /**
     * 清除上下文信息
     */
    public void clear() {
        this.paramValue.clear();
        this.commonHeader.clear();
        this.commonInput.clear();
        this.commonOutput.clear();
        this.input.clear();
        this.output.clear();
        this.outputAll.clear();
        this.business = null;
        this.currentInput = null;
        this.unresolvedCommonInput.clear();
    }

    /**
     * 解析公共参数
     *
     * @param config
     */
    public void parseCommonParamValue(ContractConfig config) {
        Map<String, String> paramConfig = config.getParam();
        if (MapUtil.isNotEmpty(paramConfig)) {
            paramConfig.forEach((k, v) -> paramValue.put(k, valueParser.parseValue(v, this)));
        }
        if (!config.isCommon()) {
            return;
        }
        //解析公共body参数
        ContractConfig.CommonConfig commonConfig = config.getCommonConfig();
        if (commonConfig == null) {
            return;
        }
        if (MapUtil.isNotEmpty(commonConfig.getHeader())) {
            commonConfig.getHeader().forEach((k, v) -> {
                String dataValue = valueParser.parseValue(v, this);
                commonHeader.put(k, dataValue);
            });
        }
        //解析Common的Input参数,如果解析失败就放入unresolvedCommonInput，等待inputObject入参结构体构造完成后再次解析
        if (MapUtil.isNotEmpty(commonConfig.getInput())) {
            commonConfig.getInput().forEach((k, v) -> {
                valueParser.parseObject(v, this, (value) -> {
                    commonInput.put(k, value);
                    return value;
                }, k, unresolvedCommonInput);
//                String dataValue = valueParser.parseValue(v, this, k, unresolvedCommonInput);
//                commonInput.put(k, dataValue);
            });
        }
    }


    /**
     * 解析结束条件结果
     *
     * @param condition
     * @return
     */
    public Boolean parseConditionResult(String condition) {
        return valueParser.parseResult(condition, this);
    }

    public void executeEL(String condition) {
        valueParser.executeEL(condition, this);
    }

    /**
     * 解析值，一般用于给提示
     *
     * @param expression
     * @return
     */
    public String parseValue(String expression) {
        return valueParser.parseValue(expression, this);
    }

    public String parseValue(String expression, String replaceValue) {
        return valueParser.parseValue(expression, replaceValue, this);
    }

    public Map<String, Object> parseResult(String exp, Map<String, Object> context) {
        return valueParser.parseToMapFromContext(exp, context);
    }

    /**
     * 解析单个函数的请求Input参数值
     *
     * @param functionConfig
     */
    public InputObject parseFunctionInputParamValue(ContractConfig contractConfig, ContractConfig.FunctionConfig functionConfig) {
        InputObject inputObject = new InputObject(contractConfig, functionConfig);
        ContractConfig.FunctionConfig.RequestConfig request = functionConfig.getRequest();
        //解析body参数
        if (MapUtil.isNotEmpty(request.getBody())) {
            Map<String, Object> functionBodyValue = new LinkedHashMap<>();
            request.getBody().forEach((key, config) -> {
                valueParser.parseObject(config, this, (value) -> {
                    functionBodyValue.put(key, value);
                    return value;
                });
            });
            inputObject.setBodys(functionBodyValue);
        }
        //解析header参数
        if (MapUtil.isNotEmpty(request.getHeader())) {
            Map<String, String> functionHeaderValue = new LinkedHashMap<>();
            request.getHeader().forEach((key, config) -> {
                valueParser.parseObject(config, this, (value) -> {
                    functionHeaderValue.put(key, (String) value);
                    return value;
                });
            });
            inputObject.setHeader(functionHeaderValue);
        }
        input.add(inputObject);
        currentInput = inputObject;
        return inputObject;
    }

    /**
     * 在InputObject请求入参结构体生成之后再触发一次之前未解析到的值解析一次
     */
    public void againParseUnresolvedParamValue() {
        if (!CollectionUtils.isEmpty(unresolvedCommonInput)) {
            Iterator<Map.Entry<String, Object>> iterator = unresolvedCommonInput.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, Object> entry = iterator.next();
                valueParser.parseObject(entry.getValue(), this, (value) -> {
                    commonInput.put(entry.getKey(), value);
                    return value;
                });
                iterator.remove();
//                commonInput.put(entry.getKey(), valueParser.parseValue(entry.getValue(), this));
//                iterator.remove();

            }
        }
    }


    /**
     * 解析出参绑定到context
     *
     * @param config
     * @param functionConfig
     * @param response
     */
    public void parseOutputParamValue(ContractConfig config, ContractConfig.FunctionConfig functionConfig, ProtocolResponse response) {
        int index = config.getFunctions().indexOf(functionConfig);
        //没有初始化functionsOutputMap
        List<Map<String, Object>> functionsOutputMap = outputAll.computeIfAbsent(index, key -> new ArrayList<>());
        functionsOutputMap.add(response.getResultMap());

        Map<String, Object> resultMap = response.getResultMap();
        // 将入参的业务参数放入出参解析数据
        resultMap.put("business", DSLContextHolder.getContext().getBusiness());

        //先处理公共出参config
        Map<String, Object> functionResponseConfig = functionConfig.getResponse();
        if (config.isCommon()) {
            Map<String, String> outputCommonConfig = config.getCommonConfig().getOutput();
            //有公共响应参数优先解析公共响应参数
            Optional.ofNullable(outputCommonConfig).ifPresent(op -> op.forEach((k, v) -> {
                //当是普通值(String\Number)直接解析,common只会套2层Map
                if (StringUtils.hasLength(v)) {
                    output.put(k, valueParser.parseToValueFromContext(v, resultMap));
                    return;
                }
                //当在common.yml里面配置的output节点的key的value是空对象时,表示需要解析到function子函数
                Object object = functionResponseConfig.get(k);
                if (object == null) {
                    //子协议，没有配置common的响应节点 .
                    log.error("配置错误common配置的output节点的key:{},在解析function的时候response没有配置这个key", k);
                    return;
                }
                //解析function响应参数
                cycleFunctionOutputParamValue(k, object, resultMap.get(k));
            }));
        } else {
//            cycleFunctionOutputParamValue(null, functionResponseConfig, resultMap);
        }
        cycleFunctionOutputParamValue(null, functionResponseConfig, resultMap);
    }


    /**
     * 循环解析出参值
     *
     * @param commonKey
     * @param functionResponseConfig
     * @param resultMap
     * @param <T>
     */
    private <T extends Iterable> void cycleFunctionOutputParamValue(String commonKey, Object functionResponseConfig, Object resultMap) {
        if (functionResponseConfig == null || resultMap == null || "null".equals(resultMap.toString()) || "".equals(resultMap.toString())) {
            return;
        }
        if (functionResponseConfig instanceof Map<?, ?> functionResponseConfigMap) {
            ((Map<String, ?>) functionResponseConfig).forEach((k, v) -> {
                if (v instanceof Iterable) {
                    cycleFunctionOutputParamValue(k, v, ((Map) resultMap).get(k));
                    return;
                }
                if (v instanceof Map) {
                    cycleFunctionOutputParamValue(k, v, ((Map) resultMap).get(k));
                    return;
                }
                //始终保存一份到output
                output.put(k, valueParser.parseToValueFromContext((String) v, (Map<String, Object>) resultMap));
            });
            output.put(commonKey, resultMap);
            return;
        }
        if (functionResponseConfig instanceof List<?> functionResponseConfigs) {
            output.put(commonKey, resultMap);
            for (int i = 0; i < functionResponseConfigs.size() && i < ((List) resultMap).size(); i++) {
                cycleFunctionOutputParamValue(null, functionResponseConfigs.get(i), ((List) resultMap).get(i));
            }
        }
    }

    public void parseGlobalParam(ContractConfig config) {
        Map<String, String> globalMap = config.getGlobal();
        if (MapUtil.isEmpty(globalMap)) {
            return;
        }
        globalMap.forEach((k, v) -> {
            Object object = valueParser.parseToValueFromContext(v, output);
            if (object == null) {
                throw new IllegalArgumentException("key:" + k + ", expression:" + v + " is null in output");
            }
            globalContext.put(k, object.toString());
        });
    }


    public <T> T serializeFromOutput(Class<T> clazz) {
        if (clazz == null) {
            return null;
        }
//        BeanUtil.mapToBean(output, clazz, true, CopyOptions.create().setFieldMapping(mapping));
        T result = BeanUtil.copyProperties(output, clazz);
        return result;
    }


}

package com.xyy.saas.localserver.medicare.dsl.executor;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/09/07 15:58
 */
public class DSLContextHolder {

    private static final ThreadLocal<DSLContext> contextHolder = new ThreadLocal<>();

    public static DSLContext getContext() {
        DSLContext dslContext = contextHolder.get();
        if (dslContext == null) {
            dslContext = new DSLContext();
            contextHolder.set(dslContext);
        }
        return dslContext;
    }

    public static void clear() {
        DSLContext dslContext = contextHolder.get();
        if (dslContext != null) {
            dslContext.clear();
        }
        contextHolder.remove();
    }
}

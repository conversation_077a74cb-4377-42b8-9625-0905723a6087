package com.xyy.saas.localserver.medicare.dsl.executor;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.http.GlobalInterceptor;
import cn.hutool.http.HttpGlobalConfig;
import com.xyy.saas.localserver.medicare.dsl.config.base.ContractConfig;
import com.xyy.saas.localserver.medicare.dsl.config.internet.InternetConfig;
import com.xyy.saas.localserver.medicare.dsl.executor.log.ContractInvokeContext;
import com.xyy.saas.localserver.medicare.dsl.executor.log.ContractInvokeContextHolder;
import com.xyy.saas.localserver.medicare.dsl.executor.log.ContractInvokeLog;
import com.xyy.saas.localserver.medicare.dsl.executor.value.InputObject;
import com.xyy.saas.localserver.medicare.dsl.parse.JsonParameterParser;
import com.xyy.saas.localserver.medicare.dsl.parse.ObjectValueParser;
import com.xyy.saas.localserver.medicare.dsl.protocol.DllProtocolClient;
import com.xyy.saas.localserver.medicare.dsl.protocol.HttpProtocalClient;
import com.xyy.saas.localserver.medicare.dsl.protocol.ProtocolClient;
import com.xyy.saas.localserver.medicare.dsl.protocol.WebserviceProtocalClient;
import com.xyy.saas.localserver.medicare.dsl.protocol.response.ProtocolResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * @Desc DSL执行器
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/03 19:47
 */
@Slf4j
public class DSLExecutor {

    public static final DSLExecutor instance = new DSLExecutor();

    private static final ObjectValueParser valueParser = new JsonParameterParser();

    private DSLExecutor() {
        // 设置hutool HttpUtil的request请求参数打印
        GlobalInterceptor.INSTANCE.addRequestInterceptor(httpObj -> log.info(String.valueOf(httpObj)));
        // 设置hutool HttpUtil的response参数打印
        GlobalInterceptor.INSTANCE.addResponseInterceptor(httpObj -> log.info(String.valueOf(httpObj)));
        // 设置hutool HttpUtil的连接超时时间、读取响应超时时间
        HttpGlobalConfig.setTimeout(10000);
    }

    /**
     * 协议调用
     *
     * @param config 协议配置
     * @param clazz  反序列化对象
     * @param <T>    泛型参数
     * @return 返回的业务对象
     */
    public <T> T contractInvoker(ContractConfig config, Class<T> clazz, Object... args) {

        InternetConfig internetConfig = DSLContext.getInternetConfig();
        //设置网络地址，如果没配置取internetConfig的网络地址
        if (internetConfig != null && !StringUtils.hasLength(config.getDomain())) {
            //yml配置,优于外部传入(如:后台医保局绑定的网络配置，收银端界面配置)
            config.setDomain(internetConfig.getUrl());
        }
        DSLContext context = DSLContextHolder.getContext();
        try {
            //构建记录请求日志上下文
            ContractInvokeContext invokeContext = ContractInvokeContextHolder.getContext();
            //优先解析公共参数
            context.parseCommonParamValue(config);
            // 循环解析 ，config -> parse  -> 可执行的逻辑
            List<ContractConfig.FunctionConfig> functions = config.getFunctions();
            for (int i = 0; i < functions.size(); i++) {
                ContractConfig.FunctionConfig functionConfig = functions.get(i);
                //覆盖参数
                coverConfig(config, functionConfig);
                //检查执行条件
                if (!checkCondition(functionConfig)) {
                    continue;
                }
                //请求参数
                InputObject inputObject = context.parseFunctionInputParamValue(config, functionConfig);
                // 如果是循环接口就设置最大调用次数，否则最大次数就是1
                int maxCount = functionConfig.isCycle() ? functionConfig.getMaxCount() : 1;
                int retryCount = 0;
                //再次解析之前未解析到的参数值
                context.againParseUnresolvedParamValue();
                ProtocolClient client = null;
                for (int count = 0; count < maxCount; count++) {
                    //组装协议调用第三方
                    client = switch (functionConfig.getProtocol()) {
                        case dll -> DllProtocolClient.builder().inputObject(inputObject).build(); //TODO
                        case webservice -> WebserviceProtocalClient.builder().inputObject(inputObject).build(); //TODO
                        //http是默认的
                        default ->
                                HttpProtocalClient.builder().inputObject(inputObject).bodyStrategy(functionConfig.getBodyStrategy()).build();
                    };
                    log.debug("开始生成报文并执行Contract:{},第{}个Function,第{}次", config.getName(), i + 1, count + 1);


                    //生成客户端报文
                    ProtocolClient generateBody = client.toGenerateBody();

                    // 记录请求入参
                    ContractInvokeLog invokeLog = recordConcatLog(invokeContext, functionConfig, generateBody.getInputObject(), config);

                    // 执行请求
                    ProtocolResponse response = generateBody.toInvoker();

                    //记录请求出参
                    invokeLog.setResponseBody(response.getBody());

                    //处理系统请求异常，比如状态码不是200
                    requestResultHandler(functionConfig, response);

                    client.parseResponseBody();
                    //解析请求并且绑定到context
                    context.parseOutputParamValue(config, functionConfig, response);

                    //解析后置表达式
                    suffixedInExpressionHandler(config, functionConfig, response);
                    //处理结果集
                    boolean retry = businessResultHandler(functionConfig, response);
                    if (retry) {
                        //重试一次协议
                        maxCount++;
                    }
                    bindingOutputToContext(config, functionConfig, response);

                    //解析第三方返回的协议
                    //TODO
                    //在循环调用中，如果配置了结束条件end,判断是否退出循环，一般退出条件是等于什么值,或者不等于值
                    if (functionConfig.isCycle() && StringUtils.hasLength(functionConfig.getEnd())) {
                        Boolean end = context.parseConditionResult(functionConfig.getEnd());
                        if (BooleanUtil.isTrue(end)) {
                            break;
                        }
                    }
                }
                context.oneFunctionFinish();

            }
            //解析全局对象
            context.parseGlobalParam(config);
            return context.serializeFromOutput(clazz);
        } finally {
            if (config.isClearContext()) {
                DSLContextHolder.clear();
            }
        }

    }

    /**
     * 检查条件是否通过
     *
     * @param functionConfig
     * @return
     */
    private boolean checkCondition(ContractConfig.FunctionConfig functionConfig) {
        if (functionConfig.getEnable() != null && BooleanUtil.isFalse(functionConfig.getEnable())) {
            return false;
        }
        //判断执行条件
        if (StringUtils.hasLength(functionConfig.getCondition())) {
            Boolean result = DSLContextHolder.getContext().parseConditionResult(functionConfig.getCondition());
            return BooleanUtil.isTrue(result);
        }
        return true;
    }


    /**
     * 覆盖Function的相关参数,优先级按照就近原则，functionConfig->contractConfig->commonConfig
     *
     * @param config
     * @param functionConfig
     */
    private void coverConfig(ContractConfig config, ContractConfig.FunctionConfig functionConfig) {
        ContractConfig.CommonConfig commonConfig = config.getCommonConfig();
        if (functionConfig.getEnable() == null) { // 没有值的时候设置
            if (config.getEnable() != null) { //优先设置ContractConfig里面填了值的场景
                functionConfig.setEnable(config.getEnable());
            }
        }
        //设置域名地址和端口
        if (!StringUtils.hasLength(functionConfig.getDomain())) { // 没有值的时候设置
            if (StringUtils.hasLength(config.getDomain())) { //优先设置ContractConfig里面填了值的场景
                functionConfig.setDomain(config.getDomain());
            } else if (config.isCommon() && StringUtils.hasLength(commonConfig.getDomain())) {
                functionConfig.setDomain(commonConfig.getDomain());
            } else {
                log.error("config‘Domain is null");
            }
        }
        //设置请求的path
        if (!StringUtils.hasLength(functionConfig.getPath())) { // 没有值的时候设置
            if (StringUtils.hasLength(config.getPath())) { //优先设置ContractConfig里面填了值的场景
                functionConfig.setPath(config.getPath());
            } else if (config.isCommon() && StringUtils.hasLength(commonConfig.getPath())) {
                functionConfig.setPath(commonConfig.getPath());
            } else {
                log.error("config‘path is null");
            }
        }
        //设置协议
        if (functionConfig.getProtocol() == null) {
            if (config.getProtocol() != null) {
                functionConfig.setProtocol(config.getProtocol());
            } else if (config.isCommon() && commonConfig.getProtocol() != null) {
                functionConfig.setProtocol(commonConfig.getProtocol());
            } else {
                log.error("config‘Protocol is null");
            }
        }
        //设置body策略
        if (functionConfig.getBodyStrategy() == null) {
            if (config.getBodyStrategy() != null) {
                functionConfig.setBodyStrategy(config.getBodyStrategy());
            } else if (config.isCommon() && commonConfig.getBodyStrategy() != null) {
                functionConfig.setBodyStrategy(commonConfig.getBodyStrategy());
            } else {
                log.error("config‘BodyStrategy is null");
            }
        }
        //设置Result策略
        if (functionConfig.getResult() == null) {
            if (config.getResult() != null) {
                functionConfig.setResult(config.getResult());
            } else if (config.isCommon() && commonConfig.getResult() != null) {
                functionConfig.setResult(commonConfig.getResult());
            }
//            else {
////                log.error("config‘Result is null");
//            }
        }

    }

    private void bindingOutputToContext(ContractConfig config, ContractConfig.FunctionConfig functionConfig, ProtocolResponse response) {

    }

    private void requestResultHandler(ContractConfig.FunctionConfig functionConfig, ProtocolResponse response) {
        if (response.getCode() == 0) {
            return;
        }
        ContractConfig.FunctionConfig.ResultConfig result = functionConfig.getResult();
        log.error("请求医保接口系统异常, 返回Body{}", response.getBody());
        if (result != null && !result.isSkip()) {
            throw new RuntimeException("请求医保接口系统异常,返回Body:" + response.getBody());
        }
    }


    private void suffixedInExpressionHandler(ContractConfig config, ContractConfig.FunctionConfig functionConfig, ProtocolResponse response) {
        String functionExpression = functionConfig.getPrefixedOutExpression();
        if (StringUtils.hasLength(functionExpression) && !"null".equals(functionExpression)) {
            //执行function的后置表达式，离调用更近
            DSLContextHolder.getContext().parseResult(functionExpression, response.getResultMap());
            return;
        }
        if (!config.isCommon()) {
            return;
        }
        String commonExpression = config.getCommonConfig().getPrefixedOutExpression();
        if (StringUtils.hasLength(commonExpression) && !"null".equals(commonExpression)) {
            //执行公共的后置表达式
            DSLContextHolder.getContext().parseResult(commonExpression, response.getResultMap());
        }
    }

    /**
     * @param functionConfig
     * @param response
     * @return 是否触发重试协议
     */
    private boolean businessResultHandler(ContractConfig.FunctionConfig functionConfig, ProtocolResponse response) {
        //解析返回值是否成功，如果调用结果不成功,给出提示
        //优先判断function的结果条件, 在function的结果条件没有的情况下，其次判断公共的结果条件
        ContractConfig.FunctionConfig.ResultConfig resultConfig = functionConfig.getResult();
        ContractInvokeContextHolder.getContext().setSuccess(true);
        if (resultConfig == null) {
            return false;
        }
        if (StringUtils.hasLength(resultConfig.getSuccess())) {
            Boolean result = DSLContextHolder.getContext().parseConditionResult(resultConfig.getSuccess());
            ContractInvokeContextHolder.getContext().setSuccess(result);

            //执行hook
            if (!CollectionUtils.isEmpty(resultConfig.getHook())) {
                //配置了的Hook的执行条件并且执行条件是true,或者未配置执行条件并且Function未成功
                if (StringUtils.hasLength(resultConfig.getHookCondition()) && BooleanUtil.isTrue(DSLContextHolder.getContext().parseConditionResult(resultConfig.getHookCondition())) ||
                        (!StringUtils.hasLength(resultConfig.getHookCondition()) && !result)) {
                    resultConfig.getHook().forEach((hook) -> {
                        DSLContextHolder.getContext().executeEL(hook);
                    });
                }
            }

            //结果不成功,并且不跳过
            if (BooleanUtil.isFalse(result) && !resultConfig.isSkip()) {
                String tips = "执行 function 失败, " + functionConfig;
                if (StringUtils.hasLength(resultConfig.getTips())) {
                    String tipsValue = DSLContextHolder.getContext().parseValue(resultConfig.getTips());
                    if (StringUtils.hasLength(tipsValue)) {
                        tips = tipsValue;
                    }
                }
                throw new RuntimeException(tips);
            }
            //是否有重试
            ContractConfig.FunctionConfig.RetryConfig retry = resultConfig.getRetry();
            if (retry != null && BooleanUtil.isTrue(retry.getEnable())) {
                Boolean retryStatus = DSLContextHolder.getContext().parseConditionResult(retry.getCondition());
                if (BooleanUtil.isTrue(retryStatus)) {
                    DSLContextHolder.getContext().executeEL(retry.getExpression());
                    return true;
                }
            }
        }

        return false;


    }

    /**
     * 记录API调用日志
     *
     * @param invokeContext  调用上下文
     * @param functionConfig 函数配置
     * @param inputObject    输入对象
     * @return 创建的调用日志对象
     */
    private ContractInvokeLog recordConcatLog(ContractInvokeContext invokeContext,
                                              ContractConfig.FunctionConfig functionConfig,
                                              InputObject inputObject,
                                              ContractConfig config) {
        // 创建新的调用日志对象
        ContractInvokeLog invokeLog = new ContractInvokeLog();
        int index = config.getFunctions().indexOf(functionConfig);
        String currentUrl = valueParser.parseValue(functionConfig.getDomain(), DSLContextHolder.getContext()) + valueParser.parseValue(functionConfig.getPath(), DSLContextHolder.getContext()) + "_" + index;
        invokeLog.setUrl(currentUrl);
        invokeLog.setMethod(functionConfig.getRequest().getMethod().name());
        invokeLog.setRequestBody(inputObject.getBody());


        // 移除相同URL的旧日志记录
        invokeContext.getInvokeLogs().removeIf(existingLog -> existingLog.getUrl().equals(currentUrl));

        // 将新记录加入集合
        invokeContext.getInvokeLogs().add(invokeLog);

        return invokeLog;
    }

}

package com.xyy.saas.localserver.medicare.dsl.executor.log;

import com.xyy.saas.localserver.medicare.dsl.executor.DSLContext;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/09/07 15:58
 */
public class ContractInvokeContextHolder<T> {

    private static final ThreadLocal<ContractInvokeContext> contextHolder = new ThreadLocal<>();

    public static ContractInvokeContext getContext() {
        ContractInvokeContext invokeLogContext = contextHolder.get();
        if (invokeLogContext == null) {
            invokeLogContext = new ContractInvokeContext();
            contextHolder.set(invokeLogContext);
        }
        return invokeLogContext;
    }

    public static void clear() {
        ContractInvokeContext contractInvokeContext = contextHolder.get();
        if (contractInvokeContext != null) {
            contractInvokeContext.clear();
        }
        contextHolder.remove();
    }
}

package com.xyy.saas.localserver.medicare.dsl.executor.value;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @Desc 参保人相关参数值
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/18 14:43
 */
@Data
@Builder
public class Insurant extends DSLValue {

    /* 参保人姓名*/
    private String name;

    /* 身份证号*/
    private String idCard;

    /*当前选择的就诊凭证,就诊凭证类型:身份证、社保卡、电子凭证*/
    private String certType;

    /*就诊凭证卡号*/
    private String cardNo;

    /*是否异地*/
    private boolean offsite;

    /*人员编号*/
    private String personNo;

    /*参保信息*/
    private List<?> insurantInfos;

}

package com.xyy.saas.localserver.medicare.dsl.executor.value;

import lombok.Builder;
import lombok.Data;

/**
 * @Desc 操作人相关参数值
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/18 13:47
 */
@Data
@Builder
public class Operator extends DSLValue {

    /*经办人id*/
    private int id;

    /* 经办人类别, 1-经办人；2-自助终端；3-移动终端*/
    private int type = 1;

    /*经办人名称*/
    private String name;

    /*经办人调用9001，返回的签到号 */
    private String signNo;


}

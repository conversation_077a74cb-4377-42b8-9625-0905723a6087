package com.xyy.saas.localserver.medicare.dsl.executor.value;

import lombok.Builder;
import lombok.Data;

/**
 * @Desc 就诊信息
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/31 19:30
 */
@Data
@Builder
public class Visit {

    /*病种编码*/
    private String diseCodg;

    /*病种名称*/
    private String diseName;

    /*个人账户使用标志*/
    private String acctUsedFlag;

    /*选择的医疗类别*/
    private String medicareType;

    /* 选择的险种*/
    private String insutype;

    /* 参保地统筹区, 选择的统筹区*/
    private String areaCode;

}

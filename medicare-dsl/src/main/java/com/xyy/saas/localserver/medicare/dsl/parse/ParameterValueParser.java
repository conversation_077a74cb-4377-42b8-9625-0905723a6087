package com.xyy.saas.localserver.medicare.dsl.parse;

import com.xyy.saas.localserver.medicare.dsl.executor.DSLContext;

import java.util.Map;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/04 13:42
 */
public interface ParameterValueParser {

    String parseValue(String expression, String replaceValue, DSLContext context);

    String parseValue(String expression, DSLContext context);

    /**
     * 解析到就返回解析值,未解析到就返回“”,并且把未解析到的key和expression放入unresolvedMap
     *
     * @param expression
     * @param context
     * @param key
     * @param unresolvedMap
     * @return 解析到就返回解析值, 未解析到就返回“”
     */
    String parseValue(String expression, DSLContext context, String key, Map<String, String> unresolvedMap);

    Boolean parseResult(String expression, DSLContext context);

    Object parseToValueFromContext(String expression, Map<String, Object> context);

    Map<String, Object> parseToMapFromContext(String expression, Map<String, Object> context);

    void executeEL(String expression, DSLContext context);

}

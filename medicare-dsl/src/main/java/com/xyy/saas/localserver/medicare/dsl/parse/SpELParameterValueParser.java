package com.xyy.saas.localserver.medicare.dsl.parse;

import cn.hutool.json.JSONUtil;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.expression.Expression;
import org.springframework.expression.ParseException;
import org.springframework.expression.spel.SpelEvaluationException;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * @Desc Spring EL 表达式解析类
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/04 14:36
 */
@Slf4j
public class SpELParameterValueParser implements ParameterValueParser {

    protected SpelExpressionParser parser = new SpelExpressionParser();

    private static final String _this_ = "_this_";

    /**
     * @param expression
     * @param replaceValue
     * @param context
     * @return
     */
    @Override
    public String parseValue(String expression, String replaceValue, DSLContext context) {
        return parseValue(expression.replace(_this_, replaceValue), context);
    }

    /**
     * 解析值
     *
     * @param expression
     * @param context
     * @return
     */
    @Override
    public String parseValue(String expression, DSLContext context) {
        if (!StringUtils.hasLength(expression)) {
            return "";
        }
        try {
            Expression exp = parser.parseExpression(expression);
            Object value = exp.getValue(context);
            return value == null ? "" : String.valueOf(value);
        } catch (ParseException | SpelEvaluationException e) {
            log.error("解析表达式错误,expression:{}", expression);
            e.printStackTrace();
            return "";
        }
    }

    /**
     * @param expression
     * @param context
     * @param key
     * @param unresolvedMap
     * @return
     */
    @Override
    public String parseValue(String expression, DSLContext context, String key, Map<String, String> unresolvedMap) {
        if (!StringUtils.hasLength(expression)) {
            return "";
        }
        try {
            Expression exp = parser.parseExpression(expression);
            Object value = exp.getValue(context);
            return value == null ? "" : String.valueOf(value);
        } catch (ParseException | SpelEvaluationException e) {
            unresolvedMap.put(key, expression);
            return "";
        }
    }

    /**
     * 解析表达式
     *
     * @param expression
     * @param context
     * @return
     */
    @Override
    public Boolean parseResult(String expression, DSLContext context) {
        try {
            Expression exp = parser.parseExpression(expression);
            return Boolean.valueOf(exp.getValue(context, Boolean.class));
        } catch (ParseException | SpelEvaluationException e) {
            log.error("解析表达式错误,expression:{}", expression);
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 解析成值
     *
     * @param expression
     * @param context
     * @return
     */
    @Override
    public Object parseToValueFromContext(String expression, Map<String, Object> context) {
        return parseMap(expression, context, String.class);
    }

    /**
     * @param expression
     * @param context
     * @return
     */
    @Override
    public Map<String, Object> parseToMapFromContext(String expression, Map<String, Object> context) {
        return parseMap(expression, context, Map.class);
    }

    /**
     * @param expression
     * @param context
     */
    @Override
    public void executeEL(String expression, DSLContext context) {
        Expression exp = parser.parseExpression(expression);
        exp.getValue(context);
    }

    /**
     * @param expression
     * @param context
     * @param clazz
     * @return
     */
    private <T> T parseMap(String expression, Map<String, Object> context, Class<T> clazz) {
        try {
            Expression exp = parser.parseExpression(expression);
            return (T) exp.getValue(context);
        } catch (SpelEvaluationException | ParseException e) {
            log.error("解析表达式错误,expression:{}, context:{}", expression, JSONUtil.toJsonStr(context));
            e.printStackTrace();
            return null;
        }
    }


}

package com.xyy.saas.localserver.medicare.dsl.protocol;

import com.xyy.saas.localserver.medicare.dsl.protocol.response.ProtocolResponse;
import lombok.experimental.SuperBuilder;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/07/25 15:56
 */
@SuperBuilder
public class WebserviceProtocalClient extends ProtocolClient {
    /**
     * @return
     */
    @Override
    public ProtocolClient toGenerateBody() {
        return null;
    }

    /**
     *
     */
    @Override
    public void parseResponseBody() {

    }

    /**
     * @return
     */
    @Override
    public ProtocolResponse toInvoker() {

        return null;
    }
}

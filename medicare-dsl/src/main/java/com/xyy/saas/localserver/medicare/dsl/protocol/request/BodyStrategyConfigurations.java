//package com.xyy.saas.localserver.medicare.dsl.protocol.request;
//
//import org.springframework.boot.autoconfigure.cache.*;
//
//import java.util.Collections;
//import java.util.EnumMap;
//import java.util.Map;
//
///**
// * @Desc
// * <AUTHOR>  <EMAIL>
// * @Date Created in 2023/08/08 15:56
// */
//public class BodyStrategyConfigurations {
//
//    private static final Map<BodyStrategy, String> MAPPINGS;
//
//    static {
//        Map<BodyStrategy, String> mappings = new EnumMap<>(BodyStrategy.class);
////        mappings.put(BodyStrategy.signdata, );
//        MAPPINGS = Collections.unmodifiableMap(mappings);
//    }
//
//}

package com.xyy.saas.localserver.medicare.dsl.protocol.request;

import cn.hutool.core.map.MapUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xyy.saas.localserver.medicare.dsl.config.base.ContractConfig;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContextHolder;
import com.xyy.saas.localserver.medicare.dsl.executor.value.InputObject;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Desc Json格式的tree型Body格式
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/08 14:40
 */
public class JsonNodeBodyBuilderStrategy extends RequestBodyBuilderStrategy {

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * @param inputObject
     * @return
     */
    @Override
    public String generateBody(InputObject inputObject) {
        ContractConfig contractConfig = inputObject.getContractConfig();
        ContractConfig.FunctionConfig functionConfig = inputObject.getFunctionConfig();
        Map<String, Object> requestBody = new LinkedHashMap<>();
        if (contractConfig.isCommon()) {
            //如果存在公共参数，就把公共参数都放到requestBody
            Map<String, Object> commonInput = DSLContextHolder.getContext().getCommonInput();
            requestBody.putAll(commonInput);
        }
//        ContractConfig.FunctionConfig.RequestConfig request = functionConfig.getRequest();
        Map<String, Object> bodys = inputObject.getBodys();
        if (MapUtil.isNotEmpty(bodys)) {
            // 现将两个 Map<String, Object> 合并，同时满足不覆盖已有大键值直到遇到基础数据类型（如字符串、数字等）的需求，你需要递归地处理嵌套的
//            requestBody.putAll(bodys);
            mergeMaps(requestBody, bodys);
        }
        try {
            return objectMapper.writeValueAsString(requestBody);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }


    public static void mergeMaps(Map<String, Object> target, Map<String, Object> source) {
        for (Map.Entry<String, Object> entry : source.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (target.containsKey(key)) {
                Object targetValue = target.get(key);
                if (targetValue instanceof Map && value instanceof Map) {
                    // 递归合并子Map
                    mergeMaps((Map<String, Object>) targetValue, (Map<String, Object>) value);
                }
            } else {
                // 如果目标Map中没有这个键，则直接放入
                target.put(key, deepCopy(value));
            }
        }
    }

    @SuppressWarnings("unchecked")
    private static Object deepCopy(Object value) {
        if (value instanceof Map) {
            Map<String, Object> original = (Map<String, Object>) value;
            Map<String, Object> copy = new LinkedHashMap<>();
            for (Map.Entry<String, Object> entry : original.entrySet()) {
                copy.put(entry.getKey(), deepCopy(entry.getValue()));
            }
            return copy;
        } else {
            return value;
        }
    }
}

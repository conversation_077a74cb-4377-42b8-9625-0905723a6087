package com.xyy.saas.localserver.medicare.dsl.reader;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.xyy.saas.localserver.medicare.dsl.config.DSLKey;
import com.xyy.saas.localserver.medicare.dsl.config.base.ContractConfig;
import com.xyy.saas.localserver.medicare.dsl.config.base.DSLConfig;
import org.springframework.core.io.ResourceLoader;
import org.springframework.util.StringUtils;

/**
 * @Desc 组合的配置读取器
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/03 19:30
 */
public class CompositeConfigReader extends ConfigReader {

    private final ResourceLoader resourceLoader;

    private LocalFileConfigReader localFileConfigReader;
    private JDBCConfigReader jdbcConfigReader = new JDBCConfigReader();
    private StringConfigReader stringConfigReader;

    public CompositeConfigReader(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
        this.localFileConfigReader = new LocalFileConfigReader(resourceLoader);
        this.jdbcConfigReader = new JDBCConfigReader();
        this.stringConfigReader = new StringConfigReader();
    }

    public static void setLocalFilePath(String path) {
        LocalFileConfigReader.setFolder(path);
    }


    /**
     * @param key
     * @param yamlSource       配置的数据元信息byte
     * @param commonYamlSource 公共yaml
     * @return
     */
    @Override
    public DSLConfig readerConfig(DSLKey key, String yamlSource, String commonYamlSource) {

        //无业务标识，则直接解析yml
        if(key == null){
            return stringConfigReader.readerConfig(null,yamlSource, commonYamlSource);
        }
        //优先查找读取文件
        DSLConfig config = localFileConfigReader.readerConfig(key, yamlSource, commonYamlSource);
        if (config != null) {
            return config;
        }
        if (!StringUtils.hasLength(yamlSource)) {
            return null;
        }
        return jdbcConfigReader.readerConfig(key, yamlSource, commonYamlSource);
    }

    /**
     * @param contractConfig
     * @param commonYamlSource
     * @return
     * @throws JsonProcessingException
     */
    @Override
    public ContractConfig readerCommonConfig(ContractConfig contractConfig, String commonYamlSource) throws JsonProcessingException {
        return null;
    }
}

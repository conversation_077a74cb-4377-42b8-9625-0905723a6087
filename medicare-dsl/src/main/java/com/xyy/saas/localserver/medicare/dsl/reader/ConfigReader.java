package com.xyy.saas.localserver.medicare.dsl.reader;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.xyy.saas.localserver.medicare.dsl.config.DSLKey;
import com.xyy.saas.localserver.medicare.dsl.config.base.ContractConfig;
import com.xyy.saas.localserver.medicare.dsl.config.base.DSLConfig;

/**
 * @Desc 配置读取器
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/01 14:36
 */
public abstract class ConfigReader {

    protected final ObjectMapper objectMapper = new ObjectMapper(new YAMLFactory());

    /**
     * @param key
     * @param yamlSource 配置的数据元信息byte
     * @return
     */
    public abstract DSLConfig readerConfig(DS<PERSON><PERSON><PERSON> key, String yamlSource, String commonYamlSource);


    /**
     * 协议类型配置读取公共参数配置
     *
     * @param contractConfig
     * @param commonYamlSource
     * @return
     */
    public abstract ContractConfig readerCommonConfig(ContractConfig contractConfig, String commonYamlSource) throws JsonProcessingException;


}

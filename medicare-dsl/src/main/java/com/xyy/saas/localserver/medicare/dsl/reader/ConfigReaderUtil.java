package com.xyy.saas.localserver.medicare.dsl.reader;

import com.xyy.saas.localserver.medicare.dsl.config.base.ContractConfig;
import com.xyy.saas.localserver.utils.SpringUtils;

/**
 * @Desc 读取配置
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/09/11 15:16
 */
public class ConfigReaderUtil {

    public static ContractConfig getConfigReader(String configName) {
        ConfigReader configReader = SpringUtils.getBean(ConfigReader.class);
        return (ContractConfig) configReader.readerConfig(null, configName, null);
    }

    public static ContractConfig getConfigReader(String configName, String commonName) {
        ConfigReader configReader = SpringUtils.getBean(ConfigReader.class);
        return (ContractConfig) configReader.readerConfig(null, configName, commonName);
    }


}

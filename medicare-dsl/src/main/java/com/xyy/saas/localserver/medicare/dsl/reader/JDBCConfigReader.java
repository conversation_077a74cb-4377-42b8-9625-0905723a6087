package com.xyy.saas.localserver.medicare.dsl.reader;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.xyy.saas.localserver.medicare.dsl.config.DSLKey;
import com.xyy.saas.localserver.medicare.dsl.config.base.ContractConfig;
import com.xyy.saas.localserver.medicare.dsl.config.base.DSLConfig;
import com.xyy.saas.localserver.medicare.dsl.config.base.LogicalConfig;
import com.xyy.saas.localserver.medicare.dsl.config.base.ViewUIConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.util.Assert;

import java.io.IOException;

/**
 * @Desc 数据库配置读取
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/02 14:52
 */
@Slf4j
public class JDBCConfigReader extends ConfigReader {


    /**
     * @param key
     * @param yamlSource 配置的数据元信息byte
     * @return 参数配置
     */
    @Override
    public DSLConfig readerConfig(DSLKey key, String yamlSource, String commonYamlSource) {
//        Assert.notNull(key, "读取的配置不能为null");
        try {
            JsonNode rootNode = objectMapper.readTree(yamlSource);
            String dslType = rootNode.get("dslType").asText();
            switch (dslType) {
                case "contract":
                    ContractConfig contractConfig = objectMapper.readValue(yamlSource, ContractConfig.class);
                    return readerCommonConfig(contractConfig, commonYamlSource);
                case "logical":
                    return objectMapper.readValue(yamlSource, LogicalConfig.class);
                case "viewui":
                    return objectMapper.readValue(yamlSource, ViewUIConfig.class);
                default:
                    log.error("DSLKey: {}, 未知的DSLType类型: {}", key, dslType);
                    throw new IllegalArgumentException("未知的DSLType类型:" + dslType);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * @param contractConfig
     * @return
     */
    @Override
    public ContractConfig readerCommonConfig(ContractConfig contractConfig, String commonYamlSource) throws JsonProcessingException {
        if (!contractConfig.isCommon()) {
            //没有公共参数的
            return contractConfig;
        }
        //有公共参数的,反序列化公共参数,不用所有协议都配一遍公共参数
        try {
            ContractConfig.CommonConfig commonConfig = objectMapper.readValue(commonYamlSource, ContractConfig.CommonConfig.class);
            contractConfig.setCommonConfig(commonConfig);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return contractConfig;
    }
}

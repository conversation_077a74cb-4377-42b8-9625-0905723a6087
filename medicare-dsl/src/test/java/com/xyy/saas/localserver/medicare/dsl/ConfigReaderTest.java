package com.xyy.saas.localserver.medicare.dsl;

import cn.hutool.json.JSONUtil;
import com.xyy.saas.localserver.medicare.dsl.config.DSLKey;
import com.xyy.saas.localserver.medicare.dsl.config.base.ContractConfig;
import com.xyy.saas.localserver.medicare.dsl.config.base.DSLConfig;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContext;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContextHolder;
import com.xyy.saas.localserver.medicare.dsl.executor.value.*;
import com.xyy.saas.localserver.medicare.dsl.protocol.request.JsonNodeBodyBuilderStrategy;
import com.xyy.saas.localserver.medicare.dsl.reader.CompositeConfigReader;
import com.xyy.saas.localserver.medicare.dsl.reader.ConfigReader;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * @Desc 配置读取测试
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/16 15:40
 */
@ExtendWith(SpringExtension.class)
//@SpringBootApplication
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
public class ConfigReaderTest {

    @Autowired
    private ResourceLoader resourceLoader;

    @Test
    public void testConfigReader() {
        //测试文件读取
        ConfigReader configReader = new CompositeConfigReader(resourceLoader);
        DSLConfig signinConfig = configReader.readerConfig(DSLKey.signin, null, null);
        Assertions.assertInstanceOf(ContractConfig.class, signinConfig);
    }

    @Test
    public void testConfigReaderAndParser() {
        DSLContext context = DSLContextHolder.getContext();
        //初始化药店信息
        context.builder(() -> Drugstore.builder().name("洛米的药店").code("P123456").areaCode("433200").build());
        //初始化操作人信息
        context.builder(() -> Operator.builder().id(1).type(2).name("洛米").build());
        context.builder(() -> Insurant.builder().name("张某某").idCard("******************").build());
        context.builder(() -> Device.builder().mac("AA-BB-CC-DD-EE").natIp("*************").build());

        //测试文件读取
        ConfigReader configReader = new CompositeConfigReader(resourceLoader);
        DSLConfig signinConfig = configReader.readerConfig(DSLKey.signin, null, null);
        Assertions.assertInstanceOf(ContractConfig.class, signinConfig);
        ContractConfig contractConfig = (ContractConfig) signinConfig;
        //测试解析值
        context.parseCommonParamValue(contractConfig);
//        System.out.println(JSONUtil.toJsonStr(context.getParamValue()));
        context.parseFunctionInputParamValue(contractConfig, contractConfig.getFunctions().get(0));

        InputObject inputObject = context.getInput(0);
        System.out.println(JSONUtil.toJsonStr(inputObject.getBodys()));
        JsonNodeBodyBuilderStrategy strategy = new JsonNodeBodyBuilderStrategy();
        System.out.println(strategy.generateBody(inputObject));
    }

}

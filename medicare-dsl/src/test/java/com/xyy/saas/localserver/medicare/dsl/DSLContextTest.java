package com.xyy.saas.localserver.medicare.dsl;

import com.xyy.saas.localserver.medicare.dsl.config.base.ContractConfig;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContext;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContextHolder;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.Map;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/17 20:04
 */
public class DSLContextTest {

    @Test
    public void testParseCommonParamValue() {
        DSLContext context = DSLContextHolder.getContext();
        ContractConfig contractConfig = new ContractConfig();
        contractConfig.setParam(Map.of("a", "111", "b", "222", "c", "333"));
        context.parseCommonParamValue(contractConfig);
        Assertions.assertEquals(context.getParamValue("a"), "111");
        Assertions.assertEquals(context.getParamValue("b"), "222");
        Assertions.assertEquals(context.getParamValue("c"), "333");
    }

}

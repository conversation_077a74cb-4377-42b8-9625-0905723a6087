package com.xyy.saas.localserver.medicare.dsl;

import com.google.common.collect.Lists;
import com.xyy.saas.localserver.medicare.dsl.config.DSLKey;
import com.xyy.saas.localserver.medicare.dsl.config.base.ContractConfig;
import com.xyy.saas.localserver.medicare.dsl.config.base.DSLConfig;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContext;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContextHolder;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLExecutor;
import com.xyy.saas.localserver.medicare.dsl.executor.value.*;
import com.xyy.saas.localserver.medicare.dsl.reader.CompositeConfigReader;
import com.xyy.saas.localserver.medicare.dsl.reader.ConfigReader;
import com.xyy.saas.localserver.medicare.dsl.request.BalanceVO;
import com.xyy.saas.localserver.medicare.dsl.response.BalanceResult;
import com.xyy.saas.localserver.medicare.dsl.response.PersonInfoResult;
import com.xyy.saas.localserver.medicare.dsl.response.SignInResult;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/25 9:56
 */
@ExtendWith(SpringExtension.class)
//@SpringBootApplication //一个包路径下只能注解一个
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
public class ShanxiTest {

    @Autowired
    private ResourceLoader resourceLoader;

    private ConfigReader configReader;


    @BeforeEach
    public void init() {
        this.configReader = new CompositeConfigReader(resourceLoader);
        ((CompositeConfigReader) configReader).setLocalFilePath("/medicare/shanxi");
        DSLContext context = DSLContextHolder.getContext();
        //初始化药店信息
        context.builder(() -> Drugstore.builder().name("眉县新眉医药有限公司横渠一分店").code("P61032600066").areaCode("610326").build());
        //初始化操作人信息
        context.builder(() -> Operator.builder().id(162545).type(1).name("王西平").signNo("28443354").build());
        context.builder(() -> Device.builder().mac("D0-27-88-65-FC-3F").natIp("*************").build());
        context.builder(() -> Insurant.builder().name("张朋").cardNo("******************").idCard("******************").personNo("42100000000000100007871904").certType("02").offsite(true).build());
        context.builder(() -> Visit.builder().areaCode("420100").acctUsedFlag("1").medicareType("41").insutype("310").build());
    }

    /**
     * 签到
     */
    @Test
    @Order(-1)
    public void testSignIn() {
        //测试文件读取
        DSLConfig signinConfig = configReader.readerConfig(DSLKey.signin, null, null);
        Assertions.assertInstanceOf(ContractConfig.class, signinConfig);
        ContractConfig contractConfig = (ContractConfig) signinConfig;

        SignInResult signInResult = DSLExecutor.instance.contractInvoker(contractConfig, SignInResult.class);
        System.out.println(signInResult);
        //测试解析值
        Assertions.assertNotNull(signInResult.getSignNo());
        Assertions.assertNotNull(signInResult.getSignTime());
    }

    /**
     * 读身份证
     */
    @Test
    @Order(1)
    public void testPersonInfo() {

        //测试文件读取
        DSLConfig config = configReader.readerConfig(DSLKey.personInfo, null, null);
        Assertions.assertInstanceOf(ContractConfig.class, config);
        ContractConfig contractConfig = (ContractConfig) config;
        Assertions.assertThrows(RuntimeException.class, () -> {
            //读卡1101设置成错误的签到接口9001
            contractConfig.getFunctions().get(0).getRequest().getBody().put("infno", "9001");
            DSLExecutor.instance.contractInvoker(contractConfig, PersonInfoResult.class);
        });
        init();
        contractConfig.getFunctions().get(0).getRequest().getBody().put("infno", "1101");
        PersonInfoResult personInfoResult = DSLExecutor.instance.contractInvoker(contractConfig, PersonInfoResult.class);

        System.out.println(personInfoResult.toString());
        //测试解析值
        Assertions.assertNotNull(personInfoResult.getCertNo());
        Assertions.assertNotNull(personInfoResult.getPersonName());
    }


    /**
     * 预结算
     */
    @Test
    @Order(2)
    public void testPreBalance() {
        DSLContext context = DSLContextHolder.getContext();
        List<BalanceVO.BalanceDetail> details = Lists.newArrayList(
                BalanceVO.BalanceDetail.builder().medicareCode("ZA09ABX0146010104456").productPref("ZHL78338945")
                        .quantity(BigDecimal.valueOf(2)).price(BigDecimal.valueOf(6)).usedWay("").build(),
                BalanceVO.BalanceDetail.builder().medicareCode("ZA09BAG0398010105015").productPref("ZHL78338333")
                        .quantity(BigDecimal.valueOf(2)).price(BigDecimal.valueOf(12)).usedWay("").build()
        );
        BigDecimal totalAmount = details.stream().map(BalanceVO.BalanceDetail::getSubtotal).reduce(BigDecimal.ZERO, BigDecimal::add);
        BalanceVO balanceVO = BalanceVO.builder().balanceNo("111116939107420004").totalAmount(totalAmount).details(details).build();
        context.setBusiness(balanceVO);
        //测试文件读取
        DSLConfig config = configReader.readerConfig(DSLKey.preBalance, null, null);
        Assertions.assertInstanceOf(ContractConfig.class, config);
        ContractConfig contractConfig = (ContractConfig) config;
        BalanceResult result = DSLExecutor.instance.contractInvoker(contractConfig, BalanceResult.class);
        System.out.println(result.toString());
    }

}

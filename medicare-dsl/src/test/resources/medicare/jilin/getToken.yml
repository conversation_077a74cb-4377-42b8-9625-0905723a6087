name: '获取token'
domain: 175.27.228.98:20001
path: /auth/oauth/token
common: false
functions:
  - condition: globalContext['access_token'] != null
    request:
      format: form
      header:
        "Authorization": "'Basic ' + T(cn.hutool.core.codec.Base64).encode(drugstore.certMap['clientid'] + ':' + drugstore.certMap['clientsecret'])"
      body:
        "scope": "'server'" #终端授权范围
        "grant_type": "'refresh_token'" #终端授权类型
        "refresh_token": globalContext['refresh_token'] #token
    response:
      "access_token": "[access_token]" #token
      "token_type": "[token_type]" #token类型
      "refresh_token": "[refresh_token]" #Refreshtoken
      "expires_in": "[expires_in]" #有效期
      "scope": "[scope]" #授权范围
      "user_id": "[user_id]" #用户id
      "username": "[username]" #
      "jti": "[jti]" #TOKEN_ID
    result:
      success: output[code] == 0
      skip: true
      tips: output[msg]
      hook:
        - globalContext.remove('access_token')
        - globalContext.remove('refresh_token')
  - condition: globalContext['access_token'] == null
    request:
      format: form
      header:
        "Authorization": "'Basic ' + T(cn.hutool.core.codec.Base64).encode(drugstore.certMap['clientid'] + ':' + drugstore.certMap['clientsecret'])"
      body:
        "scope": "'server'" #终端授权范围
        "grant_type": "'password'" #终端授权类型
        "username": drugstore.certMap['username'] #用户名
        "password": drugstore.certMap['password'] #密码
    response:
      "access_token": "[access_token]" #token
      "token_type": "[token_type]" #token类型
      "refresh_token": "[refresh_token]" #Refreshtoken
      "expires_in": "[expires_in]" #有效期
      "scope": "[scope]" #授权范围
      "user_id": "[user_id]" #用户id
      "username": "[username]" #
      "jti": "[jti]" #TOKEN_ID
global:
  "token": "['access_token']"
  "refresh_token": "['refresh_token']"
result:
  success: output[code] == null
  skip: false
  tips: output[msg]





dslType: contract
enable: true
name: '签到'
functions:
  - bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "infno": 9001 #接口号
        "input":
          "signIn":
            "opter_no": operator.id
            "mac": "'7W3fG2ba4hEkj9Ax8ji1sKWnNUeU5Ihn85y2OuwyUBc='"
            "ip": device.natIp
    response:
      "output":
        "signinoutb":
          "sign_time": "[sign_time]" #格式：yyyy-MM-dd HH:mm:ss
          "sign_no": "[sign_no]" #长度30
outputFields:
  sign_time: sign_time
  sign_no: sign_no"

domain: 10.37.128.34:80
#protocol: http
#bodyStrategy: signdata
input:
  #  "infno": 9001 #接口号
  #定点医药机构编号(12)+时间(14)+顺序号(4) 时间格式：yyyyMMddHHmmss
  "msgid": drugstore.code+T(cn.hutool.core.date.DateUtil).date().toString("yyyyMMddHHmmss")+T(java.lang.String).format("%04d", T(cn.hutool.core.util.IdUtil).createSnowflake(1, 1).nextId() % 10000)
  "mdtrtarea_admvs": drugstore.areaCode #就医地医保区划
  "insuplc_admdvs": visit.areaCode #参保地医保区划
  "recer_sys_code": "'MBS_LOCAL'" #用于多套系统接入，区分不同系统使用
  "dev_no": #设备编号
  "dev_safe_info": #设备安全信息
  "cainfo": #数字签名信息
  "signtype": #签名类型,建议使用SM2、SM3
  "infver": "'V1.0'" #接口版本号,例如：“V1.0”，版本号由医保下发通知。
  "opter_type": operator.type #经办人类别,1-经办人；2-自助终端；3-移动终端
  "opter": operator.id #经办人/终端编号
  "opter_name": operator.name #经办人姓名/终端名称
  "inf_time": T(cn.hutool.core.date.DateUtil).date().toString("yyyy-MM-dd HH:mm:ss") #交易时间
  "fixmedins_code": drugstore.code #定点医药机构编号
  "fixmedins_name": drugstore.name #定点医药机构名称
  "sign_no": operator.signNo #交易签到流水号,通过签到【9001】交易获取
  "input":
suffixedInExpression: null #前置表达式
prefixedOutExpression: null #后置表达式
output:
  "infcode": "[infcode]" #交易状态码
  #接收方报文ID,接收方返回，接收方医保区划代码(6)+时间(14)+流水号(10)，时间格式：yyyyMMddHHmmss
  "inf_refmsgid": "[inf_refmsgid]"
  "refmsg_time": "[refmsg_time]" #接收报文时间,格式：yyyyMMddHHmmssSSS
  "respond_time": "[respond_time]" #响应报文时间,格式：yyyyMMddHHmmssSSS
  "err_msg": "[err_msg]" #交易失败状态下，业务返回的错误信息
  "output":
result:
  success: output[infcode] == 0
  skip: false
  tips: output[err_msg]


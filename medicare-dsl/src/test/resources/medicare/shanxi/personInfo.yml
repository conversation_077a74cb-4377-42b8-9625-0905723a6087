dslType: contract
enable: true
name: 人员信息
protocol: http
format: json
domain: *************:21001
functions:
  - path: /fsi/api/fsiPsnInfoService/queryPsnInfo
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "infno": 1101 #接口号
        "input":
          "data":
            "mdtrt_cert_type": insurant.certType #就诊凭证类型
            "mdtrt_cert_no": "insurant.offsite ? 'SFZ;'.concat(insurant.cardNo).concat(';').concat('-') : insurant.cardNo"
            #就诊凭证编号
            "card_sn": "" #卡识别码
            "begntime": "" #开始时间
            "psn_cert_type": "" #人员证件类型
            "certno": "" #证件号码
            "psn_name": "" #人员姓名
    response:
      "output":
        "baseinfo":
          "psn_no": "[psn_no]" #人员编号
          "psn_cert_type": "[psn_cert_type]" #人员证件类型
          "certno": "[certno]" #证件号码
          "psn_name": "[psn_name]" #人员姓名
          "gend": "[gend]" #性别
          "naty": "[naty]" #民族
          "brdy": "[brdy]" #出生日期
          "age": "[age]" #年龄
        "insuinfo":
          - "balc": "[balc]" #余额
            "insutype": "[insutype]" #险种类型
            "psn_type": "[psn_type]" #人员类别
            "psn_insu_stas": "[psn_insu_stas]" #人员参保状态
            "psn_insu_date": "[psn_insu_date]" #个人参保日期
            "paus_insu_date": "[paus_insu_date]" #暂停参保日期
            "cvlserv_flag": "[cvlserv_flag]" #公务员标志
            "insuplc_admdvs": "[insuplc_admdvs]" #参保地医保区划
            "emp_name": "[emp_name]" #单位名称
        "idetinfo":
          - "psn_idet_type": "[psn_idet_type]" #人员身份类别
            "psn_type_lv": "[psn_type_lv]" #人员类别等级
            "memo": "[memo]" #备注
            "begntime": "[begntime]" #开始时间
            "endtime": "[endtime]" #结束时间

#outputFields:
#  sign_time: sign_time
#  sign_no: sign_no"

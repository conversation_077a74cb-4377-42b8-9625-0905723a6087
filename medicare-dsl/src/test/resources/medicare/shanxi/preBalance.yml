dslType: contract
enable: true
name: 人员信息
protocol: http
format: json
domain: 175.27.228.98:21001
functions:
  - path: /fsi/api/fsiDrugStoreService/drugstoreSettleAccountsPreB
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "infno": "'2101_B'" #接口号
        "input":
          "druginfo":
            "psn_no": insurant.personNo           #人员编号
            "mdtrt_cert_type": insurant.certType  #就诊凭证类型
            "mdtrt_cert_no": "insurant.offsite ? 'SFZ;'.concat(insurant.cardNo).concat(';').concat('-') : insurant.cardNo" #就诊凭证编号
            "begntime": T(cn.hutool.core.date.DateUtil).date().toString("yyyy-MM-dd HH:mm:ss") #开始时间
            "medfee_sumamt": business.totalAmount     #医疗费总额
            "insutype": visit.insutype            #险种类型
            "dise_codg": visit.diseCodg           #病种编码
            "dise_name": visit.diseName           #病种名称
            "acct_used_flag": visit.acctUsedFlag  #个人账户使用标志
            "medType": visit.medicareType         #医疗类别
          "drugdetail":
            - "feedetl_sn": business.balanceNo.concat(_index_)                #费用明细流水号
              "rxno": ""             #处方号
              "rx_circ_flag": "0"    #外购处方标志
              "fee_ocur_time": T(cn.hutool.core.date.DateUtil).date().toString("yyyy-MM-dd HH:mm:ss")     #费用发生时间
              "med_list_codg": business.details[_index_]['medicareCode']      #医疗目录编码
              "medins_list_codg": business.details[_index_]['productPref']    #医药机构目录编码
              "det_item_fee_sumamt": business.details[_index_]['subtotal']    #明细项目费用总额
              "cnt": business.details[_index_]['quantity']                    #数量
              "pric": business.details[_index_]['price']                      #单价
              "sin_dos_dscr": ""     #单次剂量描述
              "used_frqu_dscr": ""   #使用频次描述
              "prd_days": ""         #周期天数
              "medc_way_dscr": ""    #用药途径描述
              "bilg_dr_codg": ""     #开单医生编码
              "bilg_dr_name": ""     #开单医生姓名
              "tcmdrug_used_way": business.details[_index_]['usedWay']        #中药使用方式
    response:
      "output":
        "setlinfo":
          "psn_no": "[psn_no]" #人员编号
          "psn_cert_type": "[psn_cert_type]" #人员证件类型
          "certno": "[certno]" #证件号码
          "psn_name": "[psn_name]" #人员姓名
          "gend": "[gend]" #性别
          "naty": "[naty]" #民族
          "brdy": "[brdy]" #出生日期
          "age": "[age]" #年龄
          "insutype": "[insutype]"                     #险种类型
          "psn_type": "[psn_type]"                     #人员类别
          "cvlserv_flag": "[cvlserv_flag]"             #公务员标志
          "setl_time": "[setl_time]"                   #结算时间
          "mdtrt_cert_type": "[mdtrt_cert_type]"       #就诊凭证类型
          "med_type": "[med_type]"                     #医疗类别

          "medfee_sumamt": "[medfee_sumamt]"           #医疗费总额
          "fulamt_ownpay_amt": "[fulamt_ownpay_amt]"   #全自费金额
          "overlmt_selfpay": "[overlmt_selfpay]"       #超限价自费费用
          "preselfpay_amt": "[preselfpay_amt]"         #先行自付金额
          "inscp_scp_amt": "[inscp_scp_amt]"           #符合政策范围金额

          "act_pay_dedc": "[act_pay_dedc]"             #实际支付起付线
          "hifp_pay": "[hifp_pay]"                     #基本医疗保险统筹基金支出
          "pool_prop_selfpay": "[pool_prop_selfpay]"   #基本医疗保险统筹基金支付比例
          "cvlserv_pay": "[cvlserv_pay]"               #公务员医疗补助资金支出
          "hifes_pay": "[hifes_pay]"                   #企业补充医疗保险基金支出
          "hifmi_pay": "[hifmi_pay]"                   #居民大病保险资金支出
          "hifob_pay": "[hifob_pay]"                   #职工大额医疗费用补助基金支出
          "maf_pay": "[maf_pay]"                       #医疗救助基金支出
          "oth_pay": "[oth_pay]"                       #其他支出
          "fund_pay_sumamt": "[fund_pay_sumamt]"       #基金支付总额
          "psn_part_amt": "[psn_part_amt]"             #个人负担总金额
          "acct_pay": "[acct_pay]"                     #个人账户支出
          "psn_cash_pay": "[psn_cash_pay]"             #个人现金支出
          "balc": "[balc]"                             #余额
          "acct_mulaid_pay": "[acct_mulaid_pay]"       #个人账户共济支付金额

          "medins_setl_id": "[medins_setl_id]"         #医药机构结算ID,存放发送方报文ID
          "clr_optins": "[clr_optins]"                 #清算经办机构
          "clr_way": "[clr_way]"                       #清算方式
          "clr_type": "[clr_type]"                     #清算类别

        "setldetail":
          - "fund_pay_type": "[fund_pay_type]"            #基金支付类型
            "inscp_scp_amt": "[inscp_scp_amt]"            #符合政策范围金额
            "crt_payb_lmt_amt": "[crt_payb_lmt_amt]"      #本次可支付限额金额
            "fund_payamt": "[fund_payamt]"                #基金支付金额
            "fund_pay_type_name": "[fund_pay_type_name]"  #基金支付类型名称
            "setl_proc_info": "[setl_proc_info]"          #结算过程信息

        "detlcutinfo":
          - "feedetl_sn": "[feedetl_sn]"                           #费用明细流水号
            "det_item_fee_sumamt": "[det_item_fee_sumamt]"         #明细项目费用总额
            "cnt": "[cnt]"                                         #数量
            "pric": "[pric]"                                       #单价
            "pric_uplmt_amt": "[pric_uplmt_amt]"                   #定价上限金额
            "selfpay_prop": "[selfpay_prop]"                       #自付比例
            "fulamt_ownpay_amt": "[fulamt_ownpay_amt]"             #全自费金额
            "overlmt_amt": "[overlmt_amt]"                         #超限价金额
            "preselfpay_amt": "[preselfpay_amt]"                   #先行自付金额
            "inscp_scp_amt": "[inscp_scp_amt]"                     #符合政策范围金额
            "chrgitm_lv": "[chrgitm_lv]"                           #收费项目等级
            "med_chrgitm_type": "[med_chrgitm_type]"               #医疗收费项目类别
            "bas_medn_flag": "[bas_medn_flag]"                     #基本药物标志
            "hi_nego_drug_flag": "[hi_nego_drug_flag]"             #医保谈判药品标志
            "chld_medc_flag": "[chld_medc_flag]"                   #儿童用药标志
            "list_sp_item_flag": "[list_sp_item_flag]"             #目录特项标志
            "drt_reim_flag": "[drt_reim_flag]"                     #直报标志
            "memo": "[memo]"                                       #备注

#outputFields:
#  sign_time: sign_time
#  sign_no: sign_no"

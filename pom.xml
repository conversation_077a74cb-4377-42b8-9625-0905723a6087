<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.xyy.saas</groupId>
  <artifactId>medicare-localserver</artifactId>
  <packaging>pom</packaging>
  <version>1.0-SNAPSHOT</version>
  <modules>
    <module>medicare-application</module>
    <module>medicare-dsl</module>
    <module>medicare-business</module>
  </modules>

  <name>medicare-localserver</name>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <dependencies>
  </dependencies>

  <!--    控制项目的发布和部署 指定项目的构建产物（如JAR文件、WAR文件等）应该被部署到哪个远程仓库-->
  <distributionManagement>
    <repository>
      <id>maven-releases</id>
      <name>maven-releases</name>
      <url>https://mvn.int.ybm100.com/repository/maven-releases/</url>
    </repository>
    <snapshotRepository>
      <id>maven-snapshots</id>
      <name>maven-snapshots</name>
      <url>https://mvn.int.ybm100.com/repository/maven-snapshots/</url>
    </snapshotRepository>
  </distributionManagement>

  <!--    定义项目构建过程中所需的外部依赖库的来源-->
  <repositories>
    <!-- 使用  aliyun 的 Maven 源，提升下载速度 -->
    <repository>
      <id>aliyunmaven</id>
      <name>aliyun</name>
      <url>https://maven.aliyun.com/repository/public</url>
    </repository>
    <repository>
      <id>mvn-snapshots</id>
      <url>https://mvn.int.ybm100.com/repository/maven-public/</url>
      <releases>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </releases>
      <snapshots>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </snapshots>
    </repository>
  </repositories>

</project>

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.xyy.saas</groupId>
	<artifactId>local-server-cloud</artifactId>

	<modules>
		<module>data-pipeline</module>
		<module>device-manager</module>
		<module>network-proxy</module>
		<module>web-container</module>
		<module>utils-starter</module>
		<module>db-starter</module>
		<module>biz-soa-starter</module>
		<module>saas-cloud-dependencies-bom</module>
		<module>rocketmq-event-bus-spring-boot-starter</module>
		<module>data-pipeline/data-pipeline-h2</module>
	</modules>
	<packaging>pom</packaging>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>cn.iocoder.boot</groupId>
				<artifactId>yudao-dependencies</artifactId>
				<version>${yudao.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<artifactId>spring-boot-starter</artifactId>
				<groupId>org.springframework.boot</groupId>
				<scope>import</scope>
				<type>pom</type>
				<version>${springboot.version}</version>
			</dependency>

			<!-- Dubbo ，不能放开会导致spring jar依赖5.x -->
			<!--<dependency>
				<artifactId>dubbo-bom</artifactId>
				<groupId>org.apache.dubbo</groupId>
				<scope>import</scope>
				<type>pom</type>
				<version>${dubbo.version}</version>
			</dependency>-->

			<!--<dependency>
				<artifactId>dubbo-dependencies-zookeeper-curator5</artifactId>
				<groupId>org.apache.dubbo</groupId>
				<type>pom</type>
				<version>${dubbo.version}</version>
			</dependency>-->

			<!-- -->
			<dependency>
				<artifactId>utils-starter</artifactId>
				<groupId>com.xyy.saas</groupId>
				<version>1.0-SNAPSHOT</version>
			</dependency>
			<dependency>
				<artifactId>db-starter</artifactId>
				<groupId>com.xyy.saas</groupId>
				<version>1.0-SNAPSHOT</version>
			</dependency>
			<!--<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-api</artifactId>
				<version>1.7.25</version>
			</dependency>-->
			<dependency>
				<artifactId>slf4j-api</artifactId>
				<groupId>org.slf4j</groupId>
				<version>2.0.7</version>
			</dependency>
			<dependency>
				<artifactId>guava</artifactId>
				<groupId>com.google.guava</groupId>
				<version>${guava.version}</version>
			</dependency>
			<dependency>
				<artifactId>jakarta.annotation-api</artifactId>
				<groupId>jakarta.annotation</groupId>
				<version>${jakarta.version}</version>
			</dependency>
			<dependency>
				<artifactId>hutool-all</artifactId>
				<groupId>cn.hutool</groupId>
				<version>${hutool.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<profiles>
		<profile>
			<!--生产环境deploy url-->
			<id>prod</id>
			<distributionManagement>
				<repository>
					<id>maven-releases</id>
					<name>maven-releases</name>
					<url>http://maven.int.ybm100.com/repository/maven-releases/</url>
				</repository>
				<snapshotRepository>
					<id>maven-snapshots</id>
					<name>maven-snapshots</name>
					<url>http://maven.int.ybm100.com/repository/maven-snapshots/</url>
				</snapshotRepository>
			</distributionManagement>
		</profile>
		<profile>
			<!--测试环境deploy url-->
			<id>test</id>
			<distributionManagement>
				<repository>
					<id>maven-releases</id>
					<name>maven-releases</name>
					<url>http://mvn.int.ybm100.com/repository/maven-releases/</url>
				</repository>
				<snapshotRepository>
					<id>maven-snapshots</id>
					<name>maven-snapshots</name>
					<url>http://mvn.int.ybm100.com/repository/maven-snapshots/</url>
				</snapshotRepository>
			</distributionManagement>
		</profile>
	</profiles>

	<pluginRepositories>
		<pluginRepository>
			<id>spring-snapshots</id>
			<url>https://repo.spring.io/snapshot</url>
		</pluginRepository>
		<pluginRepository>
			<id>spring-milestones</id>
			<url>https://repo.spring.io/milestone</url>
		</pluginRepository>
	</pluginRepositories>

	<properties>
		<java.version>21</java.version>
		<guava.version>33.2.1-jre</guava.version>
		<hutool.version>5.8.29</hutool.version>
		<jakarta.version>2.1.1</jakarta.version>
		<springboot.version>3.3.4</springboot.version>
		<mybatis-plus.version>3.5.7</mybatis-plus.version>
		<yudao.version>2.3.0-SNAPSHOT</yudao.version>
		<dubbo.version>3.3.0</dubbo.version>
		<jackson.version>2.17.0</jackson.version>
	</properties>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>21</source>
					<target>21</target>
					<parameters>true</parameters>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<repositories>
		<repository>
			<id>spring-snapshots</id>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
			<url>https://repo.spring.io/snapshot</url>
		</repository>
		<repository>
			<id>spring-milestones</id>
			<url>https://repo.spring.io/milestone</url>
		</repository>
	</repositories>

	<version>1.0-SNAPSHOT</version>

</project>

package com.xyy.saas.eventbus.rocketmq.core.original;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.*;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR> zhangpeng
 * @date : 2023/11/27 18:28
 */
@Slf4j
public class EventBusConcurrentlyMessagePublisher implements MessageListenerConcurrently {

	@Nullable
	private final EventBusMessageMulticaster eventBusMessageMulticaster;

	private final String groupTopicConsumerId;

	public EventBusConcurrentlyMessagePublisher(EventBusMessageMulticaster multicaster, String groupTopicConsumerId) {
		this.eventBusMessageMulticaster = multicaster;
		this.groupTopicConsumerId = groupTopicConsumerId;
	}

	public boolean publishMessage(MessageExt message) {
		Assert.notNull(message, "message must not be null");
		if (this.eventBusMessageMulticaster == null) {
			throw new IllegalStateException("EventBusMessageMulticaster not initialized");
		}
		return eventBusMessageMulticaster.multicastMessage(message, this.groupTopicConsumerId);
	}

	@Override
	public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
		for (MessageExt message : list) {
			String msgBody = new String(message.getBody(), StandardCharsets.UTF_8);
			String msgID = message.getMsgId();
			log.info("并发消费-收到消息 topic {} id {} body {} tag {}", message.getTopic(), msgID, msgBody, message.getTags());
//			// 这里可以添加额外的过滤逻辑
//			if (!message.getTags().equals("dev-tag")) {
//				log.warn("消息被过滤掉，标签不匹配：{}", message.getTags());
//				continue;
//			}
			boolean result = publishMessage(message);
			if (!result) {
				log.error("message {} 执行失败,等待重试 重试次数 {}", msgID, message.getReconsumeTimes());
				return ConsumeConcurrentlyStatus.RECONSUME_LATER;
			}
		}
		return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
//		return consumeMessage(list) ? ConsumeConcurrentlyStatus.CONSUME_SUCCESS : ConsumeConcurrentlyStatus.RECONSUME_LATER;
	}

//	@Override
//	public ConsumeOrderlyStatus consumeMessage(List<MessageExt> list, ConsumeOrderlyContext consumeOrderlyContext) {
//		return consumeMessage(list) ? ConsumeOrderlyStatus.SUCCESS : ConsumeOrderlyStatus.SUSPEND_CURRENT_QUEUE_A_MOMENT;
//	}


//	public boolean consumeMessage(List<MessageExt> list) {
//		boolean result = true;
//		for (MessageExt message : list) {
//			String msgBody = new String(message.getBody(), StandardCharsets.UTF_8);
//			String msgID = message.getMsgId();
//			log.info("收到消息 topic {} id {} body {} tag {}", message.getTopic(), msgID, msgBody, message.getTags());
//			result = publishMessage(message);
//			if (!result) {
//				log.error("message {} 执行失败,等待重试 重试次数 {}", msgID, message.getReconsumeTimes());
//				return result;
//			}
//		}
//		return result;
//	}
}


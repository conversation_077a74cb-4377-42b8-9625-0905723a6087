package com.xyy.saas.eventbus.rocketmq.core.original;

//import com.aliyun.openservices.ons.api.Consumer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.springframework.beans.factory.DisposableBean;

import java.util.Objects;

/**
 * <AUTHOR> wh
 * @date : 2023/11/27 18:26
 * @description:
 */
@Slf4j
@RequiredArgsConstructor
public class EventBusConsumerHolder implements DisposableBean {

    private final DefaultMQPushConsumer consumer;

    @Override
    public void destroy() {
        if (Objects.nonNull(consumer)) {
			log.info("evnet bus rocketmq consumer shutdown");
            this.consumer.shutdown();
        }

    }
}

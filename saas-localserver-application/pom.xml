<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.xyy.saas</groupId>
    <artifactId>saas-localserver</artifactId>
    <version>${revision}</version>
  </parent>

  <artifactId>saas-localserver-application</artifactId>
  <version>${revision}</version>
  <name>saas-localserver-application</name>

  <dependencies>
    <!-- 确保依赖声明在最前面，这样重写yudao的类才能先加载并覆盖 -->
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-user-api</artifactId>
      <version>${inquiry.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>dubbo-spring-boot-starter</artifactId>
          <groupId>org.apache.dubbo</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-multi-env</artifactId>
      <version>${inquiry.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>xyy-common-dubbo-client</artifactId>
          <groupId>com.xyy.common</groupId>
        </exclusion>
<!--         <exclusion> -->
<!--           <artifactId>dubbo-spring-boot-starter</artifactId> -->
<!--           <groupId>org.apache.dubbo</groupId> -->
<!--         </exclusion> -->
        <exclusion>
          <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
          <groupId>com.alibaba.cloud</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
          <groupId>com.alibaba.cloud</groupId>
        </exclusion>
        <exclusion>
          <artifactId>nacos-client</artifactId>
          <groupId>com.alibaba.nacos</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-user-server</artifactId>
      <version>${inquiry.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>biz-soa-starter</artifactId>
          <groupId>com.xyy.saas</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xyy-common-dubbo-client</artifactId>
          <groupId>com.xyy.common</groupId>
        </exclusion>
<!--         <exclusion> -->
<!--           <groupId>com.xyy.saas</groupId> -->
<!--           <artifactId>saas-inquiry-product-server</artifactId> -->
<!--         </exclusion> -->
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-kernel-all</artifactId>
      <version>${inquiry.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>nacos-config-spring-boot-starter</artifactId>
          <groupId>com.alibaba.boot</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xyy-common-dubbo-client</artifactId>
          <groupId>com.xyy.common</groupId>
        </exclusion>
      </exclusions>

    </dependency>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-transmitter-server</artifactId>
      <version>${inquiry.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>biz-soa-starter</artifactId>
          <groupId>com.xyy.saas</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xyy-common-dubbo-client</artifactId>
          <groupId>com.xyy.common</groupId>
        </exclusion>
      </exclusions>
    </dependency>


    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>web-container</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-undertow</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-localserver-inventory-server</artifactId>
      <version>${project.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>rocketmq-event-bus-spring-boot-starter</artifactId>
          <groupId>com.xyy.saas</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
          <groupId>com.alibaba.cloud</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-localserver-purchase-server</artifactId>
      <version>${project.version}</version>
    </dependency>

    <!-- 检测循环依赖 -->
    <dependency>
      <groupId>com.tngtech.archunit</groupId>
      <artifactId>archunit</artifactId>
      <version>1.4.0</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>com.vaadin.external.google</groupId>
          <artifactId>android-json</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <!--
        graal 官方 SDK ，是必须的，
        主要用来注册 lambdaCapturingTypes：org.graalvm.nativeimage.hosted.RuntimeSerialization#registerLambdaCapturingClass
        不要删除，否则会在执行 mvnw -Pnative clean native:compile 命令时报类似这样的错：
        package org.graalvm.nativeimage.hosted does not exist
        -->
    <dependency>
      <groupId>org.graalvm.sdk</groupId>
      <artifactId>graal-sdk</artifactId>
      <version>23.1.2</version>
      <scope>provided</scope>
    </dependency>
    
  </dependencies>

  <build>
    <finalName>${project.artifactId}</finalName>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <parameters>true</parameters>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>native</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.graalvm.buildtools</groupId>
            <artifactId>native-maven-plugin</artifactId>
            <extensions>true</extensions>
            <configuration>
              <!-- imageName用于设置生成的二进制文件名称 -->
              <imageName>${project.build.finalName}</imageName>
              <!-- mainClass用于指定main方法类路径 -->
              <mainClass>${project.main-class}</mainClass>

              <!-- 启用生成调试信息, 开启会报错：https://github.com/oracle/graal/issues/7604  -->
              <!-- <debug>true</debug> -->
              <!-- 本地构建过程启用详细输出 -->
              <verbose>true</verbose>
              <!-- 减少graal编译器执行的优化数量，减少编译时间，使生成的可执行文件体积更小，但会降低峰值吞吐量 -->
              <quickBuild>true</quickBuild>

              <!-- 指定用于原生镜像构建的 JVM参数 -->
              <!-- <jvmArgs> -->
              <!--   <jvmArg>-Dspring.aot.enabled=true</jvmArg> -->
              <!--   <jvmArg>-Dspring.aop.auto=true</jvmArg> -->
              <!--   &lt;!&ndash; 使用基于接口的代理 &ndash;&gt; -->
              <!--   <jvmArg>-Dspring.aop.proxy-target-class=true</jvmArg> -->
              <!-- </jvmArgs> -->
              <buildArgs combine.children="append">
                <buildArg>--no-fallback</buildArg>
                <!-- 支持 http 协议 -->
                <buildArg>--enable-url-protocols=http,https,jar</buildArg>
                <buildArg>-H:+ReportExceptionStackTraces</buildArg>
                <!-- 用于解决数据库中文乱码问题 -->
                <buildArg>-H:+AddAllCharsets</buildArg>
                <!-- <buildArg>-H:IncludeResources='.*'</buildArg>-->
                <!-- <buildArg>-H:IncludeResources='.*/fonts/.*\.(ttf|TTF|otf)$'</buildArg>-->
                <!-- 指定native-image 配置json地址 -->
                <!-- <buildArg>-H:ConfigurationFileDirectories=target/spring-aot/main/resources/META-INF/native-image/com/clint/spring_boot_native,src/main/resources/META-INF/native-image</buildArg>-->
                <!-- <buildArg>-H:ConfigurationFileDirectories=target/spring-aot/main/resources/META-INF/native-image/com/clint/spring_boot_native,src/main/resources/META-INF/native-image</buildArg>-->
                <!-- GraalVM native image 默认不支持 Lambda 表达式，为了让它支持，需要把用到了 Lambda 表达式的类都集中注册一下  -->
                <buildArg>--features=com.xyy.saas.localserver.natives.RuntimeRegistrationFeature</buildArg>
                <buildArg>--features=org.springframework.aot.nativex.feature.PreComputeFieldFeature</buildArg>

                <buildArg>-H:+UnlockExperimentalVMOptions</buildArg>
                <buildArg>-H:+BuildReport</buildArg>

                <buildArg>--report-unsupported-elements-at-runtime</buildArg>
                <buildArg>--allow-incomplete-classpath</buildArg>

                <!-- 这是 Windows NT 平台编译器的独有编译参数，SUBSYSTEM 后面可以跟多个参数，WINDOWS 代表窗口程序，而 CONSOLE 会打开一个黑色的控制台来显示日志等信息。支持的参数可以见微软的文档：https://learn.microsoft.com/zh-cn/cpp/build/reference/subsystem-specify-subsystem?view=msvc-170 -->
                <buildArg>-H:NativeLinkerOption=/SUBSYSTEM:CONSOLE</buildArg>
                <buildArg>-J-Dfile.encoding=UTF-8</buildArg>
                <buildArg>-J-Dstdout.encoding=UTF-8</buildArg>
                <buildArg>-J-Dstderr.encoding=UTF-8</buildArg>
                <buildArg>-J-Dconsole.encoding=UTF-8</buildArg>
                <buildArg>-J-Duser.language=en</buildArg>
                <buildArg>-J-Duser.region=US</buildArg>
                <buildArg>--add-opens=java.base/java.nio=ALL-UNNAMED</buildArg>
                <buildArg>--add-opens=java.base/jdk.internal.misc=ALL-UNNAMED</buildArg>
                <buildArg>--add-opens=java.base/jdk.internal.ref=ALL-UNNAMED</buildArg>
                <buildArg>--gc=serial</buildArg>

                <buildArg>--initialize-at-build-time=org.redisson.misc.BiHashMap</buildArg>
                <buildArg>--initialize-at-build-time=org.sqlite.util.ProcessRunner</buildArg>

                <buildArg>--initialize-at-run-time=sun.net.dns.ResolverConfigurationImpl</buildArg>
                
              </buildArgs>
            </configuration>
            <executions>
              <execution>
                <id>build-native</id>
                <goals>
                  <goal>compile-no-fork</goal>
                </goals>
                <phase>package</phase>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-maven-plugin</artifactId>
            <version>${maven-springboot-plugin.version}</version>
            <executions>
              <execution>
                <id>process-aot</id>
                <goals>
                  <goal>process-aot</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <excludes>
                <exclude>
                  <groupId>org.projectlombok</groupId>
                  <artifactId>lombok</artifactId>
                </exclude>
              </excludes>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>nativeTest</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-maven-plugin</artifactId>
            <version>${maven-springboot-plugin.version}</version>
            <executions>
              <execution>
                <id>process-test-aot</id>
                <goals>
                  <goal>process-test-aot</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.graalvm.buildtools</groupId>
            <artifactId>native-maven-plugin</artifactId>
            <configuration>
              <classesDirectory>${project.build.outputDirectory}</classesDirectory>
              <metadataRepository>
                <enabled>true</enabled>
              </metadataRepository>
              <requiredVersion>22.3</requiredVersion>
            </configuration>
            <executions>
              <execution>
                <id>native-test</id>
                <goals>
                  <goal>test</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

</project>

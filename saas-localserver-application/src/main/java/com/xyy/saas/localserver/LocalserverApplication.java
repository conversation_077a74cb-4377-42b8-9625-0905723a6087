package com.xyy.saas.localserver;

import cn.iocoder.yudao.framework.operatelog.config.YudaoOperateLogConfiguration;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusInnerInterceptorAutoConfiguration;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.xyy.saas.datasync.client.EnableDataSyncScan;
import com.xyy.saas.datasync.sqlite.autoconfigure.LocalDBConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;

@SpringBootApplication(scanBasePackages = {"com.xyy.saas", "cn.iocoder.yudao.module"},
exclude = {
        YudaoOperateLogConfiguration.class,
//        MybatisPlusInnerInterceptorAutoConfiguration.class,
//        LocalDBConfig.class,
})
@EnableDataSyncScan(scanBasePackages = "com.xyy.saas.localserver.entity",
        excludePackages = "cn.iocoder.yudao.module.infra.dal.dataobject.demo")
public class LocalserverApplication {
    public static void main(String[] args) {
        SpringApplication.run(LocalserverApplication.class, args);
    }

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        return new MybatisPlusInterceptor();
    }
}
{"types": [{"name": "ch.qos.logback.classic.model.ConfigurationModel"}, {"name": "ch.qos.logback.classic.model.LoggerModel"}, {"name": "ch.qos.logback.classic.model.RootLoggerModel"}, {"name": "ch.qos.logback.core.model.AppenderModel"}, {"name": "ch.qos.logback.core.model.AppenderRefModel"}, {"name": "ch.qos.logback.core.model.ComponentModel"}, {"name": "ch.qos.logback.core.model.ImplicitModel"}, {"name": "ch.qos.logback.core.model.Model"}, {"name": "ch.qos.logback.core.model.NamedComponentModel"}, {"name": "ch.qos.logback.core.model.NamedModel"}, {"name": "ch.qos.logback.core.model.PropertyModel"}, {"name": "java.util.ArrayList"}], "lambdaCapturingTypes": [], "proxies": []}
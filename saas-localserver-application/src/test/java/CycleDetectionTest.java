import com.tngtech.archunit.core.domain.JavaClasses;
import com.tngtech.archunit.core.importer.ClassFileImporter;
import com.tngtech.archunit.library.dependencies.SlicesRuleDefinition;
import org.junit.jupiter.api.Test;

import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public class CycleDetectionTest {

    // 修改为你的两个项目编译输出路径
//    private static final String PROJECT1_CLASSES = "../project1/target/classes";
//    private static final String PROJECT2_CLASSES = "../project2/target/classes";

    private static final List<String> PACKAGES = Arrays.asList(
            "/Users/<USER>/codes/xyy/inquiry/saas-inquiry-kernel/saas-inquiry-drugstore/saas-inquiry-drugstore-server/target/classes",
            "/Users/<USER>/codes/xyy/inquiry/saas-inquiry-kernel/saas-inquiry-hospital/saas-inquiry-hospital-server/target/classes",
//            "/Users/<USER>/codes/xyy/inquiry/saas-inquiry-kernel/saas-inquiry-im/saas-inquiry-im-server/target/classes",
//            "/Users/<USER>/codes/xyy/inquiry/saas-inquiry-kernel/saas-inquiry-patient/saas-inquiry-patient-server/target/classes",
//            "/Users/<USER>/codes/xyy/inquiry/saas-inquiry-kernel/saas-inquiry-pharmacist/saas-inquiry-pharmacist-server/target/classes",
//            "/Users/<USER>/codes/xyy/inquiry/saas-inquiry-kernel/saas-inquiry-signature/saas-inquiry-signature-server/target/classes",
            "/Users/<USER>/codes/xyy/inquiry/saas-inquiry-system/saas-inquiry-product/saas-inquiry-product-server/target/classes",
            "/Users/<USER>/codes/xyy/inquiry/saas-inquiry-system/saas-inquiry-user/saas-inquiry-user-server/target/classes",
            "/Users/<USER>/codes/xyy/inquiry/saas-inquiry-system/saas-transmitter/saas-transmitter-server/target/classes"
    );

    @Test
    void detectApiServiceCyclesExcludeLazy() {
        String[] classPaths = PACKAGES.toArray(new String[0]);
        String[] packagePatterns = new String[]{
                ".*\\.(api|service)\\..*"
        };
//        CycleDetectorRefined.detectApiServiceCycles(classPaths, packagePatterns);
        // 遇到依赖中的扫不到
        EnhancedCycleDetector.detectCycles(classPaths, Arrays.stream(packagePatterns).toList(), false);
    }

    @Test
    void detectApiServiceCycles() {
        // 导入两个项目的class文件
        JavaClasses classes = new ClassFileImporter()
                .importPaths(
                        PACKAGES.stream().map(Paths::get).toList()
                );
        // 检测循环依赖并打印详细信息
        SlicesRuleDefinition.slices()
                .matching("..(*).(api|service).(*)..") // 匹配api/service包
                .should().beFreeOfCycles()
                .because("禁止ApiImpl和ServiceImpl之间的循环依赖")
                .check(classes);
    }
}

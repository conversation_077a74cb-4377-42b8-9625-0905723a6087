import com.tngtech.archunit.core.domain.*;
import com.tngtech.archunit.core.importer.ClassFileImporter;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import java.nio.file.Path;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class EnhancedCycleDetector {

    // 接口解析器：用于存储接口->实现类映射
    private static class InterfaceResolver {
        private final Map<String, Set<String>> implementationsMap = new HashMap<>();
        private final Map<String, Set<String>> inheritanceMap = new HashMap<>();
//        private final Set<String> allClasses;

        public InterfaceResolver() {
//            this.allClasses = allClasses;
        }

        // 添加接口/抽象类和实现类的映射
        public void addImplementation(JavaClass provider, JavaClass implementer) {
            String interfaceName = provider.getName();
            implementationsMap
                    .computeIfAbsent(interfaceName, k -> new HashSet<>())
                    .add(implementer.getName());
        }

        // 递归获取接口/抽象类的所有实现类（包括子接口的实现）
        public Set<String> getAllImplementations(String className) {
            Set<String> results = new HashSet<>();
            Deque<String> queue = new ArrayDeque<>();
            queue.add(className);

            while (!queue.isEmpty()) {
                String current = queue.poll();

                // 直接实现类
                implementationsMap.getOrDefault(current, Collections.emptySet())
                        .stream()
//                        .filter(allClasses::contains)
                        .forEach(impl -> {
                            if (results.add(impl)) {
                                queue.add(impl); // 考虑实现类本身也可能是抽象
                            }
                        });

                // 继承关系（子接口/子类）
                inheritanceMap.getOrDefault(current, Collections.emptySet())
                        .stream()
//                        .filter(allClasses::contains)
                        .forEach(child -> {
                            if (results.add(child)) {
                                queue.add(child);
                            }
                        });
            }

            return results;
        }

        // 添加继承关系
        public void addInheritance(JavaClass superClass, JavaClass subClass) {
            inheritanceMap
                    .computeIfAbsent(superClass.getName(), k -> new HashSet<>())
                    .add(subClass.getName());
        }
    }

    // 主检测方法
    public static void detectCycles(String[] classPaths, List<String> packageRegexes, boolean excludeLazy) {
        List<Pattern> packagePatterns = packageRegexes.stream()
                .map(Pattern::compile)
                .collect(Collectors.toList());

        // 导入所有类
        JavaClasses allClasses = new ClassFileImporter().importPaths(
                Arrays.stream(classPaths).map(Path::of).toArray(Path[]::new)
        );

        // 获取相关类
        Set<JavaClass> relevantClasses = getRelevantClasses(allClasses, packagePatterns);
        Set<String> relevantClassNames = relevantClasses.stream()
                .map(JavaClass::getName)
                .collect(Collectors.toSet());

        if (relevantClassNames.isEmpty()) {
            System.out.println("⚠️ 未找到相关类，请检查包匹配规则");
            return;
        }

        // 1. 构建接口解析器
        InterfaceResolver resolver = buildInterfaceResolver(allClasses);

        System.out.println("\n====== 循环依赖深度扫描 ======");
        System.out.println("扫描类数量: " + relevantClasses.size());
        System.out.println("注册接口/抽象类: " + resolver.implementationsMap.size());
        System.out.println("包匹配模式: " + String.join(", ", packageRegexes));
        System.out.println("排除@Lazy依赖: " + excludeLazy);

        // 2. 构建依赖图（考虑接口实现）
        Map<String, Set<String>> dependencyGraph = buildDependencyGraph(
                relevantClasses,
                relevantClassNames,
                resolver,
                excludeLazy
        );

        // 3. 检查无效依赖（调试用）
        System.out.println("\n依赖图统计:");
        System.out.println("  - 已分析节点: " + dependencyGraph.size());
        long totalEdges = dependencyGraph.values().stream().mapToLong(Set::size).sum();
        System.out.println("  - 总依赖边: " + totalEdges);

        // 4. 检测循环
        List<List<String>> cycles = findCyclesInGraph(dependencyGraph);
        printCycleResults(cycles);
    }

    // 获取相关类（正则匹配）
    private static Set<JavaClass> getRelevantClasses(JavaClasses allClasses, List<Pattern> patterns) {
        return allClasses.stream()
                .filter(jc -> jc.getPackageName() != null)
                .filter(jc -> patterns.stream().anyMatch(p -> p.matcher(jc.getPackageName()).matches()))
                .collect(Collectors.toSet());
    }

    // 构建接口解析器
    private static InterfaceResolver buildInterfaceResolver(JavaClasses classes) {
        InterfaceResolver resolver = new InterfaceResolver();

        for (JavaClass clazz : classes) {
            String className = clazz.getName();
//            if (!relevantNames.contains(className)) continue;

            // 1. 收集实现的接口
            Set<JavaClass> allInterfaces = clazz.getAllRawInterfaces();
            for (JavaClass intf : allInterfaces) {
                // 只关注相关接口
//                if (relevantNames.contains(intf.getName())) {
                    resolver.addImplementation(intf, clazz);

                    // 递归添加实现的父接口
                    JavaClass superIntf = intf;
                    while (superIntf != null) {
                        resolver.addInheritance(superIntf, intf);
                        superIntf = superIntf.getRawSuperclass().orElse(null);
                    }
//                }
            }

            // 2. 收集继承的抽象类
            JavaClass superClass = clazz.getRawSuperclass().orElse(null);
            while (superClass != null && !superClass.getName().equals(Object.class.getName())) {
                if (
//                        relevantNames.contains(superClass.getName()) &&
                        (isAbstract(superClass) || superClass.isInterface())) {

                    resolver.addImplementation(superClass, clazz);

                    // 递归添加父类继承关系
                    JavaClass parent = superClass;
                    while (parent != null) {
                        resolver.addInheritance(parent, superClass);
                        parent = parent.getRawSuperclass().orElse(null);
                        if (parent != null && parent.getName().equals(Object.class.getName())) {
                            break;
                        }
                    }
                }
                superClass = superClass.getRawSuperclass().orElse(null);
            }
        }

        return resolver;
    }

    private static boolean isAbstract(JavaClass javaClass) {
        return javaClass.getModifiers().contains(JavaModifier.ABSTRACT);
    }

    // 构建依赖图（含接口解析）
    private static Map<String, Set<String>> buildDependencyGraph(
            Set<JavaClass> relevantClasses,
            Set<String> relevantNames,
            InterfaceResolver resolver,
            boolean excludeLazy
    ) {
        Map<String, Set<String>> graph = new HashMap<>();

        // 初始化所有节点
        relevantNames.forEach(clazz -> graph.putIfAbsent(clazz, new HashSet<>()));

        // 收集各种依赖
        for (JavaClass clazz : relevantClasses) {
            String source = clazz.getName();

            // 1. 字段依赖
            collectFieldDependencies(clazz, graph, relevantNames, resolver, excludeLazy);

            // 2. 构造方法依赖
            collectConstructorDependencies(clazz, graph, relevantNames, resolver, excludeLazy);

            // 3. 方法依赖
            collectMethodDependencies(clazz, graph, relevantNames, resolver, excludeLazy);
        }

        return graph;
    }

    // 解析可能的接口目标
    private static void addDependencyEdge(
            String source,
            JavaClass targetClass,
            Map<String, Set<String>> graph,
            Set<String> relevantNames,
            InterfaceResolver resolver
    ) {
        String target = targetClass.getName();

        // 1. 直接添加目标类（如果相关）
        if (relevantNames.contains(target)) {
            graph.get(source).add(target);
        }

        // 2. 如果目标是接口/抽象类，添加其实现类
        if (targetClass.isInterface() || isAbstract(targetClass)) {
            Set<String> implementations = resolver.getAllImplementations(target);
            for (String impl : implementations) {
                if (!source.equals(impl)
//                        && relevantNames.contains(impl)
                ) {
                    graph.get(source).add(impl);
                }
            }
        }
    }

    private static boolean isInjected(JavaField field) {
        return field.isAnnotatedWith(Resource.class) || field.isAnnotatedWith(Autowired.class);
    }
    private static boolean isLazy(JavaField field) {
        return field.isAnnotatedWith(Lazy.class);
    }
    private static boolean isInjected(JavaParameter field) {
        return field.isAnnotatedWith(Resource.class) || field.isAnnotatedWith(Autowired.class);
    }
    private static boolean isLazy(JavaParameter field) {
        return field.isAnnotatedWith(Lazy.class);
    }
    private static boolean isInjected(JavaMethod field) {
        return field.isAnnotatedWith(Resource.class) || field.isAnnotatedWith(Autowired.class);
    }

    // 收集字段依赖（含接口解析）
    private static void collectFieldDependencies(
            JavaClass javaClass,
            Map<String, Set<String>> graph,
            Set<String> relevantNames,
            InterfaceResolver resolver,
            boolean excludeLazy
    ) {
        String source = javaClass.getName();

        // 过滤@Resource字段并排除@Lazy
        Set<JavaField> resourceFields = javaClass.getAllFields().stream()
                .filter(f -> isInjected(f) &&
                        !(excludeLazy && isLazy(f)))
                .collect(Collectors.toSet());

        for (JavaField field : resourceFields) {
            JavaClass fieldType = field.getRawType();
            addDependencyEdge(source, fieldType, graph, relevantNames, resolver);
        }
    }

    // 收集构造方法依赖（含接口解析）
    private static void collectConstructorDependencies(
            JavaClass javaClass,
            Map<String, Set<String>> graph,
            Set<String> relevantNames,
            InterfaceResolver resolver,
            boolean excludeLazy
    ) {
        String source = javaClass.getName();

        for (JavaConstructor constructor : javaClass.getConstructors()) {
            for (JavaParameter param : constructor.getParameters()) {
                if (isInjected(param) &&
                        !(excludeLazy && isLazy(param))) {

                    JavaClass paramType = param.getRawType();
                    addDependencyEdge(source, paramType, graph, relevantNames, resolver);
                }
            }
        }
    }

    // 收集方法依赖（含接口解析）
    private static void collectMethodDependencies(
            JavaClass javaClass,
            Map<String, Set<String>> graph,
            Set<String> relevantNames,
            InterfaceResolver resolver,
            boolean excludeLazy
    ) {
        String source = javaClass.getName();

        for (JavaMethod method : javaClass.getMethods()) {
            if (!isInjected(method)) continue;

            for (JavaParameter param : method.getParameters()) {
                if (!(excludeLazy && isLazy(param))) {
                    JavaClass paramType = param.getRawType();
                    addDependencyEdge(source, paramType, graph, relevantNames, resolver);
                }
            }
        }
    }

    // Tarjan算法检测循环
    private static List<List<String>> findCyclesInGraph(Map<String, Set<String>> graph) {
//        return new TarjanCycleDetector(graph).findCycles();
//        return new TarjanCycleDetector2(graph).findCycles();
        return new OptimizedCycleDetector(graph).findCycles();
    }

    // 打印结果
    private static void printCycleResults(List<List<String>> cycles) {
        if (cycles.isEmpty()) {
            System.out.println("\n✅ 未检测到循环依赖");
            return;
        }

        System.out.printf("\n⚠️ 发现 %d 个循环依赖链:\n", cycles.size());

        for (int i = 0; i < cycles.size(); i++) {
            List<String> cycle = cycles.get(i);
            System.out.printf("\n循环链 #%d (长度: %d):", i + 1, cycle.size());

            for (int j = 0; j < cycle.size(); j++) {
                String arrow = j == 0 ? "⟱ " : "→ ";
                System.out.printf("\n  %s%s", arrow, abbreviateClassName(cycle.get(j)));
            }
            System.out.printf("\n  ⟲ 回到: %s", abbreviateClassName(cycle.get(0)));
            System.out.println();
        }
    }

    // 简化类名显示
    private static String abbreviateClassName(String fullName) {
        String[] parts = fullName.split("\\.");
        int len = parts.length;
        if (len <= 3) return fullName;
        return ".." + parts[len-3] + "." + parts[len-2] + "." + parts[len-1];
    }

    // Tarjan 强连通分量算法实现
    static class TarjanCycleDetector {
        private final Map<String, Set<String>> graph;
        private final Map<String, Integer> indexes = new HashMap<>();
        private final Map<String, Integer> lowLinks = new HashMap<>();
        private final Deque<String> stack = new ArrayDeque<>();
        private final List<List<String>> cycles = new ArrayList<>();
        private int index = 0;

        public TarjanCycleDetector(Map<String, Set<String>> graph) {
            this.graph = graph;
        }

        public List<List<String>> findCycles() {
            graph.keySet().forEach(node -> {
                if (!indexes.containsKey(node)) {
                    strongConnect(node);
                }
            });
            return deduplicateCycles(cycles);
        }

        private void strongConnect(String node) {
            indexes.put(node, index);
            lowLinks.put(node, index);
            index++;
            stack.push(node);

            // 处理所有邻居节点
            for (String neighbor : graph.getOrDefault(node, Collections.emptySet())) {
                if (!indexes.containsKey(neighbor)) {
                    strongConnect(neighbor);
                    lowLinks.put(node, Math.min(lowLinks.get(node), lowLinks.get(neighbor)));
                } else if (stack.contains(neighbor)) {
                    lowLinks.put(node, Math.min(lowLinks.get(node), indexes.get(neighbor)));
                }
            }

            // 如果找到强连通分量
            if (lowLinks.get(node).equals(indexes.get(node))) {
                List<String> scc = new ArrayList<>();
                String popNode;
                do {
                    popNode = stack.pop();
                    scc.add(popNode);
                } while (!popNode.equals(node));

                // 保留有效循环（至少2个节点）
                if (scc.size() > 1) {
                    // 标准化环（最小节点开头）
                    String minNode = Collections.min(scc);
                    int startIndex = scc.indexOf(minNode);
                    Collections.rotate(scc, -startIndex);
                    cycles.add(scc);
                }
            }
        }

        // 去重循环（忽略方向）
        private List<List<String>> deduplicateCycles(List<List<String>> foundCycles) {
            Map<String, List<String>> cycleMap = new LinkedHashMap<>();
            for (List<String> cycle : foundCycles) {
                // 创建规范签名
                String sorted = cycle.stream()
                        .sorted()
                        .collect(Collectors.joining("→"));

                // 使用最小节点开头的表示法
                String minNode = Collections.min(cycle);
                int startIndex = cycle.indexOf(minNode);
                List<String> normalized = new ArrayList<>(cycle.subList(startIndex, cycle.size()));
                normalized.addAll(cycle.subList(0, startIndex));

                cycleMap.putIfAbsent(sorted, normalized);
            }
            return new ArrayList<>(cycleMap.values());
        }
    }


    static class TarjanCycleDetector2 {
        private final Map<String, Set<String>> graph;
        private final Map<String, Integer> indexMap = new HashMap<>();
        private final Map<String, Integer> lowLinkMap = new HashMap<>();
        private final Deque<String> stack = new ArrayDeque<>();
        private final Set<String> onStack = new HashSet<>();
        private final Set<List<String>> cycles = new LinkedHashSet<>(); // 使用Set避免重复
        private int index = 0;
        private int processedNodes = 0;

        public TarjanCycleDetector2(Map<String, Set<String>> graph) {
            this.graph = graph;
            // 添加自引用检查
            for (String node : graph.keySet()) {
                if (graph.getOrDefault(node, Collections.emptySet()).contains(node)) {
                    cycles.add(Arrays.asList(node));
                }
            }
        }

        public List<List<String>> findCycles() {
            long startTime = System.currentTimeMillis();
            int totalNodes = graph.keySet().size();

            for (String node : graph.keySet()) {
                if (!indexMap.containsKey(node)) {
                    strongConnect(node);
                }

                // 进度报告（每处理50个节点报告一次）
                if (++processedNodes % 50 == 0) {
                    long elapsed = System.currentTimeMillis() - startTime;
                    System.out.printf("进度: %d/%d 节点 (%.1f%%), 耗时: %dms, 已发现环: %d\n",
                        processedNodes, totalNodes,
                        (processedNodes * 100.0 / totalNodes),
                        elapsed, cycles.size());
                }
            }

            return new ArrayList<>(cycles);
        }

        private void strongConnect(String node) {
            // 初始化当前节点
            indexMap.put(node, index);
            lowLinkMap.put(node, index);
            index++;
            stack.push(node);
            onStack.add(node);

            // 处理所有邻居节点
            Set<String> neighbors = graph.getOrDefault(node, Collections.emptySet());
            for (String neighbor : neighbors) {
                // 跳过自引用（已单独处理）
                if (neighbor.equals(node)) continue;

                if (!indexMap.containsKey(neighbor)) {
                    // 递归访问未处理邻居
                    strongConnect(neighbor);
                    lowLinkMap.put(node, Math.min(lowLinkMap.get(node), lowLinkMap.get(neighbor)));
                } else if (onStack.contains(neighbor)) {
                    // 发现反向边（可能形成环）
                    lowLinkMap.put(node, Math.min(lowLinkMap.get(node), indexMap.get(neighbor)));
                }
            }

            // 找到SCC根节点
            if (lowLinkMap.get(node).equals(indexMap.get(node))) {
                collectSCC(node);
            }
        }
        private void collectSCC(String root) {
            List<String> scc = new ArrayList<>();
            String current;
            do {
                current = stack.pop();
                onStack.remove(current);
                scc.add(current);
            } while (!current.equals(root));

            // 当SCC包含多个节点时，直接将其作为一个环添加
            if (scc.size() > 1) {
                // 标准化环：按字典序排序后找最小元素作为起点
                Collections.sort(scc);
                cycles.add(new ArrayList<>(scc));
            }
        }

        // 简化的环验证方法
        private boolean isValidCycle(List<String> cycle) {
            if (cycle.size() < 2) return false;

            // 检查是否形成有效的依赖环
            for (int i = 0; i < cycle.size(); i++) {
                String current = cycle.get(i);
                String next = cycle.get((i + 1) % cycle.size());

                Set<String> dependencies = graph.get(current);
                if (dependencies == null || !dependencies.contains(next)) {
                    return false;
                }
            }
            return true;
        }
    }

    // 优化的循环检测器 - 专注于性能
    static class OptimizedCycleDetector {
        private final Map<String, Set<String>> graph;
        private final Map<String, Integer> indexMap = new HashMap<>();
        private final Map<String, Integer> lowLinkMap = new HashMap<>();
        private final Deque<String> stack = new ArrayDeque<>();
        private final Set<String> onStack = new HashSet<>();
        private final Set<String> visited = new HashSet<>();
        private final List<List<String>> cycles = new ArrayList<>();
        private int index = 0;
        private int processedNodes = 0;

        public OptimizedCycleDetector(Map<String, Set<String>> graph) {
            this.graph = graph;
        }

        public List<List<String>> findCycles() {
            long startTime = System.currentTimeMillis();
            int totalNodes = graph.size();

            System.out.println("开始优化的循环检测，总节点数: " + totalNodes);

            // 首先检查自引用
            for (String node : graph.keySet()) {
                Set<String> neighbors = graph.get(node);
                if (neighbors != null && neighbors.contains(node)) {
                    cycles.add(Arrays.asList(node));
                }
            }

            // Tarjan算法检测强连通分量
            for (String node : graph.keySet()) {
                if (!visited.contains(node)) {
                    tarjanSCC(node);
                }

                // 进度报告
                if (++processedNodes % 100 == 0) {
                    long elapsed = System.currentTimeMillis() - startTime;
                    double progress = (processedNodes * 100.0 / totalNodes);
                    System.out.printf("进度: %d/%d (%.1f%%), 耗时: %dms, 发现环: %d\n",
                        processedNodes, totalNodes, progress, elapsed, cycles.size());
                }
            }

            long totalTime = System.currentTimeMillis() - startTime;
            System.out.printf("循环检测完成，总耗时: %dms, 发现环: %d个\n", totalTime, cycles.size());

            return cycles;
        }

        private void tarjanSCC(String node) {
            indexMap.put(node, index);
            lowLinkMap.put(node, index);
            index++;
            stack.push(node);
            onStack.add(node);
            visited.add(node);

            Set<String> neighbors = graph.get(node);
            if (neighbors != null) {
                for (String neighbor : neighbors) {
                    if (neighbor.equals(node)) continue; // 跳过自引用

                    if (!visited.contains(neighbor)) {
                        tarjanSCC(neighbor);
                        lowLinkMap.put(node, Math.min(lowLinkMap.get(node), lowLinkMap.get(neighbor)));
                    } else if (onStack.contains(neighbor)) {
                        lowLinkMap.put(node, Math.min(lowLinkMap.get(node), indexMap.get(neighbor)));
                    }
                }
            }

            // 如果是SCC的根节点
            if (lowLinkMap.get(node).equals(indexMap.get(node))) {
                List<String> scc = new ArrayList<>();
                String current;
                do {
                    current = stack.pop();
                    onStack.remove(current);
                    scc.add(current);
                } while (!current.equals(node));

                // 只有包含多个节点的SCC才可能包含环
                if (scc.size() > 1) {
                    // 简单验证：检查是否真的形成环
                    if (isValidSCC(scc)) {
                        // 标准化环表示
                        Collections.sort(scc);
                        cycles.add(scc);
                    }
                }
            }
        }

        // 验证SCC是否包含有效的循环
        private boolean isValidSCC(List<String> scc) {
            Set<String> sccSet = new HashSet<>(scc);

            // 检查SCC内部是否有足够的连接形成环
            for (String node : scc) {
                Set<String> neighbors = graph.get(node);
                if (neighbors == null) continue;

                // 检查是否有指向SCC内其他节点的边
                boolean hasInternalEdge = neighbors.stream()
                    .anyMatch(neighbor -> sccSet.contains(neighbor) && !neighbor.equals(node));

                if (hasInternalEdge) {
                    return true; // 找到内部连接，确认是有效环
                }
            }
            return false;
        }
    }
}

<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.xyy.saas</groupId>
    <artifactId>saas-localserver</artifactId>
    <version>${revision}</version>
  </parent>

  <artifactId>saas-localserver-business</artifactId>
  <description>saas-药店-本地-业务服务</description>
  <packaging>pom</packaging>

  <modules>
    <module>saas-localserver-inventory</module>
    <module>saas-localserver-purchase</module>
  </modules>


  <dependencies>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>biz-soa-starter</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>dubbo-spring-boot-starter</artifactId>
          <groupId>org.apache.dubbo</groupId>
        </exclusion>
        <exclusion>
          <artifactId>nacos-client</artifactId>
          <groupId>com.alibaba.nacos</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
          <groupId>com.alibaba.cloud</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
          <groupId>com.alibaba.cloud</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-localserver-entity</artifactId>
      <version>${project.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>yudao-module-bpm-biz</artifactId>
          <groupId>cn.iocoder.boot</groupId>
        </exclusion>
        <exclusion>
          <artifactId>yudao-module-bpm-api</artifactId>
          <groupId>cn.iocoder.boot</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-protection</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>redisson-spring-boot-starter</artifactId>
          <groupId>org.redisson</groupId>
        </exclusion>
        <exclusion>
          <artifactId>yudao-spring-boot-starter-redis</artifactId>
          <groupId>cn.iocoder.boot</groupId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
</project>

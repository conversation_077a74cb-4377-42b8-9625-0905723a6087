package com.xyy.saas.localserver.inventory.api.campon.dto;

import com.xyy.saas.localserver.inventory.enums.AreaTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 库存批号DTO
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryCampOnLotItemDTO implements Serializable {

    private static final long serialVersionUID = 633006380198363949L;

    /* 预占详情单id */
    private Long detailId;

    /* 批次号guid */
    private String batchGuid;

    /* 追踪维度 */
    private String tracePref;

    /* 商品编号 */
    private String productPref;

    /* 库存批号 */
    private String lotNo;

    /* 货架编号 */
    private String positionGuid;

    /* 货区 */
    private AreaTypeEnum areaType;

    /* 变动数量 */
    private BigDecimal changeNumber;

    /* 成本价 */
    private BigDecimal costPrice;

    /* 供应商guid */
    private String supplierGuid;

    /* 入库含税价 */
    private BigDecimal inTaxPrice;

    /* 入库税率 */
    private BigDecimal taxRate;

    /* 灭菌批号 */
    private String sterilizationBatchNo;

}

package com.xyy.saas.localserver.inventory.api.inventory;

import com.xyy.saas.localserver.inventory.api.inventory.dto.InventoryDTO;
import com.xyy.saas.localserver.inventory.api.inventory.dto.InventoryQueryDTO;

import java.util.List;

/**
 * 商品库存查询服务Api
 */
public interface InventoryApi {

    /**
     * 获取商品库存列表
     * @param queryDTO
     * @return
     */
    List<InventoryDTO> getInventory(InventoryQueryDTO queryDTO);

}

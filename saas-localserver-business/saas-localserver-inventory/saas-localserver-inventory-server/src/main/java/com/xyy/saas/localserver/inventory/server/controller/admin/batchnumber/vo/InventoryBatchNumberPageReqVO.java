package com.xyy.saas.localserver.inventory.server.controller.admin.batchnumber.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商品批次库存分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InventoryBatchNumberPageReqVO extends PageParam {

    @Schema(description = "批次号guid", example = "7546")
    private String guid;

    @Schema(description = "商品编号")
    private String productPref;

    @Schema(description = "批号")
    private String lotNo;

    @Schema(description = "货位guid", example = "19216")
    private String positionGuid;

    @Schema(description = "库存数量")
    private BigDecimal stockNumber;

    @Schema(description = "出库预占")
    private BigDecimal campOnNumber;

    @Schema(description = "单据类型", example = "2")
    private Integer billType;

    @Schema(description = "单据编号")
    private String billNo;

    @Schema(description = "供应商guid", example = "4269")
    private String supplierGuid;

    @Schema(description = "入库含税价", example = "31417")
    private BigDecimal inTaxPrice;

    @Schema(description = "入库税率")
    private BigDecimal taxRate;

    @Schema(description = "灭菌批号")
    private String sterilizationBatchNo;

    @Schema(description = "追踪维度", example = "19654")
    private String tracePref;

    @Schema(description = "来源追踪维度", example = "19654")
    private String sourceTracePref;

    @Schema(description = "总部追踪维度", example = "19654")
    private String rootTracePref;

    @Schema(description = "入库时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] inTime;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "版本号")
    private Long version;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
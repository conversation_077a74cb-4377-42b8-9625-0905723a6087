package com.xyy.saas.localserver.inventory.server.controller.admin.change.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 库存变动明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InventoryChangeDetailPageReqVO extends PageParam {

    @Schema(description = "单据类型/摘要", example = "1")
    private Integer billType;

    @Schema(description = "单据编号")
    private String billNo;

    @Schema(description = "领域单据时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] billTime;

    @Schema(description = "商品编号")
    private String productPref;

    @Schema(description = "批号")
    private String lotNo;

    @Schema(description = "货位guid", example = "26194")
    private String positionGuid;

    @Schema(description = "批次库存guid", example = "27812")
    private String batchGuid;

    @Schema(description = "供应商编号", example = "28869")
    private String supplierGuid;

    @Schema(description = "入库数量")
    private BigDecimal inNumber;

    @Schema(description = "入库单价", example = "5864")
    private BigDecimal inPrice;

    @Schema(description = "入库总金额")
    private BigDecimal inAmount;

    @Schema(description = "出库数量")
    private BigDecimal outNumber;

    @Schema(description = "出库单价", example = "15400")
    private BigDecimal outPrice;

    @Schema(description = "出库总金额")
    private BigDecimal outAmount;

    @Schema(description = "批次库存结存数量")
    private BigDecimal batchNumber;

    @Schema(description = "批次库存结存金额")
    private BigDecimal batchAmount;

    @Schema(description = "商品结存库存数量")
    private BigDecimal stockNumber;

    @Schema(description = "商品结存库存金额")
    private BigDecimal stockAmount;

    @Schema(description = "商品成本价", example = "22099")
    private BigDecimal costPrice;

    @Schema(description = "税率")
    private BigDecimal taxRate;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "扩展信息")
    private String ext;

    @Schema(description = "来源guid")
    private String sourceGuid;

    @Schema(description = "总部guid")
    private String rootGuid;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
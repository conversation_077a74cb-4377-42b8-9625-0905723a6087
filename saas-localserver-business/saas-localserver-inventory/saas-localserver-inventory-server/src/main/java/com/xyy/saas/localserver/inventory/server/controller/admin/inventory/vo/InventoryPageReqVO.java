package com.xyy.saas.localserver.inventory.server.controller.admin.inventory.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商品库存分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InventoryPageReqVO extends PageParam {

    @Schema(description = "商品编号")
    private String productPref;

    @Schema(description = "库存数量")
    private BigDecimal stockNumber;

    @Schema(description = "预占数量")
    private BigDecimal campOnNumber;

    @Schema(description = "成本均价", example = "32481")
    private BigDecimal costPrice;

    @Schema(description = "库存总金额")
    private BigDecimal stockAmount;

    @Schema(description = "最后一次采购入库价", example = "16933")
    private BigDecimal lastInPrice;

    @Schema(description = "最后一次供应商", example = "24016")
    private String lastSupplierGuid;

    @Schema(description = "最后一次入库时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] lastInTime;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "库存上限")
    private BigDecimal storeMaxLimit;

    @Schema(description = "库存下限")
    private BigDecimal storeMinLimit;

    @Schema(description = "版本号")
    private Long version;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
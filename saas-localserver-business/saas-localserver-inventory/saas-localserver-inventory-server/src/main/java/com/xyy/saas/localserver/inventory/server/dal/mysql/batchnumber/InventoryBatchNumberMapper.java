package com.xyy.saas.localserver.inventory.server.dal.mysql.batchnumber;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.localserver.inventory.api.batchnumber.dto.InventoryBatchNumberDTO;
import com.xyy.saas.localserver.inventory.api.batchnumber.dto.InventoryBatchNumberQueryDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySelectBatchItemDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySelectItemDTO;
import com.xyy.saas.localserver.inventory.server.controller.admin.batchnumber.vo.InventoryBatchNumberPageReqVO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.batchnumber.InventoryBatchNumberDO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.inventory.enums.ErrorCodeConstants.INVENTORY_SELECT_BATCH_NOT_MATCH;

/**
 * 商品批次库存 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InventoryBatchNumberMapper extends BaseMapperX<InventoryBatchNumberDO> {

    default PageResult<InventoryBatchNumberDO> selectPage(InventoryBatchNumberPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InventoryBatchNumberDO>()
                .eqIfPresent(InventoryBatchNumberDO::getGuid, reqVO.getGuid())
                .eqIfPresent(InventoryBatchNumberDO::getProductPref, reqVO.getProductPref())
                .eqIfPresent(InventoryBatchNumberDO::getLotNo, reqVO.getLotNo())
                .eqIfPresent(InventoryBatchNumberDO::getPositionGuid, reqVO.getPositionGuid())
                .eqIfPresent(InventoryBatchNumberDO::getStockNumber, reqVO.getStockNumber())
                .eqIfPresent(InventoryBatchNumberDO::getCampOnNumber, reqVO.getCampOnNumber())
                .eqIfPresent(InventoryBatchNumberDO::getBillType, reqVO.getBillType())
                .eqIfPresent(InventoryBatchNumberDO::getBillNo, reqVO.getBillNo())
                .eqIfPresent(InventoryBatchNumberDO::getSupplierGuid, reqVO.getSupplierGuid())
                .eqIfPresent(InventoryBatchNumberDO::getInTaxPrice, reqVO.getInTaxPrice())
                .eqIfPresent(InventoryBatchNumberDO::getTaxRate, reqVO.getTaxRate())
                .eqIfPresent(InventoryBatchNumberDO::getSterilizationBatchNo, reqVO.getSterilizationBatchNo())
                .eqIfPresent(InventoryBatchNumberDO::getTracePref, reqVO.getTracePref())
                .eqIfPresent(InventoryBatchNumberDO::getSourceTracePref, reqVO.getSourceTracePref())
                .eqIfPresent(InventoryBatchNumberDO::getRootTracePref, reqVO.getRootTracePref())
                .betweenIfPresent(InventoryBatchNumberDO::getInTime, reqVO.getInTime())
                .eqIfPresent(InventoryBatchNumberDO::getRemark, reqVO.getRemark())
                .eqIfPresent(InventoryBatchNumberDO::getVersion, reqVO.getVersion())
                .betweenIfPresent(InventoryBatchNumberDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InventoryBatchNumberDO::getId));
    }

    List<InventoryBatchNumberDO> selectInventoryBatchNumberAsc(@Param("item") InventorySelectItemDTO item);

    List<InventoryBatchNumberDO> selectInventoryBatchNumberDesc(@Param("item") InventorySelectItemDTO item);

    default List<InventoryBatchNumberDO> selectInventoryBatchNumberDirect(List<InventorySelectItemDTO> items) {
        List<String> tracePrefList = items.stream()
                .filter(item -> StringUtils.isNotBlank(item.getTracePref()))
                .map(InventorySelectItemDTO::getTracePref)
                .collect(Collectors.toList());
        if(items.size() != tracePrefList.size()) {
            throw exception(INVENTORY_SELECT_BATCH_NOT_MATCH);
        }
        return selectList(new LambdaQueryWrapperX<InventoryBatchNumberDO>()
                .inIfPresent(InventoryBatchNumberDO::getTracePref, tracePrefList));
    }

    int reduceStockNumber(@Param("items") List<InventorySelectBatchItemDTO> items);

    int increaseStockNumber(@Param("items") List<InventorySelectBatchItemDTO> items);

    int increaseCampOnNumber(@Param("items") List<InventorySelectBatchItemDTO> items);

    int releaseCampOnNumber(@Param("items") List<InventorySelectBatchItemDTO> items);

    int releaseStockNumber(@Param("items") List<InventorySelectBatchItemDTO> items);

    default List<InventoryBatchNumberDO> selectList(InventoryBatchNumberQueryDTO queryDTO) {
        return selectList(new LambdaQueryWrapperX<InventoryBatchNumberDO>()
                .eqIfPresent(InventoryBatchNumberDO::getProductPref, queryDTO.getProductPref())
                .eqIfPresent(InventoryBatchNumberDO::getLotNo, queryDTO.getLotNo())
                .eqIfPresent(InventoryBatchNumberDO::getPositionGuid, queryDTO.getPositionGuid())
                .eqIfPresent(InventoryBatchNumberDO::getGuid, queryDTO.getBatchGuid())
                .eqIfPresent(InventoryBatchNumberDO::getTracePref, queryDTO.getTracePref())
                .eqIfPresent(InventoryBatchNumberDO::getBillNo, queryDTO.getBillNo())
        );
    }

}
package com.xyy.saas.localserver.inventory.server.dal.mysql.tracecode;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.tracecode.TraceCodeDO;
import org.apache.ibatis.annotations.Mapper;
import com.xyy.saas.localserver.inventory.server.controller.admin.tracecode.vo.*;

/**
 * 追溯码 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TraceCodeMapper extends BaseMapperX<TraceCodeDO> {

    default PageResult<TraceCodeDO> selectPage(TraceCodePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TraceCodeDO>()
                .eqIfPresent(TraceCodeDO::getProductPref, reqVO.getProductPref())
                .eqIfPresent(TraceCodeDO::getLotNo, reqVO.getLotNo())
//                .eqIfPresent(TraceCodeDO::getBatchGuid, reqVO.getBatchGuid())
                .eqIfPresent(TraceCodeDO::getSupplierPref, reqVO.getSupplierPref())
                .eqIfPresent(TraceCodeDO::getTraceCode, reqVO.getTraceCode())
                .eqIfPresent(TraceCodeDO::getStatus, reqVO.getStatus())
                .eqIfPresent(TraceCodeDO::getPackageLevel, reqVO.getPackageLevel())
                .eqIfPresent(TraceCodeDO::getParentCode, reqVO.getParentCode())
                .eqIfPresent(TraceCodeDO::getVersion, reqVO.getVersion())
                .betweenIfPresent(TraceCodeDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TraceCodeDO::getId));
    }

    default List<TraceCodeDO> selectList(List<String> traceCodeList) {
        return selectList(new LambdaQueryWrapperX<TraceCodeDO>()
                .inIfPresent(TraceCodeDO::getTraceCode, traceCodeList));
    }

}
package com.xyy.saas.localserver.inventory.server.dal.mysql.tracecode;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.localserver.inventory.server.controller.admin.tracecode.vo.TraceCodeStockDetailPageReqVO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.tracecode.TraceCodeStockDetailDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 追溯码帐页 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TraceCodeStockDetailMapper extends BaseMapperX<TraceCodeStockDetailDO> {

    default PageResult<TraceCodeStockDetailDO> selectPage(TraceCodeStockDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TraceCodeStockDetailDO>()
                .eqIfPresent(TraceCodeStockDetailDO::getBillType, reqVO.getBillType())
                .eqIfPresent(TraceCodeStockDetailDO::getBillNo, reqVO.getBillNo())
                .betweenIfPresent(TraceCodeStockDetailDO::getBillTime, reqVO.getBillTime())
                .eqIfPresent(TraceCodeStockDetailDO::getProductPref, reqVO.getProductPref())
                .eqIfPresent(TraceCodeStockDetailDO::getLotNo, reqVO.getLotNo())
//                .eqIfPresent(TraceCodeStockDetailDO::getBatchGuid, reqVO.getBatchGuid())
                .eqIfPresent(TraceCodeStockDetailDO::getSupplierPref, reqVO.getSupplierPref())
                .eqIfPresent(TraceCodeStockDetailDO::getTraceCode, reqVO.getTraceCode())
                .eqIfPresent(TraceCodeStockDetailDO::getInNumber, reqVO.getInNumber())
                .eqIfPresent(TraceCodeStockDetailDO::getOutNumber, reqVO.getOutNumber())
                .eqIfPresent(TraceCodeStockDetailDO::getRemainNumber, reqVO.getRemainNumber())
                .eqIfPresent(TraceCodeStockDetailDO::getStatus, reqVO.getStatus())
                .eqIfPresent(TraceCodeStockDetailDO::getPackageLevel, reqVO.getPackageLevel())
                .eqIfPresent(TraceCodeStockDetailDO::getParentCode, reqVO.getParentCode())
                .eqIfPresent(TraceCodeStockDetailDO::getVersion, reqVO.getVersion())
                .betweenIfPresent(TraceCodeStockDetailDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TraceCodeStockDetailDO::getId));
    }

}
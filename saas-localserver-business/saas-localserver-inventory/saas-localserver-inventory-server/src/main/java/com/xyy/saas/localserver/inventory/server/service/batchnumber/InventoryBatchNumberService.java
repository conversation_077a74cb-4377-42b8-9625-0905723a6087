package com.xyy.saas.localserver.inventory.server.service.batchnumber;

import java.util.*;

import com.xyy.saas.localserver.inventory.api.batchnumber.dto.InventoryBatchNumberDTO;
import com.xyy.saas.localserver.inventory.api.batchnumber.dto.InventoryBatchNumberQueryDTO;
import com.xyy.saas.localserver.inventory.server.controller.admin.batchnumber.vo.InventoryBatchNumberPageReqVO;
import com.xyy.saas.localserver.inventory.server.controller.admin.batchnumber.vo.InventoryBatchNumberSaveReqVO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.batchnumber.InventoryBatchNumberDO;
import jakarta.validation.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 商品批次库存 Service 接口
 *
 * <AUTHOR>
 */
public interface InventoryBatchNumberService {

    /**
     * 创建商品批次库存
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInventoryBatchNumber(@Valid InventoryBatchNumberSaveReqVO createReqVO);

    /**
     * 更新商品批次库存
     *
     * @param updateReqVO 更新信息
     */
    void updateInventoryBatchNumber(@Valid InventoryBatchNumberSaveReqVO updateReqVO);

    /**
     * 删除商品批次库存
     *
     * @param id 编号
     */
    void deleteInventoryBatchNumber(Long id);

    /**
     * 获得商品批次库存
     *
     * @param id 编号
     * @return 商品批次库存
     */
    InventoryBatchNumberDO getInventoryBatchNumber(Long id);

    /**
     * 获得商品批次库存分页
     *
     * @param pageReqVO 分页查询
     * @return 商品批次库存分页
     */
    PageResult<InventoryBatchNumberDO> getInventoryBatchNumberPage(InventoryBatchNumberPageReqVO pageReqVO);

    /**
     * 获得商品批次库存列表
     *
     * @param queryDTO
     * @return
     */
    List<InventoryBatchNumberDTO> getInventoryBatchNumberList(InventoryBatchNumberQueryDTO queryDTO);
}
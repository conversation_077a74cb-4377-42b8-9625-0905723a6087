package com.xyy.saas.localserver.inventory.server.service.batchnumber;

import com.xyy.saas.localserver.inventory.api.batchnumber.dto.InventoryBatchNumberDTO;
import com.xyy.saas.localserver.inventory.api.batchnumber.dto.InventoryBatchNumberQueryDTO;
import com.xyy.saas.localserver.inventory.server.controller.admin.batchnumber.vo.InventoryBatchNumberPageReqVO;
import com.xyy.saas.localserver.inventory.server.controller.admin.batchnumber.vo.InventoryBatchNumberSaveReqVO;
import com.xyy.saas.localserver.inventory.server.convert.batchnumber.BatchNumberConvert;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.batchnumber.InventoryBatchNumberDO;
import com.xyy.saas.localserver.inventory.server.dal.mysql.batchnumber.InventoryBatchNumberMapper;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;


import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.inventory.enums.ErrorCodeConstants.INVENTORY_BATCH_NUMBER_NOT_EXISTS;

/**
 * 商品批次库存 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InventoryBatchNumberServiceImpl implements InventoryBatchNumberService {

    @Resource
    private InventoryBatchNumberMapper inventoryBatchNumberMapper;

    @Override
    public Long createInventoryBatchNumber(InventoryBatchNumberSaveReqVO createReqVO) {
        // 插入
        InventoryBatchNumberDO inventoryBatchNumber = BeanUtils.toBean(createReqVO, InventoryBatchNumberDO.class);
        inventoryBatchNumberMapper.insert(inventoryBatchNumber);
        // 返回
        return inventoryBatchNumber.getId();
    }

    @Override
    public void updateInventoryBatchNumber(InventoryBatchNumberSaveReqVO updateReqVO) {
        // 校验存在
        validateInventoryBatchNumberExists(updateReqVO.getId());
        // 更新
        InventoryBatchNumberDO updateObj = BeanUtils.toBean(updateReqVO, InventoryBatchNumberDO.class);
        inventoryBatchNumberMapper.updateById(updateObj);
    }

    @Override
    public void deleteInventoryBatchNumber(Long id) {
        // 校验存在
        validateInventoryBatchNumberExists(id);
        // 删除
        inventoryBatchNumberMapper.deleteById(id);
    }

    private void validateInventoryBatchNumberExists(Long id) {
        if (inventoryBatchNumberMapper.selectById(id) == null) {
            throw exception(INVENTORY_BATCH_NUMBER_NOT_EXISTS);
        }
    }

    @Override
    public InventoryBatchNumberDO getInventoryBatchNumber(Long id) {
        return inventoryBatchNumberMapper.selectById(id);
    }

    @Override
    public PageResult<InventoryBatchNumberDO> getInventoryBatchNumberPage(InventoryBatchNumberPageReqVO pageReqVO) {
        return inventoryBatchNumberMapper.selectPage(pageReqVO);
    }

    @Override
    public List<InventoryBatchNumberDTO> getInventoryBatchNumberList(InventoryBatchNumberQueryDTO queryDTO) {
        List<InventoryBatchNumberDO> list = inventoryBatchNumberMapper.selectList(queryDTO);
        return BatchNumberConvert.INSTANCE.convert(list);
    }

}
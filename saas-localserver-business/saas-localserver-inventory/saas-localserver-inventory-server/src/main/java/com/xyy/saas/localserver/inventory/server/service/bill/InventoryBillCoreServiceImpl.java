package com.xyy.saas.localserver.inventory.server.service.bill;

import com.xyy.saas.localserver.entity.enums.bill.BillTypeEnum;
import com.xyy.saas.localserver.inventory.api.bill.dto.InventoryBillDTO;
import com.xyy.saas.localserver.inventory.api.bill.dto.InventoryBillDetailDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventoryMoveDTO;
import com.xyy.saas.localserver.inventory.enums.BillStatusEnum;
import com.xyy.saas.localserver.inventory.server.controller.admin.bill.vo.InventoryBillSaveReqVO;
import com.xyy.saas.localserver.inventory.server.convert.bill.BillConvert;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.bill.InventoryBillDO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.bill.InventoryBillDetailDO;
import com.xyy.saas.localserver.inventory.server.service.stock.InventoryStockService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import reactor.util.function.Tuple3;
import reactor.util.function.Tuples;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.inventory.enums.ErrorCodeConstants.*;

/**
 * 库存单据核心 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InventoryBillCoreServiceImpl implements InventoryBillCoreService {

    @Resource
    private InventoryBillService inventoryBillService;
    @Resource
    private InventoryStockService inventoryStockService;

    private Map<BillTypeEnum, Function<InventoryBillSaveReqVO, InventoryBillDTO>> saveBillMap = new HashMap<>();

    @PostConstruct
    public void dispatcherInit(){
        saveBillMap.put(BillTypeEnum.POSITION_MOVE, save -> saveInventoryBill(save, this::validatePositionMoveBill, this::executePositionMoveBill));
        saveBillMap.put(BillTypeEnum.INVENTORY_PLAN, save -> saveInventoryBill(save, this::validateInventoryPlanBill, this::createWorkflow));
        saveBillMap.put(BillTypeEnum.PROFIT_LOSS, save -> saveInventoryBill(save, this::validateProfitLossBill, this::createWorkflow));
        saveBillMap.put(BillTypeEnum.INVENTORY_UNBUNDLED, save -> saveInventoryBill(save, this::validateInventoryUnBundledBill, this::executeInventoryUnBundledBill));
        saveBillMap.put(BillTypeEnum.QUALITY_REVIEW, save -> saveInventoryBill(save, this::validateQualityReviewBill, this::createWorkflow));
    }

    @Override
    public InventoryBillDTO saveInventoryBill(InventoryBillSaveReqVO saveReqVO) {
        return saveBillMap.get(BillTypeEnum.getByBillType(saveReqVO.getBillType())).apply(saveReqVO);
    }

    public InventoryBillDTO saveInventoryBill(InventoryBillSaveReqVO saveReqVO, Function<InventoryBillSaveReqVO, Boolean> validate, Function<InventoryBillDTO, InventoryBillDTO> execute) {
        // 前置检查
        validate.apply(saveReqVO);

        // 对象转换
        InventoryBillDTO inventoryBill = BillConvert.INSTANCE.convert2DTO(saveReqVO);
        Tuple3<List<InventoryBillDetailDTO>, List<InventoryBillDetailDTO>, List<InventoryBillDetailDTO>> tuple = convert(inventoryBill);

        // 保存单据
        inventoryBillService.saveInventoryBill(inventoryBill, tuple);

        // 执行
        return execute.apply(inventoryBill);
    }

    @Override
    public InventoryBillDTO executePositionMoveBill(InventoryBillDTO execute) {
        // 数据组装
        InventoryMoveDTO inventoryMove = BillConvert.INSTANCE.convert(execute);

        // 货位移动
        inventoryStockService.moveStock(inventoryMove);

        // 更新单据
        inventoryBillService.updateInventoryBill(InventoryBillDO.builder().id(execute.getId()).status(BillStatusEnum.COMPLETED.getStatus()).build());
        return execute;
    }

    @Override
    public InventoryBillDTO executeInventoryPlanBill(InventoryBillDTO execute) {
        // 数据组装

        // 入库/出库

        // 生成报损报溢单

        // 更新单据
        inventoryBillService.updateInventoryBill(InventoryBillDO.builder().id(execute.getId()).status(BillStatusEnum.COMPLETED.getStatus()).build());
        return execute;
    }

    @Override
    public InventoryBillDTO executeProfitLossBill(InventoryBillDTO execute) {
        return null;
    }

    @Override
    public InventoryBillDTO executeQualityReviewBill(InventoryBillDTO execute) {
        return null;
    }

    @Override
    public InventoryBillDTO executeInventoryUnBundledBill(InventoryBillDTO execute) {
        return null;
    }

    private Boolean validatePositionMoveBill(InventoryBillSaveReqVO saveReqVO) {
        saveReqVO.getList().forEach(item -> {
            Optional.ofNullable(item.getChangeQuantity())
                    .orElseThrow(() -> exception(INVENTORY_CHANGE_NUMBER_NOT_EXISTS));
        });
        return true;
    }

    private Boolean validateInventoryPlanBill(InventoryBillSaveReqVO saveReqVO) {
        saveReqVO.getList().forEach(item -> {
            Optional.ofNullable(item.getChangeQuantity())
                    .orElseThrow(() -> exception(INVENTORY_BEFORE_QUANTITY_NOT_EXISTS));
            Optional.ofNullable(item.getChangeQuantity())
                    .orElseThrow(() -> exception(INVENTORY_ACTUAL_QUANTITY_NOT_EXISTS));
        });
        return true;
    }

    private Boolean validateQualityReviewBill(InventoryBillSaveReqVO saveReqVO) {
        saveReqVO.getList().forEach(item -> {
            Optional.ofNullable(item.getRegisterQuantity())
                    .orElseThrow(() -> exception(INVENTORY_REGISTER_QUANTITY_NOT_EXISTS));
            Optional.ofNullable(item.getChangeQuantity())
                    .orElseThrow(() -> exception(INVENTORY_CHANGE_NUMBER_NOT_EXISTS));
        });
        return true;
    }

    private Boolean validateProfitLossBill(InventoryBillSaveReqVO saveReqVO) {
        saveReqVO.getList().forEach(item -> {
            Optional.ofNullable(item.getChangeQuantity())
                    .orElseThrow(() -> exception(INVENTORY_CHANGE_NUMBER_NOT_EXISTS));
        });
        return true;
    }

    private Boolean validateInventoryUnBundledBill(InventoryBillSaveReqVO saveReqVO) {
        saveReqVO.getList().forEach(item -> {
            Optional.ofNullable(item.getChangeQuantity())
                    .orElseThrow(() -> exception(INVENTORY_CHANGE_NUMBER_NOT_EXISTS));
        });
        return true;
    }

    @Transactional
    public InventoryBillDTO createWorkflow(InventoryBillDTO inventoryBillDTO) {
        InventoryBillDO billDO = InventoryBillDO.builder().id(inventoryBillDTO.getId()).status(BillStatusEnum.PENDING_APPROVAL.getStatus()).build();
        inventoryBillService.updateInventoryBill(billDO);

        // TODO 创建审批流
        return inventoryBillDTO;
    }

    private Tuple3<List<InventoryBillDetailDTO>, List<InventoryBillDetailDTO>, List<InventoryBillDetailDTO>> convert(InventoryBillDTO inventoryBill) {
        List<InventoryBillDetailDTO> list = inventoryBill.getList();
        if(inventoryBill.getId() != null) {
            // 查询数据库中已存在的明细
            List<InventoryBillDetailDO> existingDetails = inventoryBillService.getDetailByBillNo(inventoryBill.getBillNo());
            return BillConvert.INSTANCE.convert(inventoryBill, existingDetails);
        } else {
            return Tuples.of(list, null, null);
        }
    }

}
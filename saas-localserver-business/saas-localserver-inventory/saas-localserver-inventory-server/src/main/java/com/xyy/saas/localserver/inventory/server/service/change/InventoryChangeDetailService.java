package com.xyy.saas.localserver.inventory.server.service.change;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.localserver.inventory.server.controller.admin.change.vo.InventoryChangeDetailPageReqVO;
import com.xyy.saas.localserver.inventory.server.controller.admin.change.vo.InventoryChangeDetailSaveReqVO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.change.InventoryChangeDetailDO;
import jakarta.validation.Valid;

/**
 * 库存变动明细 Service 接口
 *
 * <AUTHOR>
 */
public interface InventoryChangeDetailService {

    /**
     * 创建库存变动明细
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInventoryChangeDetail(@Valid InventoryChangeDetailSaveReqVO createReqVO);

    /**
     * 更新库存变动明细
     *
     * @param updateReqVO 更新信息
     */
    void updateInventoryChangeDetail(@Valid InventoryChangeDetailSaveReqVO updateReqVO);

    /**
     * 删除库存变动明细
     *
     * @param id 编号
     */
    void deleteInventoryChangeDetail(Long id);

    /**
     * 获得库存变动明细
     *
     * @param id 编号
     * @return 库存变动明细
     */
    InventoryChangeDetailDO getInventoryChangeDetail(Long id);

    /**
     * 获得库存变动明细分页
     *
     * @param pageReqVO 分页查询
     * @return 库存变动明细分页
     */
    PageResult<InventoryChangeDetailDO> getInventoryChangeDetailPage(InventoryChangeDetailPageReqVO pageReqVO);

}
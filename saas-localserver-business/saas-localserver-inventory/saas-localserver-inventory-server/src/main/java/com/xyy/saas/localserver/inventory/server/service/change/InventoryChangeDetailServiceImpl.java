package com.xyy.saas.localserver.inventory.server.service.change;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.localserver.inventory.server.controller.admin.change.vo.InventoryChangeDetailPageReqVO;
import com.xyy.saas.localserver.inventory.server.controller.admin.change.vo.InventoryChangeDetailSaveReqVO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.change.InventoryChangeDetailDO;
import com.xyy.saas.localserver.inventory.server.dal.mysql.change.InventoryChangeDetailMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.inventory.enums.ErrorCodeConstants.INVENTORY_CHANGE_DETAIL_NOT_EXISTS;

/**
 * 库存变动明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InventoryChangeDetailServiceImpl implements InventoryChangeDetailService {

    @Resource
    private InventoryChangeDetailMapper inventoryChangeDetailMapper;

    @Override
    public Long createInventoryChangeDetail(InventoryChangeDetailSaveReqVO createReqVO) {
        // 插入
        InventoryChangeDetailDO inventoryChangeDetail = BeanUtils.toBean(createReqVO, InventoryChangeDetailDO.class);
        inventoryChangeDetailMapper.insert(inventoryChangeDetail);
        // 返回
        return inventoryChangeDetail.getId();
    }

    @Override
    public void updateInventoryChangeDetail(InventoryChangeDetailSaveReqVO updateReqVO) {
        // 校验存在
        validateInventoryChangeDetailExists(updateReqVO.getId());
        // 更新
        InventoryChangeDetailDO updateObj = BeanUtils.toBean(updateReqVO, InventoryChangeDetailDO.class);
        inventoryChangeDetailMapper.updateById(updateObj);
    }

    @Override
    public void deleteInventoryChangeDetail(Long id) {
        // 校验存在
        validateInventoryChangeDetailExists(id);
        // 删除
        inventoryChangeDetailMapper.deleteById(id);
    }

    private void validateInventoryChangeDetailExists(Long id) {
        if (inventoryChangeDetailMapper.selectById(id) == null) {
            throw exception(INVENTORY_CHANGE_DETAIL_NOT_EXISTS);
        }
    }

    @Override
    public InventoryChangeDetailDO getInventoryChangeDetail(Long id) {
        return inventoryChangeDetailMapper.selectById(id);
    }

    @Override
    public PageResult<InventoryChangeDetailDO> getInventoryChangeDetailPage(InventoryChangeDetailPageReqVO pageReqVO) {
        return inventoryChangeDetailMapper.selectPage(pageReqVO);
    }

}
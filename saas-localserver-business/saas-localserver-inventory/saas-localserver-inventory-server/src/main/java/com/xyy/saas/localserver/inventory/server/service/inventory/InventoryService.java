package com.xyy.saas.localserver.inventory.server.service.inventory;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.localserver.inventory.api.inventory.dto.InventoryDTO;
import com.xyy.saas.localserver.inventory.api.inventory.dto.InventoryQueryDTO;
import com.xyy.saas.localserver.inventory.server.controller.admin.inventory.vo.*;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.inventory.InventoryDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 商品库存 Service 接口
 *
 * <AUTHOR>
 */
public interface InventoryService {

    /**
     * 创建商品库存
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInventory(@Valid InventorySaveReqVO createReqVO);

    /**
     * 更新商品库存
     *
     * @param updateReqVO 更新信息
     */
    void updateInventory(@Valid InventorySaveReqVO updateReqVO);

    /**
     * 删除商品库存
     *
     * @param id 编号
     */
    void deleteInventory(Long id);

    /**
     * 获得商品库存
     *
     * @param id 编号
     * @return 商品库存
     */
    InventoryDO getInventory(Long id);

    /**
     * 获得商品库存分页
     *
     * @param pageReqVO 分页查询
     * @return 商品库存分页
     */
    PageResult<InventoryDO> getInventoryPage(InventoryPageReqVO pageReqVO);

    /**
     * 获得商品库存列表
     *
     * @param queryDTO
     * @return
     */
    List<InventoryDTO> getInventoryList(InventoryQueryDTO queryDTO);

}
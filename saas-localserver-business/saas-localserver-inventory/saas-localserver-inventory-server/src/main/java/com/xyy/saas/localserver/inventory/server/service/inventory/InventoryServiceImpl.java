package com.xyy.saas.localserver.inventory.server.service.inventory;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.localserver.inventory.api.inventory.dto.InventoryDTO;
import com.xyy.saas.localserver.inventory.api.inventory.dto.InventoryQueryDTO;
import com.xyy.saas.localserver.inventory.server.controller.admin.inventory.vo.*;
import com.xyy.saas.localserver.inventory.server.convert.inventory.InventoryConvert;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.inventory.InventoryDO;
import com.xyy.saas.localserver.inventory.server.dal.mysql.inventory.InventoryMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.inventory.enums.ErrorCodeConstants.INVENTORY_NOT_EXISTS;

/**
 * 商品库存 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InventoryServiceImpl implements InventoryService {

    @Resource
    private InventoryMapper inventoryMapper;

    @Override
    public Long createInventory(InventorySaveReqVO createReqVO) {
        // 插入
        InventoryDO inventory = BeanUtils.toBean(createReqVO, InventoryDO.class);
        inventoryMapper.insert(inventory);
        // 返回
        return inventory.getId();
    }

    @Override
    public void updateInventory(InventorySaveReqVO updateReqVO) {
        // 校验存在
        validateInventoryExists(updateReqVO.getId());
        // 更新
        InventoryDO updateObj = BeanUtils.toBean(updateReqVO, InventoryDO.class);
        inventoryMapper.updateById(updateObj);
    }

    @Override
    public void deleteInventory(Long id) {
        // 校验存在
        validateInventoryExists(id);
        // 删除
        inventoryMapper.deleteById(id);
    }

    private void validateInventoryExists(Long id) {
        if (inventoryMapper.selectById(id) == null) {
            throw exception(INVENTORY_NOT_EXISTS);
        }
    }

    @Override
    public InventoryDO getInventory(Long id) {
        return inventoryMapper.selectById(id);
    }

    @Override
    public PageResult<InventoryDO> getInventoryPage(InventoryPageReqVO pageReqVO) {
        return inventoryMapper.selectPage(pageReqVO);
    }

    @Override
    public List<InventoryDTO> getInventoryList(InventoryQueryDTO queryDTO) {
        List<InventoryDO> list = inventoryMapper.selectList(queryDTO);
        return InventoryConvert.INSTANCE.convert(list);
    }

}
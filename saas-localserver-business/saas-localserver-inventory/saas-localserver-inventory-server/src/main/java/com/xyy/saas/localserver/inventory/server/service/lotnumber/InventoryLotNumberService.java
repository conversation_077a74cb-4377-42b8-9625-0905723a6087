package com.xyy.saas.localserver.inventory.server.service.lotnumber;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.localserver.inventory.api.lotnumber.dto.InventoryLotNumberDTO;
import com.xyy.saas.localserver.inventory.api.lotnumber.dto.InventoryLotNumberQueryDTO;
import com.xyy.saas.localserver.inventory.server.controller.admin.lotnumber.vo.*;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.lotnumber.InventoryLotNumberDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 商品批号库存 Service 接口
 *
 * <AUTHOR>
 */
public interface InventoryLotNumberService {

    /**
     * 创建商品批号库存
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInventoryLotNumber(@Valid InventoryLotNumberSaveReqVO createReqVO);

    /**
     * 更新商品批号库存
     *
     * @param updateReqVO 更新信息
     */
    void updateInventoryLotNumber(@Valid InventoryLotNumberSaveReqVO updateReqVO);

    /**
     * 删除商品批号库存
     *
     * @param id 编号
     */
    void deleteInventoryLotNumber(Long id);

    /**
     * 获得商品批号库存
     *
     * @param id 编号
     * @return 商品批号库存
     */
    InventoryLotNumberDO getInventoryLotNumber(Long id);

    /**
     * 获得商品批号库存分页
     *
     * @param pageReqVO 分页查询
     * @return 商品批号库存分页
     */
    PageResult<InventoryLotNumberDO> getInventoryLotNumberPage(InventoryLotNumberPageReqVO pageReqVO);

    /**
     * 获得商品批号库存列表
     *
     * @param queryDTO
     * @return
     */
    List<InventoryLotNumberDTO> getInventoryLotNumberList(InventoryLotNumberQueryDTO queryDTO);

}
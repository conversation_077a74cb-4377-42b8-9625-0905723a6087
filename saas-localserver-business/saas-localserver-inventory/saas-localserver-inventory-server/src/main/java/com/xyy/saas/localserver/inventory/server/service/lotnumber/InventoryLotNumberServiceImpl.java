package com.xyy.saas.localserver.inventory.server.service.lotnumber;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.localserver.inventory.api.lotnumber.dto.InventoryLotNumberDTO;
import com.xyy.saas.localserver.inventory.api.lotnumber.dto.InventoryLotNumberQueryDTO;
import com.xyy.saas.localserver.inventory.server.controller.admin.lotnumber.vo.*;
import com.xyy.saas.localserver.inventory.server.convert.inventory.InventoryConvert;
import com.xyy.saas.localserver.inventory.server.convert.lotnumber.LotNumberConvert;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.lotnumber.InventoryLotNumberDO;
import com.xyy.saas.localserver.inventory.server.dal.mysql.lotnumber.InventoryLotNumberMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.inventory.enums.ErrorCodeConstants.INVENTORY_LOT_NUMBER_NOT_EXISTS;

/**
 * 商品批号库存 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InventoryLotNumberServiceImpl implements InventoryLotNumberService {

    @Resource
    private InventoryLotNumberMapper inventoryLotNumberMapper;

    @Override
    public Long createInventoryLotNumber(InventoryLotNumberSaveReqVO createReqVO) {
        // 插入
        InventoryLotNumberDO inventoryLotNumber = BeanUtils.toBean(createReqVO, InventoryLotNumberDO.class);
        inventoryLotNumberMapper.insert(inventoryLotNumber);
        // 返回
        return inventoryLotNumber.getId();
    }

    @Override
    public void updateInventoryLotNumber(InventoryLotNumberSaveReqVO updateReqVO) {
        // 校验存在
        validateInventoryLotNumberExists(updateReqVO.getId());
        // 更新
        InventoryLotNumberDO updateObj = BeanUtils.toBean(updateReqVO, InventoryLotNumberDO.class);
        inventoryLotNumberMapper.updateById(updateObj);
    }

    @Override
    public void deleteInventoryLotNumber(Long id) {
        // 校验存在
        validateInventoryLotNumberExists(id);
        // 删除
        inventoryLotNumberMapper.deleteById(id);
    }

    private void validateInventoryLotNumberExists(Long id) {
        if (inventoryLotNumberMapper.selectById(id) == null) {
            throw exception(INVENTORY_LOT_NUMBER_NOT_EXISTS);
        }
    }

    @Override
    public InventoryLotNumberDO getInventoryLotNumber(Long id) {
        return inventoryLotNumberMapper.selectById(id);
    }

    @Override
    public PageResult<InventoryLotNumberDO> getInventoryLotNumberPage(InventoryLotNumberPageReqVO pageReqVO) {
        return inventoryLotNumberMapper.selectPage(pageReqVO);
    }

    @Override
    public List<InventoryLotNumberDTO> getInventoryLotNumberList(InventoryLotNumberQueryDTO queryDTO) {
        List<InventoryLotNumberDO> list = inventoryLotNumberMapper.selectList(queryDTO);
        return LotNumberConvert.INSTANCE.convert(list);
    }

}
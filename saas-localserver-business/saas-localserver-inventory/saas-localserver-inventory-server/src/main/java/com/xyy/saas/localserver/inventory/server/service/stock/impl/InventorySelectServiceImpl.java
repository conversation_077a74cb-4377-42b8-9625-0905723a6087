package com.xyy.saas.localserver.inventory.server.service.stock.impl;

import com.xyy.saas.localserver.inventory.api.stock.dto.*;
import com.xyy.saas.localserver.inventory.enums.InventorySelectEnum;
import com.xyy.saas.localserver.inventory.server.convert.batchnumber.BatchNumberConvert;
import com.xyy.saas.localserver.inventory.server.convert.stock.StockConvert;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.batchnumber.InventoryBatchNumberDO;
import com.xyy.saas.localserver.inventory.server.dal.mysql.batchnumber.InventoryBatchNumberMapper;
import com.xyy.saas.localserver.inventory.server.service.stock.*;
import com.xyy.saas.localserver.inventory.server.support.InventoryBaseSupport;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.inventory.enums.ErrorCodeConstants.*;

/**
 * 库存选取服务实现类，支持多种库存选取策略（FIFO、LIFO、FIFI、LIFI、DIRECT）。
 * 提供按效期/入库时间选择批次、指定批次、新增批次等功能。
 */
@Service
public class InventorySelectServiceImpl implements InventorySelectService {

    @Resource
    private InventoryBatchNumberMapper batchNumberMapper;
    @Resource
    private InventoryBaseSupport inventoryBaseSupport;

    private Map<InventorySelectEnum, Function<InventorySelectDTO, List<InventorySelectBatchItemDTO>>> selectStrategyMap = new HashMap<>();

    /**
     * 初始化选取策略映射表，设置不同策略对应的处理方法。
     */
    @PostConstruct
    public void dispatcherInit(){
        selectStrategyMap.put(InventorySelectEnum.FIFO, select -> selectExpiryDateFIFO(select));
        selectStrategyMap.put(InventorySelectEnum.LIFO, select -> selectExpiryDateLIFO(select));
        selectStrategyMap.put(InventorySelectEnum.FIFI, select -> selectExpiryDateFIFI(select));
        selectStrategyMap.put(InventorySelectEnum.LIFI, select -> selectExpiryDateLIFI(select));
        selectStrategyMap.put(InventorySelectEnum.DIRECT, select -> selectExpiryDateDirect(select));
    }

    /**
     * 根据传入的库存选取参数，调用相应的选取策略进行处理。
     *
     * @param inventorySelect 库存选取参数
     * @return 选取后的批次列表
     */
    @Override
    public List<InventorySelectBatchItemDTO> select(InventorySelectDTO inventorySelect) {
        return Optional.ofNullable(selectStrategyMap.get(inventorySelect.getSelectStrategy()))
                .orElse(select -> addBatch(select))
                .apply(inventorySelect);
    }

    /**
     * 按照效期和入库时间升序获取批次，并执行出库逻辑。
     *
     * @param inventorySelect 库存选取参数
     * @return 选取后的批次列表
     */
    private List<InventorySelectBatchItemDTO> selectExpiryDateFIFO(InventorySelectDTO inventorySelect) {
        return processItems(inventorySelect, item -> batchNumberMapper.selectInventoryBatchNumberAsc(item), this::selectOutBatch, inventorySelect.isAllowSelectPartial());
    }

    /**
     * 按照效期和入库时间降序获取批次，并执行出库逻辑。
     *
     * @param inventorySelect 库存选取参数
     * @return 选取后的批次列表
     */
    private List<InventorySelectBatchItemDTO> selectExpiryDateLIFO(InventorySelectDTO inventorySelect) {
        return processItems(inventorySelect, item -> batchNumberMapper.selectInventoryBatchNumberDesc(item), this::selectOutBatch, inventorySelect.isAllowSelectPartial());
    }

    /**
     * 按照效期和入库时间升序获取批次，并执行入库逻辑。
     *
     * @param inventorySelect 库存选取参数
     * @return 选取后的批次列表
     */
    private List<InventorySelectBatchItemDTO> selectExpiryDateFIFI(InventorySelectDTO inventorySelect) {
        return processItems(inventorySelect, item -> batchNumberMapper.selectInventoryBatchNumberAsc(item), this::selectInBatch, inventorySelect.isAllowSelectPartial());
    }

    /**
     * 按照效期和入库时间降序获取批次，并执行入库逻辑。
     *
     * @param inventorySelect 库存选取参数
     * @return 选取后的批次列表
     */
    private List<InventorySelectBatchItemDTO> selectExpiryDateLIFI(InventorySelectDTO inventorySelect) {
        return processItems(inventorySelect, item -> batchNumberMapper.selectInventoryBatchNumberDesc(item), this::selectInBatch, inventorySelect.isAllowSelectPartial());
    }

    /**
     * 获取指定批次并执行匹配逻辑。
     *
     * @param inventorySelect 库存选取参数
     * @return 选取后的批次列表
     */
    private List<InventorySelectBatchItemDTO> selectExpiryDateDirect(InventorySelectDTO inventorySelect) {
        return processItems(inventorySelect, items -> batchNumberMapper.selectInventoryBatchNumberDirect(items), this::selectDirectBatch);
    }

    /**
     * 新增批次并插入数据库。
     *
     * @param inventorySelect 库存选取参数
     * @return 插入后的批次列表
     */
    @Transactional
    public List<InventorySelectBatchItemDTO> addBatch(InventorySelectDTO inventorySelect) {
        // 新增批次
        List<InventoryBatchNumberDO> list = getInventoryBatchNumberDOList(inventorySelect);
        batchNumberMapper.insertBatch(list);
        return StockConvert.INSTANCE.convertDTOList(list);
    }

    /**
     * 对每个库存项执行批次获取和选取逻辑。
     *
     * @param inventorySelect 库存选取参数
     * @param normalBatchFetcher 获取候选批次的方法
     * @param normalSelectStrategy 选取可用批次的方法
     * @return 选取后的批次列表
     */
    private List<InventorySelectBatchItemDTO> processItems(InventorySelectDTO inventorySelect, NormalBatchFetcher normalBatchFetcher,
                                                           NormalSelectStrategy normalSelectStrategy, boolean allowSelectPartial) {
        List<InventorySelectBatchItemDTO> result = new ArrayList<>();

        inventorySelect.getItems().forEach(item -> {
            // 选取候选的批次
            List<InventoryBatchNumberDO> batches = normalBatchFetcher.getBatches(item);
            // 选取可用的批次
            normalSelectStrategy.selectBatch(item, batches, result, allowSelectPartial);
        });
        return result;
    }

    /**
     * 对指定批次执行批次获取和选取逻辑。
     *
     * @param inventorySelect 库存选取参数
     * @param directBatchFetcher 获取候选批次的方法
     * @param directSelectStrategy 选取可用批次的方法
     * @return 选取后的批次列表
     */
    private List<InventorySelectBatchItemDTO> processItems(InventorySelectDTO inventorySelect, DirectBatchFetcher directBatchFetcher,
                                                           DirectSelectStrategy directSelectStrategy) {
        List<InventorySelectBatchItemDTO> result = new ArrayList<>();
        // 选取候选的批次
        List<InventoryBatchNumberDO> batches = directBatchFetcher.getBatches(inventorySelect.getItems());
        // 选取可用的批次
        directSelectStrategy.selectBatch(inventorySelect.getItems(), batches, result);
        return result;
    }

    /**
     * 从候选批次中选取满足出库数量的批次。
     *
     * @param item 当前库存项
     * @param batchNumberList 候选批次列表
     * @param list 结果列表
     */
    private void selectOutBatch(InventorySelectItemDTO item, List<InventoryBatchNumberDO> batchNumberList, List<InventorySelectBatchItemDTO> list, boolean allowSelectPartial) {
        // 过滤出可用的批次
        List<InventoryBatchNumberDO> availableBatchNumbers = batchNumberList.stream()
                .filter(batchNumber -> batchNumber.getStockNumber().subtract(batchNumber.getCampOnNumber()).compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());

        // 计算可用库存总数
        BigDecimal stockNumTotal = availableBatchNumbers.stream()
                .map(batchNumber -> batchNumber.getStockNumber().subtract(batchNumber.getCampOnNumber()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (!allowSelectPartial && stockNumTotal.compareTo(item.getChangeNumber()) < 0) {
            throw exception(INVENTORY_BATCH_NOT_ENOUGH);
        }

        // 选取实际的批次
        BigDecimal stockNumTemp = BigDecimal.ZERO;
        for (InventoryBatchNumberDO batchNumber : availableBatchNumbers) {
            if (stockNumTemp.compareTo(item.getChangeNumber()) == 0) {
                break;
            }
            BigDecimal availableStockNumber = batchNumber.getStockNumber().subtract(batchNumber.getCampOnNumber());
            stockNumTemp = stockNumTemp.add(availableStockNumber);

            availableStockNumber = stockNumTemp.compareTo(item.getChangeNumber()) < 0 ? availableStockNumber : availableStockNumber.subtract(stockNumTemp.subtract(item.getChangeNumber()));
            addItem(batchNumber, availableStockNumber, list, item);
        }
    }

    /**
     * 选取第一个批次作为入库批次。
     *
     * @param item 当前库存项
     * @param batchNumberList 候选批次列表
     * @param list 结果列表
     */
    private void selectInBatch(InventorySelectItemDTO item, List<InventoryBatchNumberDO> batchNumberList, List<InventorySelectBatchItemDTO> list, boolean allowSelectPartial) {
        if (batchNumberList.isEmpty()) {
            throw exception(INVENTORY_BATCH_NOT_EXISTS);
        }

        // 选取入库批次
        addItem(batchNumberList.get(0), item.getChangeNumber(), list, item);
    }

    /**
     * 验证并选取指定批次。
     *
     * @param items 库存项列表
     * @param batchNumberList 候选批次列表
     * @param list 结果列表
     */
    private void selectDirectBatch(List<InventorySelectItemDTO> items, List<InventoryBatchNumberDO> batchNumberList, List<InventorySelectBatchItemDTO> list) {
        // 选取指定批次
        Map<String, InventorySelectItemDTO> map = items.stream().collect(Collectors.toMap(InventorySelectItemDTO::getTracePref, p -> p, (k1, k2) -> k1));
        if(map.size() != batchNumberList.size()) {
            throw exception(INVENTORY_SELECT_BATCH_NOT_MATCH);
        }

        for (InventoryBatchNumberDO batchNumber : batchNumberList) {
            InventorySelectItemDTO item = map.get(batchNumber.getGuid());
            addItem(batchNumber, item.getChangeNumber(), list, item);
        }
    }

    /**
     * 将批次信息封装为 DTO 并添加到结果列表。
     *
     * @param batchNumber 批次对象
     * @param needSubStockNumber 需要变动的数量
     * @param list 结果列表
     * @param item 当前库存项
     */
    private void addItem(InventoryBatchNumberDO batchNumber, BigDecimal needSubStockNumber, List<InventorySelectBatchItemDTO> list, InventorySelectItemDTO item) {
        InventorySelectBatchItemDTO dto = BatchNumberConvert.INSTANCE.convert(batchNumber);
        dto.setChangeNumber(needSubStockNumber);
        dto.setCostPrice(item.getCostPrice());
        dto.setTraceCodes(item.getTraceCodes());
        list.add(dto);
    }

    /**
     * 根据库存选取参数构建新增的批次 DO 列表。
     *
     * @param inventorySelect 库存选取参数
     * @return 新增的批次 DO 列表
     */
    private List<InventoryBatchNumberDO> getInventoryBatchNumberDOList(InventorySelectDTO inventorySelect) {
        List<InventoryBatchNumberDO> list = inventorySelect.getItems().stream().map(item -> {
            InventoryBatchNumberDO batchItem = StockConvert.INSTANCE.convertDO(item);
            batchItem.setGuid(inventoryBaseSupport.getGuid());
            batchItem.setBillType(inventorySelect.getBillType());
            batchItem.setBillNo(inventorySelect.getBillNo());
            batchItem.setInTime(inventorySelect.getStockTime());
            return batchItem;
        }).collect(Collectors.toList());
        return list;
    }

}

package com.xyy.saas.localserver.inventory.server.service.tracecode;

import java.util.*;

import com.xyy.saas.localserver.inventory.api.change.dto.InventoryChangeDetailDTO;
import com.xyy.saas.localserver.inventory.enums.TraceCodeStatusEnum;
import jakarta.validation.*;
import com.xyy.saas.localserver.inventory.server.controller.admin.tracecode.vo.*;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.tracecode.TraceCodeDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 追溯码 Service 接口
 *
 * <AUTHOR>
 */
public interface InventoryTraceCodeService {

    /**
     * 更新追溯码状态
     *
     * @param changeTraceCodeDetailDTOList
     * @param statusEnum
     */
    void updateTraceCodeStatus(List<InventoryChangeDetailDTO> changeTraceCodeDetailDTOList, TraceCodeStatusEnum statusEnum);

    /**
     * 创建追溯码
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInventoryTraceCode(@Valid TraceCodeSaveReqVO createReqVO);

    /**
     * 更新追溯码
     *
     * @param updateReqVO 更新信息
     */
    void updateInventoryTraceCode(@Valid TraceCodeSaveReqVO updateReqVO);

    /**
     * 删除追溯码
     *
     * @param id 编号
     */
    void deleteInventoryTraceCode(Long id);

    /**
     * 获得追溯码
     *
     * @param id 编号
     * @return 追溯码
     */
    TraceCodeDO getInventoryTraceCode(Long id);

    /**
     * 获得追溯码分页
     *
     * @param pageReqVO 分页查询
     * @return 追溯码分页
     */
    PageResult<TraceCodeDO> getInventoryTraceCodePage(TraceCodePageReqVO pageReqVO);

}
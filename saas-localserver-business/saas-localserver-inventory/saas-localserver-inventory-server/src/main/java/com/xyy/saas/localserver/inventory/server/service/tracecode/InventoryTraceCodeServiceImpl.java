package com.xyy.saas.localserver.inventory.server.service.tracecode;

import com.xyy.saas.localserver.inventory.api.change.dto.InventoryChangeDetailDTO;
import com.xyy.saas.localserver.inventory.api.tracecode.dto.TraceCodeDTO;
import com.xyy.saas.localserver.inventory.api.tracecode.dto.TraceCodeStockDetailDTO;
import com.xyy.saas.localserver.inventory.enums.TraceCodeStatusEnum;
import com.xyy.saas.localserver.inventory.server.convert.tracecode.TraceCodeConvert;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.tracecode.TraceCodeStockDetailDO;
import com.xyy.saas.localserver.inventory.server.dal.mysql.tracecode.TraceCodeStockDetailMapper;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.xyy.saas.localserver.inventory.server.controller.admin.tracecode.vo.*;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.tracecode.TraceCodeDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import com.xyy.saas.localserver.inventory.server.dal.mysql.tracecode.TraceCodeMapper;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.inventory.enums.ErrorCodeConstants.INVENTORY_TRACE_CODE_NOT_EXISTS;

/**
 * 追溯码 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InventoryTraceCodeServiceImpl implements InventoryTraceCodeService {

    @Resource
    private TraceCodeMapper traceCodeMapper;
    @Resource
    private TraceCodeStockDetailMapper traceCodeStockDetailMapper;

    private Map<TraceCodeStatusEnum, Function<List<InventoryChangeDetailDTO>, Boolean>> traceCodeStatusMap = new HashMap<>();

    @PostConstruct
    public void dispatcherInit(){
        traceCodeStatusMap.put(TraceCodeStatusEnum.RECEIVED, list -> updateTraceCodeStatus(list, TraceCodeStatusEnum.RECEIVED, this::updateReceivedStatus, this::newReceivedStatus));
        traceCodeStatusMap.put(TraceCodeStatusEnum.WAREHOUSING, list -> updateTraceCodeStatus(list, TraceCodeStatusEnum.WAREHOUSING, this::updateWarehousingStatus, this::newWarehousingStatus));
        traceCodeStatusMap.put(TraceCodeStatusEnum.CAMP_ON, list -> updateTraceCodeStatus(list, TraceCodeStatusEnum.CAMP_ON, this::updateCampOnStatus, this::throwException));
        traceCodeStatusMap.put(TraceCodeStatusEnum.SALE, list -> updateTraceCodeStatus(list, TraceCodeStatusEnum.SALE, this::updateSaleStatus, this::throwException));
        traceCodeStatusMap.put(TraceCodeStatusEnum.DESTROYED, list -> updateTraceCodeStatus(list, TraceCodeStatusEnum.DESTROYED, this::updateDestroyedStatus, this::throwException));
        traceCodeStatusMap.put(TraceCodeStatusEnum.PURCHASE_RETURN, list -> updateTraceCodeStatus(list, TraceCodeStatusEnum.PURCHASE_RETURN, this::updatePurchaseReturnStatus, this::throwException));
        traceCodeStatusMap.put(TraceCodeStatusEnum.SALE_RETURN, list -> updateTraceCodeStatus(list, TraceCodeStatusEnum.SALE_RETURN, this::updateSaleReturnStatus, this::throwException));
    }

    @Override
    public void updateTraceCodeStatus(List<InventoryChangeDetailDTO> list, TraceCodeStatusEnum statusEnum) {
        traceCodeStatusMap.get(statusEnum).apply(list);
    }

    public Boolean updateTraceCodeStatus(List<InventoryChangeDetailDTO> list, TraceCodeStatusEnum statusEnum,
                                         BiFunction<TraceCodeDO, TraceCodeDTO, Tuple2> updateFunction,
                                         Function<TraceCodeDTO, Tuple2> newFunction) {
        // 提取所有 TraceCodeChangeDTO 并按 traceCde 去重存入 Map
        Map<String, TraceCodeDTO> traceCodeMap = list.stream()
                .flatMap(item -> item.getTraceCodes().stream().map(dto -> TraceCodeConvert.INSTANCE.convert(item, dto)))
                .collect(Collectors.toMap(TraceCodeDTO::getTraceCode, Function.identity(), (v1, v2) -> v2));

        // 获取 traceCode 列表
        List<String> traceCodeList = new ArrayList<>(traceCodeMap.keySet());
        if (traceCodeList.isEmpty()) {
            return true;
        }

        // 查询数据库中已存在的追溯码信息
        List<TraceCodeDO> inventoryTraceCodeList = traceCodeMapper.selectList(traceCodeList);
        Set<String> existingTraceCodes = inventoryTraceCodeList.stream().map(TraceCodeDO::getTraceCode).collect(Collectors.toSet());

        // 筛选出需要更新的记录
        List<Tuple2<TraceCodeDTO, TraceCodeStockDetailDTO>> toUpdateList = inventoryTraceCodeList.stream()
                .filter(inventoryTraceCode -> traceCodeMap.containsKey(inventoryTraceCode.getTraceCode()))
                .map(inventoryTraceCode -> buildUpdateTraceCode(inventoryTraceCode, traceCodeMap.get(inventoryTraceCode.getTraceCode()), updateFunction))
                .collect(Collectors.toList());

        // 筛选出需要新增的记录
        List<Tuple2<TraceCodeDTO, TraceCodeStockDetailDTO>> toCreateList = traceCodeList.stream()
                .filter(traceCode -> !existingTraceCodes.contains(traceCode))
                .map(traceCode -> buildNewTraceCode(traceCodeMap.get(traceCode), newFunction))
                .collect(Collectors.toList());

        // 执行批量更新和新增操作
        updateTraceCode(toUpdateList, toCreateList);
        return true;
    }

    @Override
    public Long createInventoryTraceCode(TraceCodeSaveReqVO createReqVO) {
        // 插入
        TraceCodeDO inventoryTraceCode = BeanUtils.toBean(createReqVO, TraceCodeDO.class);
        traceCodeMapper.insert(inventoryTraceCode);
        // 返回
        return inventoryTraceCode.getId();
    }

    @Override
    public void updateInventoryTraceCode(TraceCodeSaveReqVO updateReqVO) {
        // 校验存在
        validateInventoryTraceCodeExists(updateReqVO.getId());
        // 更新
        TraceCodeDO updateObj = BeanUtils.toBean(updateReqVO, TraceCodeDO.class);
        traceCodeMapper.updateById(updateObj);
    }

    @Override
    public void deleteInventoryTraceCode(Long id) {
        // 校验存在
        validateInventoryTraceCodeExists(id);
        // 删除
        traceCodeMapper.deleteById(id);
    }

    private void validateInventoryTraceCodeExists(Long id) {
        if (traceCodeMapper.selectById(id) == null) {
            throw exception(INVENTORY_TRACE_CODE_NOT_EXISTS);
        }
    }

    @Override
    public TraceCodeDO getInventoryTraceCode(Long id) {
        return traceCodeMapper.selectById(id);
    }

    @Override
    public PageResult<TraceCodeDO> getInventoryTraceCodePage(TraceCodePageReqVO pageReqVO) {
        return traceCodeMapper.selectPage(pageReqVO);
    }

    @Transactional
    public void updateTraceCode(List<Tuple2<TraceCodeDTO, TraceCodeStockDetailDTO>> toUpdateList, List<Tuple2<TraceCodeDTO, TraceCodeStockDetailDTO>> toCreateList) {
        if (toUpdateList.isEmpty() && toCreateList.isEmpty()) {
            return;
        }

        if (!toUpdateList.isEmpty()) {
            traceCodeMapper.updateBatch(TraceCodeConvert.INSTANCE.convert(toUpdateList.stream().map(Tuple2::getT1).collect(Collectors.toList())));
        }

        if (!toCreateList.isEmpty()) {
            traceCodeMapper.insertBatch(TraceCodeConvert.INSTANCE.convert(toCreateList.stream().map(Tuple2::getT1).collect(Collectors.toList())));
        }

        List<TraceCodeStockDetailDO> stockDetails = Stream.concat(toUpdateList.stream(), toCreateList.stream())
                .map(tuple -> TraceCodeConvert.INSTANCE.convert2Detail(tuple.getT2()))
                .collect(Collectors.toList());
        traceCodeStockDetailMapper.insertBatch(stockDetails);
    }

    /**
     * 构建更新追溯码对象
     *
     * @param codeDO
     * @param dto
     * @param function
     * @return
     */
    private Tuple2<TraceCodeDTO, TraceCodeStockDetailDTO> buildUpdateTraceCode(TraceCodeDO codeDO, TraceCodeDTO dto, BiFunction<TraceCodeDO, TraceCodeDTO, Tuple2> function) {
        dto.setId(codeDO.getId());
        return function.apply(codeDO, dto);
    }

    /**
     * 构建新增追溯码对象
     *
     * @param dto
     * @param function
     * @return
     */
    private Tuple2<TraceCodeDTO, TraceCodeStockDetailDTO> buildNewTraceCode(TraceCodeDTO dto, Function<TraceCodeDTO, Tuple2> function) {
//        dto.setBatchGuid(dto.getBatchGuid());
        return function.apply(dto);
    }

    public Tuple2 updateReceivedStatus(TraceCodeDO codeDO, TraceCodeDTO item) {
        return newReceivedStatus(item);
    }

    public Tuple2 newReceivedStatus(TraceCodeDTO item) {
        item.setStatus(TraceCodeStatusEnum.RECEIVED.getStatus());
//        item.setBatchGuid(null);
        item.setInNumber(BigDecimal.ZERO);
        item.setOutNumber(BigDecimal.ZERO);
        item.setRemainNumber(item.getInNumber().subtract(item.getOutNumber()));
        return Tuples.of(item, TraceCodeConvert.INSTANCE.convert(item));
    }

    public Tuple2 updateWarehousingStatus(TraceCodeDO codeDO, TraceCodeDTO item) {
        item.setStatus(TraceCodeStatusEnum.WAREHOUSING.getStatus());
//        if(codeDO.getBatchGuid().equals(item.getBatchGuid())) {
//            item.setInNumber(codeDO.getInNumber().add(item.getChangeNumber()));
//        } else {
//            item.setInNumber(item.getChangeNumber());
//            item.setOutNumber(BigDecimal.ZERO);
//        }
        item.setRemainNumber(item.getInNumber().subtract(item.getOutNumber()));
        return Tuples.of(item, TraceCodeConvert.INSTANCE.convert(item));
    }

    public Tuple2 newWarehousingStatus(TraceCodeDTO item) {
        item.setStatus(TraceCodeStatusEnum.WAREHOUSING.getStatus());
        item.setInNumber(item.getChangeNumber());
        item.setOutNumber(BigDecimal.ZERO);
        item.setRemainNumber(item.getInNumber().subtract(item.getOutNumber()));
        return Tuples.of(item, TraceCodeConvert.INSTANCE.convert(item));
    }

    public Tuple2 updateCampOnStatus(TraceCodeDO codeDO, TraceCodeDTO item) {
        item.setStatus(TraceCodeStatusEnum.CAMP_ON.getStatus());
        return Tuples.of(item, TraceCodeConvert.INSTANCE.convert(item));
    }

    public Tuple2 updateSaleStatus(TraceCodeDO codeDO, TraceCodeDTO item) {
        item.setStatus(TraceCodeStatusEnum.SALE.getStatus());
        item.setOutNumber(item.getOutNumber().add(item.getChangeNumber()));
        item.setRemainNumber(item.getInNumber().subtract(item.getOutNumber()));
        return Tuples.of(item, TraceCodeConvert.INSTANCE.convert(item));
    }

    public Tuple2 updateDestroyedStatus(TraceCodeDO codeDO, TraceCodeDTO item) {
        item.setStatus(TraceCodeStatusEnum.DESTROYED.getStatus());
        return Tuples.of(item, TraceCodeConvert.INSTANCE.convert(item));
    }

    public Tuple2 updatePurchaseReturnStatus(TraceCodeDO codeDO, TraceCodeDTO item) {
        item.setStatus(TraceCodeStatusEnum.PURCHASE_RETURN.getStatus());
        item.setOutNumber(item.getOutNumber().add(item.getChangeNumber()));
        item.setRemainNumber(item.getInNumber().subtract(item.getOutNumber()));
        return Tuples.of(item, TraceCodeConvert.INSTANCE.convert(item));
    }

    public Tuple2 updateSaleReturnStatus(TraceCodeDO codeDO, TraceCodeDTO item) {
        item.setStatus(TraceCodeStatusEnum.SALE_RETURN.getStatus());
        item.setOutNumber(item.getOutNumber().subtract(item.getChangeNumber()));
        item.setRemainNumber(item.getInNumber().subtract(item.getOutNumber()));
        return Tuples.of(item, TraceCodeConvert.INSTANCE.convert(item));
    }

    public Tuple2 throwException(TraceCodeDTO item) {
        throw exception(INVENTORY_TRACE_CODE_NOT_EXISTS);
    }

}
package com.xyy.saas.localserver.inventory.server.service.stock;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySelectItemDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventoryReduceDTO;
import com.xyy.saas.localserver.inventory.enums.InventorySelectEnum;
import com.xyy.saas.localserver.inventory.server.InventoryServerApplication;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest(classes = InventoryServerApplication.class)
@ActiveProfiles("test")
public class InventoryStockServiceImplTest {

    @Resource
    private InventoryStockService inventoryStockService;
    @Test
    public void testReduceStock() {
        InventorySelectItemDTO productLotItemDTO = InventorySelectItemDTO
                .builder()
                .productPref("ZHL21051000")
                .lotNo("21051001")
                .positionGuid("450e8400e29b41d4a716446655440000")
                .changeNumber(new BigDecimal("3"))
                .build();
        List<InventorySelectItemDTO> items = new ArrayList<>();
        items.add(productLotItemDTO);
        InventoryReduceDTO dto = InventoryReduceDTO
                .builder()
                .billType(1)
                .billNo("PD20250423001")
//                .selectStrategy(InventorySelectEnum.FIFO)
//                .items(items)
                .build();

        inventoryStockService.reduceStock(dto);
    }

    @BeforeEach
    public void before() {
        RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
        requestAttributes.setAttribute("login_user_id", 0L, RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.setRequestAttributes(requestAttributes);
        TenantContextHolder.setTenantId(0L);
    }

}

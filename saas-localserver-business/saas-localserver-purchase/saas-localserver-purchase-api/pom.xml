<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xyy.saas</groupId>
        <artifactId>saas-localserver-purchase</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>saas-localserver-purchase-api</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.xyy.saas</groupId>
            <artifactId>saas-localserver-inventory-api</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>
</project>
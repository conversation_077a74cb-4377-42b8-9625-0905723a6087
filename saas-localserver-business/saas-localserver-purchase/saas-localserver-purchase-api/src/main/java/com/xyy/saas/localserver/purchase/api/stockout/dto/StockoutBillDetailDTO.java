package com.xyy.saas.localserver.purchase.api.stockout.dto;

import com.xyy.saas.localserver.entity.pojo.BaseDto;
import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;

/**
 * 采购-缺货单明细 DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "采购-缺货单明细 DTO")
@Data
@Accessors(chain = true)
public class StockoutBillDetailDTO extends BaseDto {

    /** 主键ID */
    @Schema(description = "主键ID")
    private Long id;

    /** 租户ID */
    @Schema(description = "租户ID")
    private Long tenantId;

    /** 单号 */
    @Schema(description = "单号")
    private String billNo;

    /** 商品编码 */
    @Schema(description = "商品编码")
    private String productPref;

    /** 要货数量 */
    @Schema(description = "要货数量")
    private BigDecimal requireQuantity;

    /** 缺货数量 */
    @Schema(description = "缺货数量")
    private BigDecimal stockoutQuantity;

    /** 医保项目编码 */
    @Schema(description = "医保项目编码")
    private String medicareProjectCode;

    /** 医保项目名称 */
    @Schema(description = "医保项目名称")
    private String medicareProjectName;

    /** 医保项目等级 */
    @Schema(description = "医保项目等级")
    private Integer medicareProjectLevel;

    /** 医保最小包装数量 */
    @Schema(description = "医保最小包装数量")
    private Integer medicareMinPackageNum;

    /** 扩展信息（当前商品信息） */
    @Schema(description = "扩展信息")
    private ExtDTO ext;

    /** 备注 */
    @Schema(description = "备注")
    private String remark;
}
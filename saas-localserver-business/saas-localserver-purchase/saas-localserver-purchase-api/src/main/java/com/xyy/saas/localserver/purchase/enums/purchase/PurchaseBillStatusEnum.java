package com.xyy.saas.localserver.purchase.enums.purchase;

import lombok.AllArgsConstructor;
import lombok.Getter;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.PURCHASE_BILL_STATUS_NOT_EXISTS;

@Getter
@AllArgsConstructor
public enum PurchaseBillStatusEnum {

    PLAN_CREATED(1, "已创建计划"),
    PENDING_APPROVAL(2, "已下单待审批"),
    APPROVED_PENDING_DELIVERY(3, "已审批待发货"),
    IN_DELIVERY(4, "发货中（部分发货）"),
    DELIVERED(5, "已发货（全部发货）"),
    COMPLETED(6, "已完成（已收货）"),
    FAILED(7, "采购失败"),
    REVOKED(8, "已撤销（审批前撤销）"),
    REJECTED(9, "已驳回（审批不通过）");

    private final Integer code;
    private final String description;

    /**
     * 根据编码获取枚举实例
     */
    public static PurchaseBillStatusEnum fromCode(Integer code) {
        for (PurchaseBillStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw exception(PURCHASE_BILL_STATUS_NOT_EXISTS);
    }
}
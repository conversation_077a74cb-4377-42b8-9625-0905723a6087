package com.xyy.saas.localserver.purchase.server.admin.returned;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillDTO;
import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillPageReqDTO;
import com.xyy.saas.localserver.purchase.server.admin.returned.vo.ReturnBillPageReqVO;
import com.xyy.saas.localserver.purchase.server.admin.returned.vo.ReturnBillRespVO;
import com.xyy.saas.localserver.purchase.server.admin.returned.vo.ReturnBillSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.returned.ReturnBillConvert;
import com.xyy.saas.localserver.purchase.server.service.returned.PurchaseReturnService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.io.IOException;
import java.util.List;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 采购退货单")
@RestController
@RequestMapping("/saas/purchase/purchase-return")
@Validated
public class PurchaseReturnController {

    @Resource
    private PurchaseReturnService purchaseReturnService;

    @PostMapping("/save")
    @Operation(summary = "保存采购退货单")
    @PreAuthorize("@ss.hasPermission('saas:purchase-return:save')")
    public CommonResult<Long> save(@Valid @RequestBody ReturnBillSaveReqVO saveReqVO) {
        // 对象转换
        ReturnBillDTO returnBill = ReturnBillConvert.INSTANCE.convert2PurchaseReturnDTO(saveReqVO);
        return success(purchaseReturnService.savePurchaseReturnBill(returnBill));
    }

    @PostMapping("/revoke")
    @Operation(summary = "撤销采购退货")
    @PreAuthorize("@ss.hasPermission('saas:purchase-return:revoke')")
    public CommonResult<Boolean> revoke(@Valid @RequestBody ReturnBillSaveReqVO saveReqVO) {
        // 对象转换
        ReturnBillDTO returnBill = ReturnBillConvert.INSTANCE.convert2RevokeDTO(saveReqVO);
        purchaseReturnService.revokePurchaseReturn(returnBill);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除采购退货单")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:purchase-return:delete')")
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        purchaseReturnService.deletePurchaseReturnBill(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得采购退货单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:purchase-return:query')")
    public CommonResult<ReturnBillRespVO> get(@RequestParam("id") Long id) {
        ReturnBillDTO returnBill = purchaseReturnService.getPurchaseReturnBill(id);
        return success(ReturnBillConvert.INSTANCE.convert2VO(returnBill));
    }

    @GetMapping("/get-with-details")
    @Operation(summary = "获得采购退货单详情")
    @Parameter(name = "billNo", description = "单据编号", required = true)
    @Parameter(name = "tenantId", description = "租户编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:purchase-return:query')")
    public CommonResult<ReturnBillRespVO> getWithDetails(@RequestParam("billNo") String billNo,
            @RequestParam("tenantId") Long tenantId) {
        ReturnBillDTO returnBill = purchaseReturnService.getPurchaseReturnBillWithDetails(billNo, tenantId);
        return success(ReturnBillConvert.INSTANCE.convert2VO(returnBill));
    }

    @GetMapping("/page")
    @Operation(summary = "获得采购退货单分页")
    @PreAuthorize("@ss.hasPermission('saas:purchase-return:query')")
    public CommonResult<PageResult<ReturnBillRespVO>> getPage(@Valid ReturnBillPageReqVO pageReqVO) {
        ReturnBillConvert.INSTANCE.processPageQueryParams(pageReqVO);
        ReturnBillPageReqDTO pageReqDTO = ReturnBillConvert.INSTANCE.convert2DTO(pageReqVO);
        PageResult<ReturnBillDTO> pageResult = purchaseReturnService.getPurchaseReturnBillPage(pageReqDTO);
        return success(ReturnBillConvert.INSTANCE.convert2VO(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出采购退货单 Excel")
    @PreAuthorize("@ss.hasPermission('saas:purchase-return:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportExcel(@Valid ReturnBillPageReqVO pageReqVO,
            HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        ReturnBillConvert.INSTANCE.processPageQueryParams(pageReqVO);
        ReturnBillPageReqDTO pageReqDTO = ReturnBillConvert.INSTANCE.convert2DTO(pageReqVO);
        List<ReturnBillDTO> list = purchaseReturnService.getPurchaseReturnBillPage(pageReqDTO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "采购退货单.xls", "数据", ReturnBillRespVO.class,
                ReturnBillConvert.INSTANCE.convert2VOList(list));
    }
}
package com.xyy.saas.localserver.purchase.server.admin.supplier;

import com.xyy.saas.localserver.purchase.api.supplier.dto.TenantSupplierSalesDTO;
import com.xyy.saas.localserver.purchase.api.supplier.dto.TenantSupplierSalesPageReqDTO;
import com.xyy.saas.localserver.purchase.server.admin.supplier.vo.TenantSupplierSalesPageReqVO;
import com.xyy.saas.localserver.purchase.server.admin.supplier.vo.TenantSupplierSalesRespVO;
import com.xyy.saas.localserver.purchase.server.admin.supplier.vo.TenantSupplierSalesSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.supplier.TenantSupplierSalesConvert;
import com.xyy.saas.localserver.purchase.server.service.supplier.TenantSupplierSalesService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

/**
 * 管理后台 - 租户-供应商-销售人员信息 Controller
 *
 * <p>
 * 该Controller提供了租户与供应商销售人员信息相关的REST API，包括：
 * </p>
 * <ul>
 * <li>销售人员信息的增删改查等基础操作</li>
 * <li>销售人员信息的分页查询</li>
 * <li>销售人员信息的Excel导出</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "管理后台 - 租户-供应商-销售人员信息")
@RestController
@RequestMapping("/saas/purchase/tenant-supplier-sales")
@Validated
public class TenantSupplierSalesController {

    @Resource
    private TenantSupplierSalesService tenantSupplierSalesService;

    // ========== 基础操作方法 ==========

    /**
     * 创建租户-供应商销售人员信息
     *
     * <p>
     * 该接口用于创建新的租户与供应商销售人员之间的关联信息，主要处理以下步骤：
     * </p>
     * <ol>
     * <li>将请求对象转换为DTO对象</li>
     * <li>调用Service层创建销售人员信息</li>
     * </ol>
     *
     * @param createReqVO 创建销售人员信息的请求对象，包含销售人员的基本信息
     * @return 销售人员信息编号
     */
    @PostMapping("/create")
    @Operation(summary = "创建租户-供应商-销售人员信息")
    @PreAuthorize("@ss.hasPermission('saas:purchase:tenant-supplier-sales:create')")
    public CommonResult<Long> createTenantSupplierSales(@Valid @RequestBody TenantSupplierSalesSaveReqVO createReqVO) {
        // 1. 对象转换
        TenantSupplierSalesDTO createDTO = TenantSupplierSalesConvert.INSTANCE.convert2DTO(createReqVO);
        // 2. 创建销售人员信息
        return success(tenantSupplierSalesService.createTenantSupplierSales(createDTO));
    }

    /**
     * 更新租户-供应商销售人员信息
     *
     * <p>
     * 该接口用于更新已存在的租户与供应商销售人员之间的关联信息，主要处理以下步骤：
     * </p>
     * <ol>
     * <li>将请求对象转换为DTO对象</li>
     * <li>调用Service层更新销售人员信息</li>
     * </ol>
     *
     * @param updateReqVO 更新销售人员信息的请求对象，包含需要更新的销售人员信息
     * @return 是否更新成功
     */
    @PutMapping("/update")
    @Operation(summary = "更新租户-供应商-销售人员信息")
    @PreAuthorize("@ss.hasPermission('saas:purchase:tenant-supplier-sales:update')")
    public CommonResult<Boolean> updateTenantSupplierSales(
            @Valid @RequestBody TenantSupplierSalesSaveReqVO updateReqVO) {
        // 1. 对象转换
        TenantSupplierSalesDTO updateDTO = TenantSupplierSalesConvert.INSTANCE.convert2DTO(updateReqVO);
        // 2. 更新销售人员信息
        tenantSupplierSalesService.updateTenantSupplierSales(updateDTO);
        return success(true);
    }

    /**
     * 删除租户-供应商销售人员信息
     *
     * <p>
     * 该接口用于删除指定的租户与供应商销售人员之间的关联信息，主要处理以下步骤：
     * </p>
     * <ol>
     * <li>调用Service层删除销售人员信息</li>
     * </ol>
     *
     * @param id 销售人员信息编号
     * @return 是否删除成功
     */
    @DeleteMapping("/delete")
    @Operation(summary = "删除租户-供应商-销售人员信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:purchase:tenant-supplier-sales:delete')")
    public CommonResult<Boolean> deleteTenantSupplierSales(@RequestParam("id") Long id) {
        // 1. 删除销售人员信息
        tenantSupplierSalesService.deleteTenantSupplierSales(id);
        return success(true);
    }

    /**
     * 获取租户-供应商销售人员信息
     *
     * <p>
     * 该接口用于获取指定的租户与供应商销售人员之间的关联信息详情，主要处理以下步骤：
     * </p>
     * <ol>
     * <li>调用Service层查询销售人员信息</li>
     * <li>将DTO对象转换为VO对象返回</li>
     * </ol>
     *
     * @param id 销售人员信息编号
     * @return 销售人员详细信息
     */
    @GetMapping("/get")
    @Operation(summary = "获得租户-供应商-销售人员信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:purchase:tenant-supplier-sales:query')")
    public CommonResult<TenantSupplierSalesRespVO> getTenantSupplierSales(@RequestParam("id") Long id) {
        // 1. 查询销售人员信息
        TenantSupplierSalesDTO tenantSupplierSalesDTO = tenantSupplierSalesService.getTenantSupplierSales(id);
        // 2. 转换返回
        return success(TenantSupplierSalesConvert.INSTANCE.convert2VO(tenantSupplierSalesDTO));
    }

    // ========== 查询方法 ==========

    /**
     * 获取租户-供应商销售人员信息分页
     *
     * <p>
     * 该接口用于分页查询租户与供应商销售人员之间的关联信息，主要处理以下步骤：
     * </p>
     * <ol>
     * <li>将请求对象转换为DTO对象</li>
     * <li>调用Service层进行分页查询</li>
     * <li>将查询结果转换为VO对象返回</li>
     * </ol>
     *
     * @param pageReqVO 分页查询条件
     * @return 分页查询结果
     */
    @GetMapping("/page")
    @Operation(summary = "获得租户-供应商-销售人员信息分页")
    @PreAuthorize("@ss.hasPermission('saas:purchase:tenant-supplier-sales:query')")
    public CommonResult<PageResult<TenantSupplierSalesRespVO>> getTenantSupplierSalesPage(
            @Valid TenantSupplierSalesPageReqVO pageReqVO) {
        // 1. 对象转换
        TenantSupplierSalesPageReqDTO pageReqDTO = TenantSupplierSalesConvert.INSTANCE.convert2DTO(pageReqVO);
        // 2. 查询分页
        PageResult<TenantSupplierSalesDTO> pageResult = tenantSupplierSalesService
                .getTenantSupplierSalesPage(pageReqDTO);
        // 3. 转换返回
        return success(TenantSupplierSalesConvert.INSTANCE.convert2VO(pageResult));
    }

    // ========== 导出方法 ==========

    /**
     * 导出租户-供应商销售人员信息Excel
     *
     * <p>
     * 该接口用于导出租户与供应商销售人员之间的关联信息到Excel文件，主要处理以下步骤：
     * </p>
     * <ol>
     * <li>设置不分页查询</li>
     * <li>查询所有符合条件的销售人员信息</li>
     * <li>将数据导出为Excel文件</li>
     * </ol>
     *
     * @param pageReqVO 查询条件
     * @param response  HTTP响应对象
     * @throws IOException 导出过程中的IO异常
     */
    @GetMapping("/export-excel")
    @Operation(summary = "导出租户-供应商-销售人员信息 Excel")
    @PreAuthorize("@ss.hasPermission('saas:purchase:tenant-supplier-sales:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTenantSupplierSalesExcel(@Valid TenantSupplierSalesPageReqVO pageReqVO,
            HttpServletResponse response) throws IOException {
        // 1. 设置不分页
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        // 2. 查询数据
        TenantSupplierSalesPageReqDTO pageReqDTO = TenantSupplierSalesConvert.INSTANCE.convert2DTO(pageReqVO);
        List<TenantSupplierSalesDTO> list = tenantSupplierSalesService.getTenantSupplierSalesPage(pageReqDTO).getList();
        // 3. 导出 Excel
        ExcelUtils.write(response, "租户-供应商-销售人员信息.xls", "数据", TenantSupplierSalesRespVO.class,
                TenantSupplierSalesConvert.INSTANCE.convert2VOList(list));
    }

}
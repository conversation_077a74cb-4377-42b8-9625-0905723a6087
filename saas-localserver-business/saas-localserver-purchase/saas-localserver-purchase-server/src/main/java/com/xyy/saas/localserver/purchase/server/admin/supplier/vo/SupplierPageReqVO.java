package com.xyy.saas.localserver.purchase.server.admin.supplier.vo;

import lombok.*;

import java.time.LocalDate;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 供应商信息分页 Request VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 供应商信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SupplierPageReqVO extends PageParam {

    /** 供应商编码 */
    @Schema(description = "供应商编码(租户本地新建供应商生成规则：GYS+租户id(来源)+机器码+日期+4位流水)")
    private String guid;

    /** 商城供应商编码 */
    @Schema(description = "商城供应商编码")
    private String sourceSupplierGuid;

    /** 供应商名称 */
    @Schema(description = "供应商名称", example = "赵六")
    private String name;

    /** 供应商类别 */
    @Schema(description = "供应商类别（字典配置-20048）", example = "1")
    private Integer type;

    /** 助记码 */
    @Schema(description = "助记码（混合查询条件）")
    private String mnemonicCode;

    /** 系统默认 */
    @Schema(description = "系统默认")
    private Boolean systemDefault;

    /** 来源 */
    @Schema(description = "来源（非系统默认供应商，数据来源默认为租户id）")
    private Long source;

    /** 法定代表人 */
    @Schema(description = "法定代表人")
    private String legalRepresentative;

    /** 注册地址 */
    @Schema(description = "注册地址")
    private String registeredAddress;

    /** 经营范围 */
    @Schema(description = "经营范围（字典配置-10005）")
    private String businessScope;

    /** 营业执照编码 */
    @Schema(description = "营业执照编码")
    private String businessLicense;

    /** 发证机关 */
    @Schema(description = "发证机关")
    private String licenceAuthority;

    /** 注册日期 */
    @Schema(description = "注册日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] registeredDate;

    /** 有效期至 */
    @Schema(description = "有效期至")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] expirationDate;

    /** 有效期至方式 */
    @Schema(description = "有效期至方式:1--长期，2--手填，当为2的时候，expiration_date必须有值", example = "1")
    private Integer expirationDateType;

    /** 是否三证合一 */
    @Schema(description = "是否三证合一：0--否，1--是")
    private Boolean triCertMerged;

    /** 开户银行 */
    @Schema(description = "开户银行")
    private String depositBank;

    /** 银行账号 */
    @Schema(description = "银行账号", example = "30038")
    private String bankAccount;

    /** 开户户名 */
    @Schema(description = "开户户名", example = "王五")
    private String accountName;

    /** 组织机构代码 */
    @Schema(description = "组织机构代码")
    private String organizationCertificationCode;

    /** 组织机构发证日期 */
    @Schema(description = "组织机构发证日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] organizationCertificationDate;

    /** 组织机构有效期至 */
    @Schema(description = "组织机构有效期至")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] organizationCertificationExpirationDate;

    /** 组织机构税务登记号 */
    @Schema(description = "组织机构税务登记号")
    private String organizationCertificationTaxNo;

    /** 组织机构代码证发证机关 */
    @Schema(description = "组织机构代码证发证机关")
    private String organizationCertificationAuthority;

    /** 注册地址code */
    @Schema(description = "注册地址code")
    private String registeredAddressCod;

    /** 仓库地址code */
    @Schema(description = "仓库地址code")
    private String storeAddressCode;

    /** 仓库明细地址 */
    @Schema(description = "仓库明细地址")
    private String storeAddress;

    /** 印章印模附件 */
    @Schema(description = "印章印模附件")
    private String signet;

    /** 随货同行单样式附件 */
    @Schema(description = "随货同行单样式附件")
    private String shipmentTemplate;

    /** 资质与经营范围json数据 */
    @Schema(description = "资质与经营范围json数据")
    private String qualificationInfos;

    /** 委托人身份证号 */
    @Schema(description = "委托人身份证号")
    private String proxyIdCard;

    /** 委托人身份证有效期 */
    @Schema(description = "委托人身份证有效期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] proxyIdCardExpirationDate;

    /** 码上放心-企业唯一标识 */
    @Schema(description = "码上放心-企业唯一标识", example = "3033")
    private String msfxRefEntId;

    /** 码上放心-企业ID */
    @Schema(description = "码上放心-企业ID", example = "21044")
    private String msfxEntId;

    /** 关联分发业务 */
    @Schema(description = "关联分发业务")
    private Boolean relateDistribute;

    /** 备注 */
    @Schema(description = "备注（存json数据）", example = "你说的对")
    private String remark;

    /** 是否禁用 */
    @Schema(description = "是否禁用")
    private Boolean disabled;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
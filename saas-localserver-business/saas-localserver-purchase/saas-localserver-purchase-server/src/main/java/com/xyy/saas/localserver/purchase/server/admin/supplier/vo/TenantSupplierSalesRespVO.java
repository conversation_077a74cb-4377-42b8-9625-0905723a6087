package com.xyy.saas.localserver.purchase.server.admin.supplier.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

/**
 * 管理后台 - 租户-供应商-销售人员信息 Response VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 租户-供应商-销售人员信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TenantSupplierSalesRespVO {

    /** 主键ID */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19789")
    @ExcelProperty("主键ID")
    private Long id;

    /** 供应商编号 */
    @Schema(description = "供应商编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("供应商编号")
    private String supplierGuid;

    /** 销售人员姓名 */
    @Schema(description = "销售人员姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("销售人员姓名")
    private String salesName;

    /** 授权区域 */
    @Schema(description = "授权区域", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("授权区域")
    private String authorizedArea;

    /** 授权书号 */
    @Schema(description = "授权书号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("授权书号")
    private String authorizationNum;

    /** 授权书号有效期 */
    @Schema(description = "授权书号有效期")
    @ExcelProperty("授权书号有效期")
    private LocalDateTime authorizationNumExpirationDate;

    /** 手机号码 */
    @Schema(description = "手机号码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("手机号码")
    private String phoneNumber;

    /** 授权信息 */
    @Schema(description = "授权信息", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("授权信息")
    private String authorizedVarieties;

    /** 身份证号 */
    @Schema(description = "身份证号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("身份证号")
    private String idCard;

    /** 身份证有效期 */
    @Schema(description = "身份证有效期")
    @ExcelProperty("身份证有效期")
    private LocalDateTime idCardExpirationDate;

    /** 身份证附件 */
    @Schema(description = "身份证附件", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("身份证附件")
    private String idCardAttachment;

    /** 授权书附件 */
    @Schema(description = "授权书附件", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("授权书附件")
    private String authorizationAttachment;

    /** 经营范围 */
    @Schema(description = "经营范围", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("经营范围")
    private String authorizedScope;

    /** 创建时间 */
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
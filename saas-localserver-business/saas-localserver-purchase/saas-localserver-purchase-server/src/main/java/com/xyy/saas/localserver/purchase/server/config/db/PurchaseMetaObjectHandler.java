package com.xyy.saas.localserver.purchase.server.config.db;

import cn.iocoder.yudao.framework.mybatis.core.handler.DefaultDBFieldHandler;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.extend.PurchaseErpBillDO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.purchase.PurchaseBillDO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.receive.ReceiveBillDO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.returned.ReturnBillDO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.stockout.StockoutBillDO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.supplier.SupplierDO;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

@Primary
@Component
public class PurchaseMetaObjectHandler extends DefaultDBFieldHandler {

    private static final String COMPOSITE_BILL_NO = "compositeBillNo";

    private static final String MNEMONIC_CODE = "mnemonicCode";

    @Override
    public void insertFill(MetaObject metaObject) {
        // 先处理基础字段
        super.insertFill(metaObject);
        // 处理助记码
        handleCompositeBillNo(metaObject);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        // 先处理基础字段
        super.updateFill(metaObject);
        // 处理助记码
        handleCompositeBillNo(metaObject);
    }

    private void handleCompositeBillNo(MetaObject metaObject) {
        Object originalObject = metaObject.getOriginalObject();
        if (originalObject instanceof SupplierDO supplier) {
            metaObject.setValue(MNEMONIC_CODE, supplier.generateMnemonicCode());
        } else if (originalObject instanceof StockoutBillDO stockoutBill) {
            metaObject.setValue(COMPOSITE_BILL_NO, stockoutBill.generateCompositeBillNo());
        } else if (originalObject instanceof PurchaseErpBillDO purchaseErpBill) {
            metaObject.setValue(COMPOSITE_BILL_NO, purchaseErpBill.generateCompositeBillNo());
        } else if(originalObject instanceof ReceiveBillDO receiveBill){
            metaObject.setValue(COMPOSITE_BILL_NO, receiveBill.generateCompositeBillNo());
        }else if (originalObject instanceof PurchaseBillDO purchaseBill) {
            metaObject.setValue(COMPOSITE_BILL_NO, purchaseBill.generateCompositeBillNo());
        }else if (originalObject instanceof ReturnBillDO returnBillDO) {
            metaObject.setValue(COMPOSITE_BILL_NO, returnBillDO.generateCompositeBillNo());
        }
    }
}
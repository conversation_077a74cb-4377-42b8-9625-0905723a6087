package com.xyy.saas.localserver.purchase.server.convert.receive;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import com.xyy.saas.localserver.entity.enums.tenant.TenantTypeEnum;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDetailDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillPageReqDTO;
import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillDTO;
import com.xyy.saas.localserver.purchase.server.admin.receive.vo.ReceiveBillPageReqVO;
import com.xyy.saas.localserver.purchase.server.admin.receive.vo.ReceiveBillRespVO;
import com.xyy.saas.localserver.purchase.server.admin.receive.vo.ReceiveBillSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.extend.PurchaseInvoiceConvert;
import com.xyy.saas.localserver.purchase.server.convert.extend.PurchaseTransportConvert;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.receive.ReceiveBillDO;
import com.xyy.saas.localserver.purchase.server.enums.BillNoTypeEnum;
import com.xyy.saas.localserver.purchase.server.utils.BillContentUtil;
import com.xyy.saas.localserver.purchase.enums.purchase.PurchaseBillStatusEnum;
import com.xyy.saas.localserver.purchase.enums.purchase.PurchaseBillTypeEnum;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import static com.xyy.saas.localserver.entity.enums.tenant.TenantTypeEnum.CHAIN_STORE;
import static com.xyy.saas.localserver.purchase.enums.receive.ReceiveBillStatusEnum.*;
import static com.xyy.saas.localserver.purchase.enums.receive.ReceiveBillTypeEnum.*;

/**
 * 收货单转换器
 * <p>
 * 主要功能：
 * 1. 基础转换 - VO/DO/DTO之间的转换
 * 2. 单据类型转换 - 处理不同类型的收货单据
 * 3. 内容填充 - 处理单据内容的生成和填充
 * 4. 单据转换 - 处理不同类型单据之间的转换
 * 5. 辅助方法 - 提供各种辅助功能
 */
@Mapper(uses = { PurchaseTransportConvert.class, PurchaseInvoiceConvert.class, ReceiveBillDetailConvert.class })
public interface ReceiveBillConvert {

    ReceiveBillConvert INSTANCE = Mappers.getMapper(ReceiveBillConvert.class);

    // ========== 1. 基础转换方法 ==========

    /**
     * VO 转 DO
     *
     * @param receiveBillVO 收货单保存请求VO
     * @return 收货单DO
     */
    ReceiveBillDO convert2DO(ReceiveBillSaveReqVO receiveBillVO);

    /**
     * DTO 转 DO
     *
     * @param receiveBillDTO 收货单DTO
     * @return 收货单DO
     */
    @Named("convert2DO")
    ReceiveBillDO convert2DO(ReceiveBillDTO receiveBillDTO);

    /**
     * DTO列表 转 DO列表
     *
     * @param receiveBillDTOs 收货单DTO列表
     * @return 收货单DO列表
     */
    @IterableMapping(qualifiedByName = "convert2DO")
    List<ReceiveBillDO> convert2DOList(List<ReceiveBillDTO> receiveBillDTOs);

    /**
     * VO 转 DTO
     *
     * @param receiveBillVO 收货单保存请求VO
     * @return 收货单DTO
     */
    @Mapping(target = "details", source = "details")
    ReceiveBillDTO convert2DTO(ReceiveBillSaveReqVO receiveBillVO);

    /**
     * DO 转 DTO
     *
     * @param receiveBill 收货单DO
     * @return 收货单DTO
     */
    ReceiveBillDTO convert2DTO(ReceiveBillDO receiveBill);

    /**
     * DO 转 DTO
     *
     * @param pageResult 收货单DO分页结果
     * @return 收货单DTO分页结果
     */
    PageResult<ReceiveBillDTO> convert2DTO(PageResult<ReceiveBillDO> pageResult);

    /**
     * VO 转 DTO
     *
     * @param pageReqVO 收货单分页查询VO
     * @return 收货单分页查询DTO
     */
    ReceiveBillPageReqDTO convert2DTO(ReceiveBillPageReqVO pageReqVO);

    /**
     * DO 转 VO
     *
     * @param receiveBillDTO 收货单DTO
     * @return 收货单保存请求VO
     */
    @Mapping(target = "details", source = "details")
    ReceiveBillSaveReqVO convert2SaveVO(ReceiveBillDTO receiveBillDTO);

    /**
     * DTO 转 VO
     *
     * @param receiveBillDTO 收货单DTO
     * @return 收货单响应VO
     */
    @Mapping(target = "details", source = "details")
    ReceiveBillRespVO convert2RespVO(ReceiveBillDTO receiveBillDTO);

    /**
     * DTO 转 VO
     *
     * @param pageResult 收货单DTO分页结果
     * @return 收货单响应VO分页结果
     */
    PageResult<ReceiveBillRespVO> convert2VO(PageResult<ReceiveBillDTO> pageResult);

    /**
     * DTO 转 VO
     *
     * @param list 收货单DTO列表
     * @return 收货单响应VO列表
     */
    List<ReceiveBillRespVO> convert2RespVOList(List<ReceiveBillDTO> list);

    /**
     * 处理查询参数
     * 处理显示状态及其他查询参数，直接修改传入的对象
     *
     * @param pageReqVO 查询请求VO
     */
    default void processQueryParams(ReceiveBillPageReqVO pageReqVO) {
        // 如果设置了显示状态，则应用显示状态的查询条件
        if (pageReqVO.getDisplayStatus() != null) {
            ReceiveBillPageReqVO.ReceiveBillDisplayStatus displayStatus = ReceiveBillPageReqVO.ReceiveBillDisplayStatus
                    .fromCode(pageReqVO.getDisplayStatus());
            // 使用枚举中的方法应用状态
            displayStatus.apply(pageReqVO);
        }

        // 这里可以添加处理其他查询参数的逻辑

        // 返回处理后的对象，方便链式调用
    }

    // ========== 2. 单据类型转换方法 ==========

    /**
     * VO 转 DTO(要货单发货)
     *
     * @param receiveBillVO 收货单保存请求VO
     * @return 收货单DTO
     */
    default ReceiveBillDTO convert2RequisitionDispatchDTO(ReceiveBillSaveReqVO receiveBillVO) {
        ReceiveBillDTO receiveBill = convert2DTO(receiveBillVO);
        // 设置单据类型和状态
        receiveBill.setBillType(REQUISITION_RECEIVE.getCode());
        receiveBill.setStatus(PENDING_RECEIVE.getCode());
        // 填充配送员和时间
        fillDeliverAndTime(receiveBill);
        // 填充复核员和时间
        fillCheckerAndTime(receiveBill);
        return receiveBill;
    }

    /**
     * VO 转 DTO(铺货单发货)
     *
     * @param receiveBillVO 收货单保存请求VO
     * @return 收货单DTO
     */
    default ReceiveBillDTO convert2DistributionDispatchDTO(ReceiveBillSaveReqVO receiveBillVO) {
        ReceiveBillDTO receiveBill = convert2DTO(receiveBillVO);
        // 设置单据类型和状态
        receiveBill.setBillType(DISTRIBUTION_RECEIVE.getCode());
        receiveBill.setStatus(PENDING_RECEIVE.getCode());
        // 填充配送员和时间
        fillDeliverAndTime(receiveBill);
        // 填充复核员和时间
        fillCheckerAndTime(receiveBill);
        return receiveBill;
    }

    /**
     * VO 转 DTO(收货)
     *
     * @param receiveBillVO 收货单保存请求VO
     * @return 收货单DTO
     */
    default ReceiveBillDTO convert2ReceiveDTO(ReceiveBillSaveReqVO receiveBillVO) {
        ReceiveBillDTO receiveBill = convert2DTO(receiveBillVO);
        // 设置单据类型和单据状态
        Optional.ofNullable(receiveBill.getBillType()).ifPresentOrElse(receiveBillVO::setBillType,
                () -> receiveBill.setBillType(PURCHASE_ORDER_RECEIVE.getCode()));
        receiveBill.setStatus(PENDING_CHECK.getCode());
        // 填充收货员和时间
        fillReceiverAndTime(receiveBill);
        // 设置租户id
        receiveBill.setTenantId(TenantContextHolder.getTenantId());
        // todo 设置租户类型
        receiveBill.setTenantType(TenantTypeEnum.CHAIN_HEADQUARTERS.getCode());
        return receiveBill;
    }

    /**
     * VO 转 DTO(采购收货)
     *
     * @param receiveBillVO 收货单保存请求VO
     * @return 收货单DTO
     */
    default ReceiveBillDTO convert2AcceptDTO(ReceiveBillSaveReqVO receiveBillVO) {
        ReceiveBillDTO receiveBill = convert2DTO(receiveBillVO);
        // 设置单据状态
        receiveBill.setStatus(PENDING_STORAGE.getCode());
        // 填充验收员和时间
        fillAccepterAndTime(receiveBill);
        return receiveBill;
    }

    /**
     * VO 转 DTO(采购收货)
     *
     * @param receiveBillVO 收货单保存请求VO
     * @return 收货单DTO
     */
    default ReceiveBillDTO convert2WarehouseDTO(ReceiveBillSaveReqVO receiveBillVO) {
        ReceiveBillDTO receiveBill = convert2DTO(receiveBillVO);
        // 设置单据状态
        receiveBill.setStatus(STORED.getCode());
        // 填充入库员和时间
        fillWarehouserAndTime(receiveBill);
        return receiveBill;
    }

    /**
     * VO 转 DTO(一步入库)
     *
     * @param receiveBillVO 收货单保存请求VO
     * @return 收货单DTO
     */
    default ReceiveBillDTO convert2OneStepWarehousingDTO(ReceiveBillSaveReqVO receiveBillVO) {
        ReceiveBillDTO receiveBill = convert2DTO(receiveBillVO);
        receiveBill.setBillType(PURCHASE_ORDER_RECEIVE.getCode());
        receiveBill.setStatus(STORED.getCode());
        // 填充采购员和时间
        fillPurchaserAndTime(receiveBill);
        // 填充收货员和时间
        fillReceiverAndTime(receiveBill);
        // 填充验收员和时间
        fillAccepterAndTime(receiveBill);
        // 填充入库员和时间
        fillWarehouserAndTime(receiveBill);
        return receiveBill;
    }

    /**
     * VO 转 DTO(拒收入库)
     *
     * @param receiveBillVO 收货单保存请求VO
     * @return 收货单DTO
     */
    default ReceiveBillDTO convert2RejectWarehousingDTO(ReceiveBillSaveReqVO receiveBillVO) {
        ReceiveBillDTO receiveBill = convert2DTO(receiveBillVO);
        receiveBill.setBillType(REJECT_RECEIVE.getCode());
        receiveBill.setStatus(STORED.getCode());
        // 填充收货员和时间
        fillReceiverAndTime(receiveBill);
        // 填充验收员和时间
        fillAccepterAndTime(receiveBill);
        // 填充入库员和时间
        fillWarehouserAndTime(receiveBill);
        return receiveBill;
    }

    /**
     * VO 转 DTO(门店调剂入库)
     *
     * @param receiveBillVO 收货单保存请求VO
     * @return 收货单DTO
     */
    default ReceiveBillDTO convert2StoreAllocationHeadWarehousingDTO(ReceiveBillSaveReqVO receiveBillVO) {
        ReceiveBillDTO receiveBill = convert2DTO(receiveBillVO);
        receiveBill.setBillType(ALLOCATION_RECEIVE.getCode());
        receiveBill.setStatus(STORED.getCode());
        // 填充收货员和时间
        fillReceiverAndTime(receiveBill);
        // 填充验收员和时间
        fillAccepterAndTime(receiveBill);
        // 填充入库员和时间
        fillWarehouserAndTime(receiveBill);
        return receiveBill;
    }

    // ========== 3. 内容填充方法 ==========

    /**
     * 填充收货内容
     * 包括：单号、金额计算、收货内容生成
     *
     * @param receiveBill 收货单
     */
    default void fillReceiveContent(ReceiveBillDTO receiveBill) {
        // 填充收货单号
        fillBillNo(receiveBill);
        // 计算金额
        calculateAmount(receiveBill);
        // 填充采购内容
        generateReceiveContent(receiveBill);
    }

    /**
     * 填充回滚信息（状态回退 + 字段重置）
     *
     * @param receiveBill 收货单
     * @return 收货单DO
     */
    default ReceiveBillDO convert2RollbackDO(ReceiveBillDTO receiveBill) {
        Optional.ofNullable(receiveBill.getStatus())
                .map(status -> {
                    // 确定回退后的状态
                    Integer targetStatus = determineRollbackStatus(status);
                    receiveBill.setStatus(targetStatus);
                    // 根据目标状态清理详情字段
                    ReceiveBillDetailConvert.INSTANCE.resetDetailFields(receiveBill.getDetails(), targetStatus);
                    return targetStatus;
                });
        return convert2DO(receiveBill);
    }

    /**
     * 创建门店拒收单
     */
    default ReceiveBillSaveReqVO generateStoreRejectWarehouseBill(ReceiveBillDTO sourceBill,
            List<ReceiveBillDetailDTO> details) {
        // 转换拒收信息
        ReceiveBillSaveReqVO rejectBill = convert2SaveVO(sourceBill);
        // 清空id
        rejectBill.setId(null);
        // 清空单号以便生成新单号
        rejectBill.setBillNo(null);
        // 设置源单号
        rejectBill.setSourceBillNo(sourceBill.getBillNo());
        // 设置备注为门店拒收
        rejectBill.setRemark("门店拒收");
        // 设置租户ID为门店租户ID
        rejectBill.setTenantId(sourceBill.getHeadTenantId());
        // 设置出库租户id为当前收货的租户id
        rejectBill.setOutboundTenantId(sourceBill.getTenantId());
        // 设置租户类型为总部
        rejectBill.setTenantType(TenantTypeEnum.CHAIN_HEADQUARTERS.getCode());
        // 设置详情
        rejectBill.setDetails(ReceiveBillDetailConvert.INSTANCE.generateRejectDetails(details));
        return rejectBill;
    }

    /**
     * 创建总部拒收单
     */
    default ReceiveBillSaveReqVO generateHeadRejectWarehouseBill(ReceiveBillDTO sourceBill, ReturnBillDTO returnBill,
            List<ReceiveBillDetailDTO> details) {
        // 转换拒收信息
        ReceiveBillSaveReqVO rejectBill = convert2SaveVO(sourceBill);
        // 清空id
        rejectBill.setId(null);
        // 清空单号以便生成新单号
        rejectBill.setBillNo(null);
        // 设置源单号
        rejectBill.setSourceBillNo(sourceBill.getBillNo());
        // 设置备注为总部拒收
        rejectBill.setRemark("总部拒收");
        // 设置租户ID为门店租户ID
        rejectBill.setTenantId(returnBill.getTenantId());
        // 设置出库租户id为总部租户id
        rejectBill.setOutboundTenantId(sourceBill.getTenantId());
        // 设置总部租户ID
        rejectBill.setHeadTenantId(sourceBill.getTenantId());
        // 设置租户类型为门店
        rejectBill.setTenantType(CHAIN_STORE.getCode());
        // 设置详情
        rejectBill.setDetails(ReceiveBillDetailConvert.INSTANCE.generateRejectDetails(details));
        return rejectBill;
    }

    // ========== 4. 单据转换方法 ==========

    /**
     * 深拷贝收货单
     *
     * @param sourceBill 源收货单
     * @return 新的收货单
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "billNo", ignore = true)
    @Mapping(target = "details", source = "details")
    ReceiveBillDTO deepCopyBill(ReceiveBillDTO sourceBill);

    /**
     * 转换拆分后的单据
     *
     * @param sourceBill     源收货单
     * @param receiveDetails 收货单详情列表
     * @return 新的收货单
     */
    default ReceiveBillDTO convert2SplitDO(ReceiveBillDTO sourceBill, List<ReceiveBillDetailDTO> receiveDetails) {
        ReceiveBillDTO newBill = deepCopyBill(sourceBill);
        newBill.setBillNo(BillNoTypeEnum.RECEIVE.getBillNo(LocalDateTime.now()));
        newBill.setDetails(receiveDetails);
        fillReceiveContent(newBill);
        return newBill;
    }

    /**
     * 收货单转采购订单
     *
     * @param receiveBill 收货单
     * @return 采购订单
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "purchaseBillNo", ignore = true)
    @Mapping(target = "details", source = "details")
    PurchaseBillDTO convert2PurchaseOrder(ReceiveBillDTO receiveBill);

    /**
     * 收货单转采购订单
     *
     * @param receiveBill 收货单
     * @return 采购订单
     */
    default PurchaseBillDTO generatePurchaseOrder(ReceiveBillDTO receiveBill) {
        // 使用 MapStruct 生成的转换方法进行基础转换
        PurchaseBillDTO purchaseBill = convert2PurchaseOrder(receiveBill);
        // 设置单据类型和状态
        purchaseBill.setBillType(PurchaseBillTypeEnum.PURCHASE_ORDER.getCode());
        purchaseBill.setStatus(PurchaseBillStatusEnum.COMPLETED.getCode());
        // 设置单号
        purchaseBill.setPurchaseBillNo(BillNoTypeEnum.PURCHASE_ORDER.getBillNo(receiveBill.getPurchaseTime()));
        receiveBill.setSourceBillNo(purchaseBill.getPurchaseBillNo());
        return purchaseBill;
    }

    /**
     * 收货单转总部铺货单（调剂）
     *
     * @param headReceiveBill 总部收货单
     * @return 总部铺货单
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "purchaseBillNo", ignore = true)
    @Mapping(target = "details", source = "details")
    PurchaseBillDTO convert2HeadDistribution(ReceiveBillDTO headReceiveBill);

    /**
     * 收货单转总部铺货单（调剂）
     *
     * @param headReceiveBill 总部收货单
     * @return 总部铺货单
     */
    default PurchaseBillDTO generateHeadDistributionByAllocation(ReceiveBillDTO headReceiveBill) {
        // 使用 MapStruct 生成的转换方法进行基础转换
        PurchaseBillDTO distributionBill = convert2HeadDistribution(headReceiveBill);
        // 设置单据类型
        distributionBill.setBillType(PurchaseBillTypeEnum.HEADQUARTERS_DISTRIBUTION.getCode());
        // 设置单据状态
        distributionBill.setStatus(PurchaseBillStatusEnum.DELIVERED.getCode());
        // 设置单据已提交
        distributionBill.setSubmitted(true);
        // 设置单号
        distributionBill.setPlanBillNo(headReceiveBill.getAllocationBill().getPurchaseBillNo());
        // 设置源单号
        distributionBill.setPurchaseBillNo(
                BillNoTypeEnum.HEADQUARTERS_DISTRIBUTION.getBillNo(headReceiveBill.getPurchaseTime()));
        // 设置入库门店信息
        distributionBill.setInboundTenantId(headReceiveBill.getAllocationBill().getInboundTenantId());
        return distributionBill;
    }

    /**
     * 总部收货单转门店收货单（调剂）
     *
     * @param headReceiveBill 总部收货单
     * @return 门店收货单
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "billNo", ignore = true)
    @Mapping(target = "details", source = "details")
    ReceiveBillDTO convert2StoreReceiveBill(ReceiveBillDTO headReceiveBill);

    /**
     * 总部收货单转门店收货单（调剂）
     *
     * @param headReceiveBill 总部收货单
     * @return 门店收货单
     */
    default ReceiveBillDTO generateStoreReceiveBillByAllocation(ReceiveBillDTO headReceiveBill) {
        // 使用 MapStruct 生成的转换方法进行基础转换
        ReceiveBillDTO storeReceiveBill = convert2StoreReceiveBill(headReceiveBill);
        // 设置单据类型
        storeReceiveBill.setBillType(ALLOCATION_RECEIVE.getCode());
        // 设置单据状态
        storeReceiveBill.setStatus(PENDING_RECEIVE.getCode());
        // 设置租户ID和类型
        storeReceiveBill.setTenantId(headReceiveBill.getAllocationBill().getInboundTenantId());
        storeReceiveBill.setTenantType(CHAIN_STORE.getCode());
        storeReceiveBill.setHeadTenantId(headReceiveBill.getAllocationBill().getHeadTenantId());
        // 设置租户ID
        if (storeReceiveBill.getDetails() != null) {
            storeReceiveBill.getDetails().forEach(detail -> detail.setTenantId(storeReceiveBill.getTenantId()));
        }
        return storeReceiveBill;
    }

    // ========== 5. 辅助方法 ==========

    /**
     * 填充配送员和时间
     *
     * @param receiveBill 收货单
     */
    private void fillDeliverAndTime(ReceiveBillDTO receiveBill) {
        Optional.ofNullable(receiveBill.getDeliverer()).ifPresentOrElse(receiveBill::setDeliverer,
                () -> receiveBill.setDeliverer(WebFrameworkUtils.getLoginUserId().toString()));
        Optional.ofNullable(receiveBill.getDeliveryTime()).ifPresentOrElse(receiveBill::setDeliveryTime,
                () -> receiveBill.setDeliveryTime(LocalDateTime.now()));
    }

    /**
     * 填充收货员和时间
     *
     * @param receiveBill 收货单
     */
    private void fillReceiverAndTime(ReceiveBillDTO receiveBill) {
        // TODO 获取默认收货员
        Optional.ofNullable(receiveBill.getReceiver()).ifPresentOrElse(receiveBill::setReceiver,
                () -> receiveBill.setReceiver(WebFrameworkUtils.getLoginUserId().toString()));
        Optional.ofNullable(receiveBill.getReceiveTime()).ifPresentOrElse(receiveBill::setReceiveTime,
                () -> receiveBill.setReceiveTime(LocalDateTime.now()));
    }

    /**
     * 填充验收员和时间
     *
     * @param receiveBill 收货单
     */
    private void fillAccepterAndTime(ReceiveBillDTO receiveBill) {
        Optional.ofNullable(receiveBill.getAccepter()).ifPresentOrElse(receiveBill::setAccepter,
                () -> receiveBill.setAccepter(WebFrameworkUtils.getLoginUserId().toString()));
        Optional.ofNullable(receiveBill.getAcceptTime()).ifPresentOrElse(receiveBill::setAcceptTime,
                () -> receiveBill.setAcceptTime(LocalDateTime.now()));
    }

    /**
     * 填充入库员和时间
     *
     * @param receiveBill 收货单
     */
    private void fillWarehouserAndTime(ReceiveBillDTO receiveBill) {
        Optional.ofNullable(receiveBill.getWarehouser()).ifPresentOrElse(receiveBill::setWarehouser,
                () -> receiveBill.setWarehouser(WebFrameworkUtils.getLoginUserId().toString()));
        Optional.ofNullable(receiveBill.getWarehouseTime()).ifPresentOrElse(receiveBill::setWarehouseTime,
                () -> receiveBill.setWarehouseTime(LocalDateTime.now()));
    }

    /**
     * 填充采购员和时间
     *
     * @param receiveBill 收货单
     */
    private void fillPurchaserAndTime(ReceiveBillDTO receiveBill) {
        Optional.ofNullable(receiveBill.getPurchaser()).ifPresentOrElse(receiveBill::setPurchaser,
                () -> receiveBill.setPurchaser(WebFrameworkUtils.getLoginUserId().toString()));
        Optional.ofNullable(receiveBill.getPurchaseTime()).ifPresentOrElse(receiveBill::setPurchaseTime,
                () -> receiveBill.setPurchaseTime(LocalDateTime.now().minusDays(2)));
    }

    /**
     * 填充单据编号
     *
     * @param receiveBill 收货单
     */
    private void fillBillNo(ReceiveBillDTO receiveBill) {
        // 填充出库单号
        if (List.of(REQUISITION_RECEIVE.getCode(), DISTRIBUTION_RECEIVE.getCode(), ALLOCATION_RECEIVE.getCode())
                .contains(receiveBill.getBillType()) && CHAIN_STORE.getCode() == receiveBill.getTenantType()) {
            receiveBill.setDeliveryBillNo(BillNoTypeEnum.OUTBOUND_ORDER.getBillNo(receiveBill.getDeliveryTime()));
        }

        // 填充收货单号
        fillReceiveBillNo(receiveBill);
    }

    /**
     * 填充单据编号
     *
     * @param receiveBill 收货单
     */
    private void fillReceiveBillNo(ReceiveBillDTO receiveBill) {
        // 填充收货单号
        Optional.ofNullable(receiveBill.getBillNo()).ifPresentOrElse(receiveBill::setBillNo,
                () -> receiveBill.setBillNo(BillNoTypeEnum.RECEIVE.getBillNo(receiveBill.getReceiveTime())));

        // 详情填充单号
        receiveBill.getDetails().forEach(detail -> detail.setBillNo(receiveBill.getBillNo()));

        // 运输信息填充单号
        Optional.ofNullable(receiveBill.getTransport())
                .ifPresent(transport -> Optional.ofNullable(receiveBill.getBillNo()).ifPresent(transport::setBillNo));

        // 发票填充单号
        Optional.ofNullable(receiveBill.getInvoice())
                .ifPresent(transport -> Optional.ofNullable(receiveBill.getBillNo()).ifPresent(transport::setBillNo));
    }

    /**
     * 计算金额
     *
     * @param receiveBill 收货单
     */
    private void calculateAmount(ReceiveBillDTO receiveBill) {
        List<ReceiveBillDetailDTO> details = Optional.ofNullable(receiveBill.getDetails())
                .orElseGet(List::of);

        // 计算商品种类
        receiveBill.setProductKind(details.size());

        // 计算收货数量和金额
        BigDecimal totalQuantity = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;

        for (ReceiveBillDetailDTO detail : details) {
            totalQuantity = totalQuantity.add(detail.getReceiveQuantity());
            totalAmount = totalAmount.add(detail.getReceiveAmount());
        }

        receiveBill.setReceiveQuantity(totalQuantity);
        receiveBill.setReceiveAmount(totalAmount);
    }

    /**
     * 智能生成收货描述
     *
     * @param receiveBill 收货单
     */
    private void generateReceiveContent(ReceiveBillDTO receiveBill) {
        List<ReceiveBillDetailDTO> details = Optional.ofNullable(receiveBill.getDetails())
                .orElseGet(List::of);

        // 直接使用详情中的商品信息生成内容
        String content = BillContentUtil.generateContent(details, detail -> detail.getExt().getProductInfo());

        receiveBill.setReceiveContent(content);
    }

    /**
     * 根据当前状态决定回退后的目标状态
     *
     * @param currentStatus 当前状态
     * @return 目标状态
     */
    private Integer determineRollbackStatus(Integer currentStatus) {
        return Optional.ofNullable(currentStatus)
                .filter(PENDING_CHECK.getCode()::equals)
                .map(ignored -> PENDING_RECEIVE.getCode())
                .orElse(PENDING_CHECK.getCode());
    }

    /**
     * 填充操作员
     *
     * @param receiveBill 收货单
     */
    private void fillCheckerAndTime(ReceiveBillDTO receiveBill) {
        Optional.ofNullable(receiveBill.getChecker()).ifPresentOrElse(receiveBill::setChecker,
                () -> receiveBill.setChecker(WebFrameworkUtils.getLoginUserId().toString()));
        Optional.ofNullable(receiveBill.getCheckTime()).ifPresentOrElse(receiveBill::setCheckTime,
                () -> receiveBill.setCheckTime(LocalDateTime.now()));
    }
}

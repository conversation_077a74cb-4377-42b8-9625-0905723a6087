package com.xyy.saas.localserver.purchase.server.convert.returned;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import com.xyy.saas.localserver.entity.enums.tenant.TenantTypeEnum;
import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillDTO;
import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillDetailDTO;
import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillPageReqDTO;
import com.xyy.saas.localserver.purchase.enums.receive.ReceiveBillStatusEnum;
import com.xyy.saas.localserver.purchase.enums.receive.ReceiveBillTypeEnum;
import com.xyy.saas.localserver.purchase.server.admin.receive.vo.ReceiveBillSaveReqVO;
import com.xyy.saas.localserver.purchase.server.admin.returned.vo.ReturnBillPageReqVO;
import com.xyy.saas.localserver.purchase.server.admin.returned.vo.ReturnBillRespVO;
import com.xyy.saas.localserver.purchase.server.admin.returned.vo.ReturnBillSaveReqVO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.returned.ReturnBillDO;
import com.xyy.saas.localserver.purchase.server.enums.BillNoTypeEnum;
import com.xyy.saas.localserver.purchase.server.utils.BillContentUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import static com.xyy.saas.localserver.purchase.enums.returned.ReturnBillTypeEnum.*;
import static com.xyy.saas.localserver.purchase.enums.returned.ReturnBillStatusEnum.*;

/**
 * 退货单转换器
 * <p>
 * 主要功能：
 * 1. 基础转换 - VO/DO/DTO之间的转换
 * 2. 单据类型转换 - 处理不同类型的退货单据
 * 3. 内容填充 - 处理单据内容的生成和填充
 * 4. 辅助方法 - 提供各种辅助功能
 */
@Mapper(uses = { ReturnBillDetailConvert.class })
public interface ReturnBillConvert {

    ReturnBillConvert INSTANCE = Mappers.getMapper(ReturnBillConvert.class);

    // ========== 1. 基础转换方法 ==========

    /**
     * DTO 转 DO
     *
     * @param returnBill 退货单DTO
     * @return 退货单DO
     */
    ReturnBillDO convert2DO(ReturnBillDTO returnBill);

    /**
     * DTO列表 转 DO列表
     *
     * @param returnBills 退货单DTO列表
     * @return 退货单DO列表
     */
    List<ReturnBillDO> convert2DOList(List<ReturnBillDTO> returnBills);

    /**
     * VO 转 DTO
     *
     * @param returnBillVO 退货单保存请求VO
     * @return 退货单DTO
     */
    @Mapping(target = "details", source = "details")
    ReturnBillDTO convert2DTO(ReturnBillSaveReqVO returnBillVO);

    /**
     * DO 转 DTO
     *
     * @param returnBill 退货单DO
     * @return 退货单DTO
     */
    ReturnBillDTO convert2DTO(ReturnBillDO returnBill);

    /**
     * VO 转 DTO
     *
     * @param pageReqDTO 退货单分页查询VO
     * @return 退货单分页查询DTO
     */
    ReturnBillPageReqDTO convert2DTO(ReturnBillPageReqVO pageReqDTO);

    /**
     * DO 转 DTO
     *
     * @param pageResult 退货单DO分页结果
     * @return 退货单DTO分页结果
     */
    PageResult<ReturnBillDTO> convert2DTO(PageResult<ReturnBillDO> pageResult);

    /**
     * DO 转 VO
     *
     * @param returnBillDTO 退货单DTO
     * @return 退货单响应VO
     */
    @Mapping(target = "details", source = "details")
    ReturnBillRespVO convert2VO(ReturnBillDTO returnBillDTO);

    /**
     * DTO 转 VO
     *
     * @param pageResult 退货单DTO分页结果
     * @return 退货单响应VO分页结果
     */
    PageResult<ReturnBillRespVO> convert2VO(PageResult<ReturnBillDTO> pageResult);

    /**
     * DTO列表 转 VO列表
     *
     * @param list 退货单DTO列表
     * @return 退货单响应VO列表
     */
    List<ReturnBillRespVO> convert2VOList(List<ReturnBillDTO> list);

    /**
     * VO 转 DTO(撤销)
     *
     * @param returnBillVO 退货单保存请求VO
     * @return 退货单DTO
     */
    default ReturnBillDTO convert2RevokeDTO(ReturnBillSaveReqVO returnBillVO) {
        ReturnBillDTO returnBill = convert2DTO(returnBillVO);
        returnBill.setStatus(REVOKED.getCode());
        return returnBill;
    }

    // ========== 2. 单据类型转换方法 ==========

    /**
     * 将退货单转换为采购退货单
     * 
     * @param returnBillVO 退货单请求VO
     * @return 采购退货单DTO
     */
    default ReturnBillDTO convert2PurchaseReturnDTO(ReturnBillSaveReqVO returnBillVO) {
        ReturnBillDTO returnBill = convert2DTO(returnBillVO);
        // 设置单据类型和状态
        returnBill.setBillType(PURCHASE_RETURN.getCode());
        returnBill.setStatus(PENDING_APPROVAL.getCode());
        // 填充操作员和时间
        fillOperatorAndTime(returnBill);
        return returnBill;
    }

    /**
     * 将退货单转换为门店退货单
     * 
     * @param returnBillVO 退货单请求VO
     * @return 门店退货单DTO
     */
    default ReturnBillDTO convert2StoreReturnDTO(ReturnBillSaveReqVO returnBillVO) {
        ReturnBillDTO returnBill = convert2DTO(returnBillVO);
        // 设置单据类型和状态
        returnBill.setBillType(STORE_RETURN.getCode());
        returnBill.setStatus(PENDING_APPROVAL.getCode());
        // 填充操作员和时间
        fillOperatorAndTime(returnBill);
        return returnBill;
    }

    /**
     * 生成门店调剂单
     * 
     * @param returnBillVO 退货单请求VO
     * @return 门店调剂单DTO
     */
    default ReturnBillDTO generateStoreAllocation(ReturnBillSaveReqVO returnBillVO) {
        ReturnBillDTO returnBill = convert2DTO(returnBillVO);
        // 设置单据类型和状态
        returnBill.setBillType(STORE_ALLOCATION.getCode());
        returnBill.setStatus(PENDING_APPROVAL.getCode());
        // 填充操作员和时间
        fillOperatorAndTime(returnBill);
        return returnBill;
    }

    /**
     * 将退货单转换为总部收货单
     * 
     * @param returnBill 退货单DTO
     * @return 总部收货单VO
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "billNo", ignore = true)
    @Mapping(target = "details", source = "details")
    ReceiveBillSaveReqVO convert2HeadReceiveBill(ReturnBillDTO returnBill);

    /**
     * 生成总部收货单
     * 
     * @param returnBill 退货单DTO
     * @return 总部收货单VO
     */
    default ReceiveBillSaveReqVO generateHeadReceiveBill(ReturnBillDTO returnBill) {
        // 使用 MapStruct 生成的转换方法进行基础转换
        ReceiveBillSaveReqVO headReceiveBill = convert2HeadReceiveBill(returnBill);

        // 设置租户ID和出库门店租户id
        headReceiveBill.setTenantId(returnBill.getHeadTenantId());
        headReceiveBill.setTenantType(TenantTypeEnum.CHAIN_HEADQUARTERS.getCode());
        headReceiveBill.setOutboundTenantId(returnBill.getTenantId());

        // 设置源单号和单据类型
        headReceiveBill.setSourceBillNo(returnBill.getBillNo());
        headReceiveBill.setBillType(ReceiveBillTypeEnum.ALLOCATION_RECEIVE.getCode());
        headReceiveBill.setStatus(ReceiveBillStatusEnum.PENDING_RECEIVE.getCode());
        return headReceiveBill;
    }

    // ========== 3. 内容填充方法 ==========

    /**
     * 填充退货单内容
     * 包括：单号、金额计算、退货内容生成
     * 
     * @param returnBill 退货单DTO
     */
    default void fillReturnContent(ReturnBillDTO returnBill) {
        // 填充单号
        fillBillNo(returnBill);
        // 计算金额
        calculateAmount(returnBill);
        // 填充退货内容
        generateReturnContent(returnBill);
    }

    // ========== 4. 辅助方法 ==========

    /**
     * 处理查询参数
     * 处理显示状态及其他查询参数，直接修改传入的对象
     *
     * @param pageReqVO 查询请求VO
     */
    default void processPageQueryParams(ReturnBillPageReqVO pageReqVO) {
        // 如果设置了显示状态，则应用显示状态的查询条件
        if (pageReqVO.getDisplayStatus() != null) {
            ReturnBillPageReqVO.ReturnBillDisplayStatus displayStatus = ReturnBillPageReqVO.ReturnBillDisplayStatus
                    .fromCode(pageReqVO.getDisplayStatus());
            // 使用枚举中的方法应用状态
            displayStatus.apply(pageReqVO);
        }
    }

    /**
     * 填充操作员和时间
     * 
     * @param returnBill 退货单DTO
     */
    private void fillOperatorAndTime(ReturnBillDTO returnBill) {
        Optional.ofNullable(returnBill.getOperator()).ifPresentOrElse(returnBill::setOperator,
                () -> returnBill.setOperator(WebFrameworkUtils.getLoginUserId().toString()));
        Optional.ofNullable(returnBill.getOperateTime()).ifPresentOrElse(returnBill::setOperateTime,
                () -> returnBill.setOperateTime(LocalDateTime.now()));
    }

    /**
     * 填充单号
     * 
     * @param returnBill 退货单DTO
     */
    private void fillBillNo(ReturnBillDTO returnBill) {
        Optional.ofNullable(returnBill.getBillNo()).ifPresentOrElse(returnBill::setBillNo,
                () -> returnBill.setBillNo(BillNoTypeEnum.RETURN.getBillNo(returnBill.getOperateTime())));
        Optional.ofNullable(returnBill.getDetails())
                .ifPresent(details -> details
                        .forEach(detail -> Optional.ofNullable(returnBill.getBillNo()).ifPresent(detail::setBillNo)));
    }

    /**
     * 计算金额
     * 
     * @param returnBill 退货单DTO
     */
    private void calculateAmount(ReturnBillDTO returnBill) {
        List<ReturnBillDetailDTO> details = Optional.ofNullable(returnBill.getDetails())
                .orElseGet(List::of);

        // 计算商品种类
        returnBill.setProductKind(details.size());

        // 计算退货数量和金额
        BigDecimal totalQuantity = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (ReturnBillDetailDTO detail : details) {
            totalQuantity = totalQuantity.add(detail.getOutboundQuantity());
            totalAmount = totalAmount.add(detail.getOutboundAmount());
        }

        returnBill.setReturnQuantity(totalQuantity);
        returnBill.setReturnAmount(totalAmount);
    }

    /**
     * 生成退货描述
     * 
     * @param returnBill 退货单DTO
     */
    private void generateReturnContent(ReturnBillDTO returnBill) {
        List<ReturnBillDetailDTO> details = Optional.ofNullable(returnBill.getDetails())
                .orElseGet(List::of);

        // 直接使用详情中的商品信息生成内容
        String content = BillContentUtil.generateContent(details, detail -> detail.getExt().getProductInfo());
        returnBill.setReturnContent(content);
    }
}
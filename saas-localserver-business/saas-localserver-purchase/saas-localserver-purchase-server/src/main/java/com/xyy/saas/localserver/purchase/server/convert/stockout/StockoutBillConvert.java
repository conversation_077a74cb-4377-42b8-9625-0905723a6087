package com.xyy.saas.localserver.purchase.server.convert.stockout;

import com.xyy.saas.localserver.purchase.api.stockout.dto.StockoutBillDTO;
import com.xyy.saas.localserver.purchase.api.stockout.dto.StockoutBillDetailDTO;
import com.xyy.saas.localserver.purchase.api.stockout.dto.StockoutBillPageReqDTO;
import com.xyy.saas.localserver.purchase.server.admin.stockout.vo.StockoutBillPageReqVO;
import com.xyy.saas.localserver.purchase.server.admin.stockout.vo.StockoutBillRespVO;
import com.xyy.saas.localserver.purchase.server.admin.stockout.vo.StockoutBillSaveReqVO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.stockout.StockoutBillDO;
import com.xyy.saas.localserver.purchase.server.enums.BillNoTypeEnum;
import com.xyy.saas.localserver.purchase.server.utils.BillContentUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 缺货单转换器
 * <p>
 * 主要功能：
 * 1. 基础转换 - VO/DO/DTO之间的转换
 * 2. 内容填充 - 处理单据内容的生成和填充
 * 3. 辅助方法 - 提供各种辅助功能
 */
@Mapper(uses = { StockoutBillDetailConvert.class })
public interface StockoutBillConvert {

    StockoutBillConvert INSTANCE = Mappers.getMapper(StockoutBillConvert.class);

    // ========== 1. 基础转换方法 ==========

    /**
     * VO 转 DTO
     *
     * @param saveReqVO 缺货单保存请求VO
     * @return 缺货单DTO
     */
    @Mapping(target = "details", source = "details")
    StockoutBillDTO convert2DTO(StockoutBillSaveReqVO saveReqVO);

    /**
     * DO 转 DTO
     *
     * @param stockoutBill 缺货单DO
     * @return 缺货单DTO
     */
    StockoutBillDTO convert2DTO(StockoutBillDO stockoutBill);

    /**
     * DTO 转 DO
     *
     * @param stockoutBillDTO 缺货单DTO
     * @return 缺货单DO
     */
    StockoutBillDO convert2DO(StockoutBillDTO stockoutBillDTO);

    /**
     * VO 转 DTO
     *
     * @param pageReqVO 缺货单分页查询VO
     * @return 缺货单分页查询DTO
     */
    StockoutBillPageReqDTO convert2DTO(StockoutBillPageReqVO pageReqVO);

    /**
     * DO 转 DTO
     *
     * @param pageResult 缺货单DO分页结果
     * @return 缺货单DTO分页结果
     */
    PageResult<StockoutBillDTO> convert2DTO(PageResult<StockoutBillDO> pageResult);

    /**
     * DTO 转 VO
     *
     * @param pageResult 缺货单DTO分页结果
     * @return 缺货单响应VO分页结果
     */
    PageResult<StockoutBillRespVO> convert2VO(PageResult<StockoutBillDTO> pageResult);

    /**
     * DTO 转 VO
     *
     * @param stockoutBillDTO 缺货单DTO
     * @return 缺货单响应VO
     */
    @Mapping(target = "details", source = "details")
    StockoutBillRespVO convert2VO(StockoutBillDTO stockoutBillDTO);

    /**
     * DTO列表 转 VO列表
     *
     * @param list 缺货单DTO列表
     * @return 缺货单响应VO列表
     */
    @Mapping(target = "details", source = "details")
    List<StockoutBillRespVO> convert2VOList(List<StockoutBillDTO> list);

    // ========== 2. 内容填充方法 ==========

    /**
     * 填充缺货单内容
     * 包括：单号、金额计算、缺货内容生成
     * 
     * @param stockoutBillDTO 缺货单DTO
     */
    default void fillStockoutContent(StockoutBillDTO stockoutBillDTO) {
        // 填充单号
        fillBillNo(stockoutBillDTO);
        // 计算金额
        calculateAmount(stockoutBillDTO);
        // 填充采购内容
        generatePurchaseContent(stockoutBillDTO);
    }

    // ========== 3. 辅助方法 ==========

    /**
     * 填充单号
     * 
     * @param stockoutBillDTO 缺货单DTO
     */
    private void fillBillNo(StockoutBillDTO stockoutBillDTO) {
        stockoutBillDTO.setBillNo(
                Optional.ofNullable(stockoutBillDTO.getBillNo())
                        .orElseGet(() -> BillNoTypeEnum.STOCKOUT.getBillNo(stockoutBillDTO.getCreateTime())));
    }

    /**
     * 计算金额
     * 
     * @param stockoutBillDTO 缺货单DTO
     */
    default void calculateAmount(StockoutBillDTO stockoutBillDTO) {
        List<StockoutBillDetailDTO> details = Optional.ofNullable(stockoutBillDTO.getDetails())
                .orElseGet(Collections::emptyList);

        // 计算商品种类
        stockoutBillDTO.setProductKind(details.size());

        // 计算采购数量和金额
        BigDecimal totalStockoutQuantity = BigDecimal.ZERO;
        BigDecimal totalRequireQuantity = BigDecimal.ZERO;

        for (StockoutBillDetailDTO detail : details) {
            totalStockoutQuantity = totalStockoutQuantity.add(detail.getStockoutQuantity());
            totalRequireQuantity = totalRequireQuantity.add(detail.getRequireQuantity());
        }

        stockoutBillDTO.setStockoutQuantity(totalStockoutQuantity);
        stockoutBillDTO.setRequireQuantity(totalRequireQuantity);
    }

    /**
     * 生成采购描述
     * 
     * @param stockoutBillDTO 缺货单DTO
     */
    default void generatePurchaseContent(StockoutBillDTO stockoutBillDTO) {
        List<StockoutBillDetailDTO> details = Optional.ofNullable(stockoutBillDTO.getDetails())
                .orElseGet(List::of);

        String content = BillContentUtil.generateContent(details,
                detail -> detail.getExt().getProductInfo());

        stockoutBillDTO.setStockoutContent(content);
    }
}

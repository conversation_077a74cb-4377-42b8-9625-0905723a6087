package com.xyy.saas.localserver.purchase.server.convert.stockout;

import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import com.xyy.saas.localserver.purchase.api.stockout.dto.StockoutBillDetailDTO;
import com.xyy.saas.localserver.purchase.api.stockout.dto.StockoutBillDetailPageReqDTO;
import com.xyy.saas.localserver.purchase.server.admin.stockout.vo.StockoutBillDetailPageReqVO;
import com.xyy.saas.localserver.purchase.server.admin.stockout.vo.StockoutBillDetailRespVO;
import com.xyy.saas.localserver.purchase.server.admin.stockout.vo.StockoutBillDetailSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.extend.ExtConvert;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.stockout.StockoutBillDetailDO;
import com.xyy.saas.localserver.purchase.server.utils.ProductInfoUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Optional;

import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 缺货单明细转换器
 * <p>
 * 主要功能：
 * 1. 基础转换 - VO/DO/DTO之间的转换
 * 2. 商品信息填充 - 处理商品相关信息的填充
 */
@Mapper(uses = { ExtConvert.class })
public interface StockoutBillDetailConvert {

    StockoutBillDetailConvert INSTANCE = Mappers.getMapper(StockoutBillDetailConvert.class);

    // ========== 1. 基础转换方法 ==========

    /**
     * DTO 转 DO
     * 
     * @param stockoutBillDetail 缺货单明细DTO
     * @return 缺货单明细DO
     */
    @Mapping(target = "ext", source = "ext")
    StockoutBillDetailDO convert2DO(StockoutBillDetailDTO stockoutBillDetail);

    /**
     * DTO列表 转 DO列表
     *
     * @param stockoutBillDetails 缺货单明细DTO列表
     * @return 缺货单明细DO列表
     */
    @Mapping(target = "ext", source = "ext")
    List<StockoutBillDetailDO> convert2DO(List<StockoutBillDetailDTO> stockoutBillDetails);

    /**
     * DTO 转 DO
     *
     * @param pageReqDTO 缺货单明细分页查询DTO
     * @return 缺货单明细DO
     */
    StockoutBillDetailDO convert2DO(StockoutBillDetailPageReqDTO pageReqDTO);

    /**
     * VO 转 DTO
     *
     * @param stockoutBillDetail 缺货单明细保存请求VO
     * @return 缺货单明细DTO
     */
    @Mapping(target = "ext", source = "ext")
    StockoutBillDetailDTO convert2DTO(StockoutBillDetailSaveReqVO stockoutBillDetail);

    /**
     * DO 转 DTO
     * 
     * @param stockoutBillDetail 缺货单明细DO
     * @return 缺货单明细DTO
     */
    @Mapping(target = "ext", source = "ext")
    StockoutBillDetailDTO convert2DTO(StockoutBillDetailDO stockoutBillDetail);

    /**
     * VO 转 DTO
     *
     * @param pageReqVO 缺货单明细分页查询VO
     * @return 缺货单明细分页查询DTO
     */
    StockoutBillDetailPageReqDTO convert2DTO(StockoutBillDetailPageReqVO pageReqVO);

    /**
     * DO 转 DTO
     *
     * @param pageResult 缺货单明细DO分页结果
     * @return 缺货单明细DTO分页结果
     */
    PageResult<StockoutBillDetailDTO> convert2DTO(PageResult<StockoutBillDetailDO> pageResult);

    /**
     * DTO 转 VO
     * 
     * @param stockoutBillDetail 缺货单明细DTO
     * @return 缺货单明细响应VO
     */
    @Mapping(target = "ext", source = "ext")
    StockoutBillDetailRespVO convert2VO(StockoutBillDetailDTO stockoutBillDetail);

    /**
     * DTO列表 转 VO列表
     * 
     * @param stockoutBillDetails 缺货单明细DTO列表
     * @return 缺货单明细响应VO列表
     */
    List<StockoutBillDetailRespVO> convert2VOList(List<StockoutBillDetailDTO> stockoutBillDetails);

    /**
     * DTO 转 VO
     *
     * @param pageResult 缺货单明细DTO分页结果
     * @return 缺货单明细响应VO分页结果
     */
    PageResult<StockoutBillDetailRespVO> convert2VO(PageResult<StockoutBillDetailDTO> pageResult);

    // ========== 2. 商品信息填充方法 ==========

    /**
     * 填充商品信息
     * 
     * @param details 详情列表
     */
    default void fillProductInfo(List<StockoutBillDetailDO> details) {
        ProductInfoUtil.fillProductInfo(
                details,
                StockoutBillDetailDO::getProductPref,
                (detail, productInfo) -> Optional.ofNullable(detail.getExt())
                        .or(() -> Optional.of(new ExtDTO()).map(e -> {
                            detail.setExt(e);
                            return e;
                        }))
                        .ifPresent(ext -> ext.setProductInfo(productInfo)));
    }
}
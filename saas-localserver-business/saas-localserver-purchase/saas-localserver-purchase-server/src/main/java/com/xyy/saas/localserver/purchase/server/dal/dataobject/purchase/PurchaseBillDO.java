package com.xyy.saas.localserver.purchase.server.dal.dataobject.purchase;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.baomidou.mybatisplus.annotation.*;
import org.apache.commons.lang3.StringUtils;

/**
 * 采购单 DO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@TableName("saas_purchase_bill")
// @KeySequence("saas_purchase_bill_seq") // 用于
// Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseBillDO extends TenantBaseDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 计划单号 */
    private String planBillNo;

    /** 采购单号 */
    private String purchaseBillNo;

    /** 商城订单号 */
    private String mallOrderNo;

    /** 租户类型：1-单体门店、2-连锁门店、3-连锁总部 */
    private Integer tenantType;

    /** 总部租户ID */
    private Long headTenantId;

    /** 入库租户ID（主配） */
    private Long inboundTenantId;

    /** 出库租户ID（调剂） */
    private Long outboundTenantId;

    /** 综合单据号（单号混合） */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String compositeBillNo;

    /** 单据来源：1-采购订单、2-门店要货、3-门店调剂、4-总部铺货 */
    private Integer billType;

    /** 导入方式：1-采购计划（正常创建）、2-一键入库、3-接口拉取（比如药帮忙订单）、4-excel导入 */
    private Integer importMode;

    /** 是否远程收货 */
    private Boolean remoteReceived;

    /** 已提交（类同于是否暂存） */
    private Boolean submitted;

    /** 采购状态：1-采购计划、2-已下单待审批、3-已审批待发货、4-发货中（部分发货）、5-已发货（全部发货）、6-已完成（已收货）、7-采购失败、8-已撤销（审批前撤销）、9-已驳回（审批不通过） */
    private Integer status;

    /** 药品类型：1-中药、2-非中药 */
    private Integer medicineType;

    /** 采购方式：1-线下采购、2-无仓（B2B 无仓）、3-共仓（B2C 共仓） */
    private Integer purchaseMode;

    /** 供应商编码 */
    private String supplierGuid;

    /** 供应商名称 */
    private String supplierName;

    /** 采购内容 */
    private String purchaseContent;

    /** 采购总数量（原单退货扣减可退数量） */
    private BigDecimal purchaseQuantity;

    /** 采购金额（总金额） */
    private BigDecimal purchaseAmount;

    /** 已发货数量 */
    private BigDecimal deliveredQuantity;

    /** 可退总数量（原单退货扣减可退数量，下游收货单的实际入库数量） */
    private BigDecimal returnableQuantity;

    /** 商品种类 */
    private Integer productKind;

    /** 计划员（计划审核员） */
    private String planner;

    /** 计划时间（计划审核时间） */
    private LocalDateTime planTime;

    /** 采购员（采购审核员） */
    private String purchaser;

    /** 采购时间（采购审核时间） */
    private LocalDateTime purchaseTime;

    /** 复核员 */
    private String checker;

    /** 复核时间（采购审核时间） */
    private LocalDateTime checkTime;

    /** 收货人（门店收货人信息） */
    private String receiver;

    /** 收货人电话 */
    private String receiverPhone;

    /** 收货人所在区域 */
    private String receiverArea;

    /** 收货人地址 */
    private String receiverAddress;

    /** 备注 */
    private String remark;

    /** 版本(乐观锁) */
    private Integer version;

    /**
     * 生成综合单据号
     * 规则：planBillNo|purchaseBillNo|mallOrderNo
     */
    public String generateCompositeBillNo() {
        this.compositeBillNo = Stream.of(planBillNo, purchaseBillNo, mallOrderNo)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining("|", "|", "|"));
        return this.compositeBillNo;
    }
}
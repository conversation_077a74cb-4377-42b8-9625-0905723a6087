package com.xyy.saas.localserver.purchase.server.dal.dataobject.stockout;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import lombok.*;

import java.math.BigDecimal;

/**
 * 缺货单明细 DO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@TableName("saas_purchase_stockout_bill_detail")
// @KeySequence("saas_purchase_stockout_bill_detail_seq") // 用于
// Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockoutBillDetailDO extends TenantBaseDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 单号 */
    private String billNo;

    /** 商品编码 */
    private String productPref;

    /** 要货数量 */
    private BigDecimal requireQuantity;

    /** 缺货数量 */
    private BigDecimal stockoutQuantity;

    /** 医保项目编码 */
    private String medicareProjectCode;

    /** 医保项目名称 */
    private String medicareProjectName;

    /** 医保项目等级 */
    private Integer medicareProjectLevel;

    /** 医保最小包装数量 */
    private Integer medicareMinPackageNum;

    /** 扩展信息（当前商品信息） */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ExtDTO ext;

    /** 备注 */
    private String remark;
}
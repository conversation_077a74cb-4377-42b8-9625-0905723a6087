package com.xyy.saas.localserver.purchase.server.dal.mysql.receive;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillPageReqDTO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.receive.ReceiveBillDO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 收货单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ReceiveBillMapper extends BaseMapperX<ReceiveBillDO> {

    /**
     *  查询收货单分页
     * @param reqDTO 查询参数
     * @return 收货单分页
     */
    default PageResult<ReceiveBillDO> selectPage(ReceiveBillPageReqDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<ReceiveBillDO>()
                .eqIfPresent(ReceiveBillDO::getBillNo, reqDTO.getBillNo())
                .eqIfPresent(ReceiveBillDO::getSourceBillNo, reqDTO.getSourceBillNo())
                .eqIfPresent(ReceiveBillDO::getDeliveryBillNo, reqDTO.getDeliveryBillNo())
                .eqIfPresent(ReceiveBillDO::getShipmentNo, reqDTO.getShipmentNo())
                .eqIfPresent(ReceiveBillDO::getMallOrderNo, reqDTO.getMallOrderNo())
                .eqIfPresent(ReceiveBillDO::getOutboundTenantId, reqDTO.getOutboundTenantId())
                .eqIfPresent(ReceiveBillDO::getTenantType, reqDTO.getTenantType())
                .eqIfPresent(ReceiveBillDO::getHeadTenantId, reqDTO.getHeadTenantId())
                .eqIfPresent(ReceiveBillDO::getBillType, reqDTO.getBillType())
                .eqIfPresent(ReceiveBillDO::getPurchaseMode, reqDTO.getPurchaseMode())
                .eqIfPresent(ReceiveBillDO::getStatus, reqDTO.getStatus())
                .eqIfPresent(ReceiveBillDO::getSupplierGuid, reqDTO.getSupplierGuid())
                .likeIfPresent(ReceiveBillDO::getSupplierName, reqDTO.getSupplierName())
                .eqIfPresent(ReceiveBillDO::getSupplierSales, reqDTO.getSupplierSales())
                .eqIfPresent(ReceiveBillDO::getProductKind, reqDTO.getProductKind())
                .eqIfPresent(ReceiveBillDO::getReceiveQuantity, reqDTO.getReceiveQuantity())
                .eqIfPresent(ReceiveBillDO::getDiscount, reqDTO.getDiscount())
                .eqIfPresent(ReceiveBillDO::getDiscountAmount, reqDTO.getDiscountAmount())
                .eqIfPresent(ReceiveBillDO::getReceiveContent, reqDTO.getReceiveContent())
                .eqIfPresent(ReceiveBillDO::getReceiveAmount, reqDTO.getReceiveAmount())
                .eqIfPresent(ReceiveBillDO::getDeliverer, reqDTO.getDeliverer())
                .betweenIfPresent(ReceiveBillDO::getDeliveryTime, reqDTO.getDeliveryTime())
                .eqIfPresent(ReceiveBillDO::getReceiver, reqDTO.getReceiver())
                .betweenIfPresent(ReceiveBillDO::getReceiveTime, reqDTO.getReceiveTime())
                .eqIfPresent(ReceiveBillDO::getAccepter, reqDTO.getAccepter())
                .betweenIfPresent(ReceiveBillDO::getAcceptTime, reqDTO.getAcceptTime())
                .eqIfPresent(ReceiveBillDO::getWarehouser, reqDTO.getWarehouser())
                .betweenIfPresent(ReceiveBillDO::getWarehouseTime, reqDTO.getWarehouseTime())
                .eqIfPresent(ReceiveBillDO::getChecker, reqDTO.getChecker())
                .betweenIfPresent(ReceiveBillDO::getCheckTime, reqDTO.getCheckTime())
                .eqIfPresent(ReceiveBillDO::getQualityInspector, reqDTO.getQualityInspector())
                .eqIfPresent(ReceiveBillDO::getQualityInspectionReport, reqDTO.getQualityInspectionReport())
                .eqIfPresent(ReceiveBillDO::getRemark, reqDTO.getRemark())
                .eqIfPresent(ReceiveBillDO::getVersion, reqDTO.getVersion())
                .betweenIfPresent(ReceiveBillDO::getCreateTime, reqDTO.getCreateTime())
                // 优化 compositeBillNo 匹配逻辑
                .apply(StringUtils.isNotBlank(reqDTO.getCompositeBillNo()),
                        "INSTR(CONCAT('|', composite_bill_no, '|'), CONCAT('|', {0}, '|')) > 0",
                        reqDTO.getCompositeBillNo())
                .orderByDesc(ReceiveBillDO::getId));
    }

    /**
     * 根据源单号（采购/要货/铺货）和租户ID查询收货单列表
     *
     * @param sourceBillNo 源单号
     * @param tenantId     租户ID
     * @return 收货单列表
     */
    default List<ReceiveBillDO> selectReceiveBllBySourceBillAndTenantId(String sourceBillNo, Long tenantId) {
        return selectList(new LambdaQueryWrapperX<ReceiveBillDO>()
                .eqIfPresent(ReceiveBillDO::getSourceBillNo, sourceBillNo)
                .eqIfPresent(ReceiveBillDO::getTenantId, tenantId));
    }

    /**
     * 根据单号和租户ID查询收货单信息（包含订单详情）
     *
     * @param billNo   单号
     * @param tenantId 租户ID
     * @return 订单信息
     */
    ReceiveBillDTO getReceiveBillWithDetails(@Param("billNo") String billNo, @Param("tenantId") Long tenantId);

    /**
     * 根据源单号（采购/要货/铺货）和租户ID查询收货单列表（包含订单详情）
     *
     * @param sourceBillNo   源单号
     * @param tenantId       租户ID
     * @return 收货单列表
     */
    List<ReceiveBillDTO> getReceiveBillWithDetailsBySourceBill(@Param("sourceBillNo") String sourceBillNo, @Param("tenantId") Long tenantId);

}
//package com.xyy.saas.localserver.purchase.server.service.bpm.handler;
//
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import static com.xyy.saas.inquiry.product.consts.ProductConstant.*;
//
///**
// */
//@Slf4j
//@Component
//public class PurchaseReturnApprovalHandler implements BpmApprovalHandler {
//
//
//    @Override
//    public void handleApproval(BpmBusinessRelationDto businessDto) {
//
//    }
//}
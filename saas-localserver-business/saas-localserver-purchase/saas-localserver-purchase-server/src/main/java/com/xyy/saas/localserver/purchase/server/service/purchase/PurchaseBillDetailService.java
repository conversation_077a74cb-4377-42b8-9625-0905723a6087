//package com.xyy.saas.localserver.purchase.server.service.purchase;
//
//import java.util.*;
//
//import com.xyy.saas.localserver.purchase.server.admin.purchase.vo.PurchaseBillDetailPageReqVO;
//import com.xyy.saas.localserver.purchase.server.admin.purchase.vo.PurchaseBillDetailSaveReqVO;
//import com.xyy.saas.localserver.purchase.server.dal.dataobject.purchase.PurchaseBillDetailDO;
//import jakarta.validation.*;
//import cn.iocoder.yudao.framework.common.pojo.PageResult;
//import cn.iocoder.yudao.framework.common.pojo.PageParam;
//
///**
// * 采购明细 Service 接口
// *
// * <AUTHOR>
// */
//public interface PurchaseBillDetailService {
//
//    /**
//     * 创建采购明细
//     *
//     * @param createReqVO 创建信息
//     * @return 编号
//     */
//    Long createPurchaseBillDetail(@Valid PurchaseBillDetailSaveReqVO createReqVO);
//
//    /**
//     * 更新采购明细
//     *
//     * @param updateReqVO 更新信息
//     */
//    void updatePurchaseBillDetail(@Valid PurchaseBillDetailSaveReqVO updateReqVO);
//
//    /**
//     * 删除采购明细
//     *
//     * @param id 编号
//     */
//    void deletePurchaseBillDetail(Long id);
//
//    /**
//     * 获得采购明细
//     *
//     * @param id 编号
//     * @return 采购明细
//     */
//    PurchaseBillDetailDO getPurchaseBillDetail(Long id);
//
//    /**
//     * 获得采购明细分页
//     *
//     * @param pageReqVO 分页查询
//     * @return 采购明细分页
//     */
//    PageResult<PurchaseBillDetailDO> getPurchaseBillDetailPage(PurchaseBillDetailPageReqVO pageReqVO);
//
//}
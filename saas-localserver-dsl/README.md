**严肃声明：不要把代码发到外网 !!!**

**「写好代码，做好事情」**

医保dsl。

## 🐶 新手必读

* 业务相关wiki地址【新手指引】：<https://wiki.int.ybm100.com/pages/viewpage.action?pageId=457870385>

## 🐯 工程简介

**医保DSL**，支持Yaml的动态配置调用第三方引擎

> 有任何问题，或者想要的功能，请提 _Issues_ 。
>
> 😜 ~~~~~！

### 包介绍

![img.png](img.png)

* 后端采用 Spring Boot 多模块架构、jackson-yaml

### 包介绍

路径：com.xyy.saas.localserver.medicare.dsl

| 包        | 作用                                                            | 简介                                                                                                 |
|----------|---------------------------------------------------------------|----------------------------------------------------------------------------------------------------|
| config   | 配置对象<br/>DSLConfig                                            | 把yaml格式文件反序列化的Java对象，结构化的配置实体，<br/>支持协议配置ContractConfig<br/>逻辑配置LogicalConfig<br/>视图配置ViewUIConfig |
| reader   | 配置读取<br/>ConfigReader                                         | 把Config配置从yaml格式文件读取到Java对象的处理，支持本地文件路径/medicare文件读取、JDBC-SQL读取                                    |
| parse    | 配置解析<br/>ParameterValueParser(EL)<br/>ObjectValueParser(结构解析) | 把Config配置中根据配置逻辑，从context上下文解析成第三方的真实参数值，目前是基于EL表达式实现                                              |
| protocol | 协议处理<br/>ProtocolClient                                       | 支持Http、Dll、Webservice协议客户端                                                                         |
| format   | 格式处理<br/>FormatType                                           | TODO                                                                                               |
| executor | 执行流程<br/>DSLExecutor(执行器)<br/>DSLContext(上下文)                 | 调用第三方请求的入口DSLExecutor.contractInvoker，DSLContext执行上下文参数容器，逻辑配置从DSLContext中的属性中取                    |

## 😎 协议

**开发规范**

① 代码整洁、架构整洁，遵循《阿里巴巴 Java 开发手册》规范，代码注释详细，注释比例不得低于1/5。


> 友情提示：本项目，**重构优化**后端的代码，**美化**前端的界面。
>
> * 重点功能，我们使用 🚀 标记。
> * 拓展功能，我们使用 ⭐️ 标记。

🙂 所有功能，都通过 **单元测试** 保证高质量。

### 系统功能

|    | 功能              | 描述                       | 单元测试 |
|----|-----------------|--------------------------|------|
|    | 逻辑配置            | TDDO                     |      |
|    | 视图配置            | TDDO                     |      |
|    | 配置读取            | 已完成本地/medicare下的yaml文件读取 | 完成   |
| ⭐️ | SQL读取           | TDDO                     |      |
| ⭐️ | Dll协议客户端        | TDDO                     |      |
| ⭐️ | WebService协议客户端 | TDDO                     |      |
|    | 单例模式改多例         | ~                        | 完成   |
|    | 延迟解析            | ~                        | 完成   |
|    | 加密功能            | ~                        | 完成   |
|    | Body加密后放置Header | ~                        | 完成   |
|    | 前置后置表达式         | ~吉林                      |      |
|    | 协议支持第三方SDK      | ~甘肃                      |      |
|    | 单               | ~                        |      |
| 🚀 | ~               | ~                        |      |
| 🚀 | ~               | ~                        |      |

初始化、签到(2)、医保目录(5)、读卡(5)、就诊登记(4)、事前事中(2)、结算(4)、对账(5*2)、清算(3)、进销存(8)、电子处方(6)、小票打印、账单导出(2)




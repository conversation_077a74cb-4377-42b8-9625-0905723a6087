package com.xyy.saas.localserver.medicare.dsl.executor.log;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/09/07 15:58
 */
public class ContractInvokeContext {

    /* 响应结果*/
    @Getter
    @Setter
    private boolean success;

    /*请求日志*/
    @Getter
    private List<ContractInvokeLog> invokeLogs = new ArrayList<>();

    /**
     * 清除上下文信息
     */
    public void clear() {
        this.invokeLogs.clear();
    }
}

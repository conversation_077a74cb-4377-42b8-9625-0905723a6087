package com.xyy.saas.localserver.medicare.dsl.executor.value;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * @Desc 药店相关参数值
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/18 13:35
 */
@Data
@Builder
public class Drugstore extends DSLValue {

    /*机构号*/
    private String organSign;

    /*药店名称*/
    private String name;

    /*两定机构编码*/
    private String code;

    /*药店统筹区、就医地医保区划*/
    private String areaCode;

    /*秘钥*/
    private Map<String, String> certMap;
}

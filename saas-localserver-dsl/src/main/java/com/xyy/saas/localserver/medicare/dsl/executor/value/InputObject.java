package com.xyy.saas.localserver.medicare.dsl.executor.value;

import cn.hutool.core.map.MapUtil;
import com.xyy.saas.localserver.medicare.dsl.config.base.ContractConfig;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/04 10:57
 */
@Data
public class InputObject {

    private ContractConfig contractConfig;

    private ContractConfig.FunctionConfig functionConfig;

    /*已是EL经过解析的值*/
    private Map<String, Object> bodys;

    /*已是EL经过解析的值*/
    private Map<String, String> header;

    private Map<String, String> earlyELMap;

//    private String headerStr;

    private String body;

    public InputObject(ContractConfig contractConfig, ContractConfig.FunctionConfig functionConfig) {
        this.contractConfig = contractConfig;
        this.functionConfig = functionConfig;
    }

    public void addHeader(Map<String, String> headerValues) {
        if (header == null) {
            synchronized (this) {
                if (header == null) {
                    header = new HashMap<>();
                }
            }
        }
        this.header.putAll(headerValues);
    }


    public String generateBody() {
        return body;
    }


}

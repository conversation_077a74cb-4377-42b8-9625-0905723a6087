package com.xyy.saas.localserver.medicare.dsl.reader;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.xyy.saas.localserver.medicare.dsl.config.DSLKey;
import com.xyy.saas.localserver.medicare.dsl.config.base.*;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.util.Assert;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * @Desc 本地文件配置读取
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/02 14:51
 */
//@Slf4j
@Slf4j
public class LocalFileConfigReader extends ConfigReader {

    @Setter
    public static String folder = "/medicare/";

    private final ResourceLoader resourceLoader;

    public LocalFileConfigReader(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }


    /**
     * @param key 当key==null,yamlSource是文件名
     * @param yamlSource 本地从文件里读，这里是空
     * @return
     */
    public DSLConfig readerConfig(DSLKey key, String yamlSource, String commonYamlSource) {
//        Assert.notNull(key, "读取的配置不能为null");
        //只支持yml尾缀命名的文件
        //当key==null,yamlSource是文件名
        String pathUrl = key == null ? folder + "/" + yamlSource + ".yml" : folder + "/" + key.name() + ".yml";
//        Path path = Paths.get(pathUrl);
        Resource resource = resourceLoader.getResource(pathUrl);

//        File yamlFile = path.toFile();
        if (!resource.exists()) {
            log.info("在{}下,没有找到.yml的文件", pathUrl);
            return null;
        }
        try {
            File yamlFile = resource.getFile();
            JsonNode rootNode = objectMapper.readTree(yamlFile);
            JsonNode jsonNode = rootNode.get("dslType");
            if (jsonNode == null) {
                ContractConfig contractConfig = objectMapper.readValue(yamlFile, ContractConfig.class);
                return readerCommonConfig(contractConfig, commonYamlSource);
            }
            String dslType = jsonNode.asText();
            switch (dslType) {
                case "contract":
                    ContractConfig contractConfig = objectMapper.readValue(yamlFile, ContractConfig.class);
                    return readerCommonConfig(contractConfig, commonYamlSource);
                case "logical":
                    return objectMapper.readValue(yamlFile, LogicalConfig.class);
                case "viewui":
                    return objectMapper.readValue(yamlFile, ViewUIConfig.class);
                default:
                    log.error("DSLConfig文件路径: {}, 未知的DSLType类型: {}", pathUrl, dslType);
                    throw new IllegalArgumentException("未知的DSLType类型:" + dslType);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * @param contractConfig
     * @param commonYamlSource
     * @return
     */
    @Override
    public ContractConfig readerCommonConfig(ContractConfig contractConfig, String commonYamlSource) throws JsonProcessingException {
        if (!contractConfig.isCommon()) {
            //没有公共参数的
            return contractConfig;
        }
        //有公告参数的,反序列化公告参数,不用所有协议都配一遍公共参数
        String pathUrl = folder + "/" + contractConfig.getCommonName() + ".yml";
        Resource resource = resourceLoader.getResource(pathUrl);
        try {
            ContractConfig.CommonConfig commonConfig = objectMapper.readValue(resource.getInputStream(), ContractConfig.CommonConfig.class);
            contractConfig.setCommonConfig(commonConfig);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return contractConfig;
    }
}

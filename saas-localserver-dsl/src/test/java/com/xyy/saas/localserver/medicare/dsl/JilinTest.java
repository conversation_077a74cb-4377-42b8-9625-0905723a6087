package com.xyy.saas.localserver.medicare.dsl;

import cn.hutool.json.JSONUtil;
import com.xyy.saas.localserver.medicare.dsl.config.DSLKey;
import com.xyy.saas.localserver.medicare.dsl.config.base.ContractConfig;
import com.xyy.saas.localserver.medicare.dsl.config.base.DSLConfig;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContext;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContextHolder;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLExecutor;
import com.xyy.saas.localserver.medicare.dsl.executor.value.*;
import com.xyy.saas.localserver.medicare.dsl.reader.CompositeConfigReader;
import com.xyy.saas.localserver.medicare.dsl.reader.ConfigReader;
import com.xyy.saas.localserver.medicare.dsl.response.SignInResult;
import com.xyy.saas.localserver.medicare.dsl.response.TokenResult;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Map;

/**
 * @Desc 吉林测试用例
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/09/08 15:58
 */
@ExtendWith(SpringExtension.class)
@SpringBootApplication //一个包路径下只能注解一个
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
public class JilinTest {

    @Autowired
    private ResourceLoader resourceLoader;

    private ConfigReader configReader;


    @BeforeEach
    public void init() {
        this.configReader = new CompositeConfigReader(resourceLoader);
        ((CompositeConfigReader) configReader).setLocalFilePath("/medicare/jilin");
        DSLContext context = DSLContextHolder.getContext();
        //初始化药店信息
        Map<String, String> certMap = Map.of("clientid", "B59BA4AC70E098C6",
                "clientsecret", "clielDdSC@@166",
                "username", "XZdd230324050",
                "password", "Pwd1281501!^#D012",
                "medicareAccount", "BMgwggG4AzRLXMBAAcwoKc9IrAzSIb5dACUfJo08GCU/AkUq0L2NnAvZtMI/FU5EPwfD3IsQk1Z6DXtSQvVw5Lg=",
                "medicarePrivateKey", "AJCYCdY3RPRl1kU30DLkxQGlXdeHKDM8B/3+voYQXrMV");
        context.builder(() -> Drugstore.builder()
                .organSign("ZHL00046122").name("吉林省华阳百姓药房有限责任公司").code("P22072401207").areaCode("220724").certMap(certMap).build());
        //初始化操作人信息
        context.builder(() -> Operator.builder().id(187145).type(1).name("医保").signNo("").build());
        context.builder(() -> Device.builder().mac("1C-B7-2C-A9-AB-01").natIp("************").build());
        context.builder(() -> Insurant.builder().name("张朋").cardNo("******************").idCard("******************").personNo("42100000000000100007871904").certType("02").offsite(true).build());
        context.builder(() -> Visit.builder().areaCode("420100").acctUsedFlag("1").medicareType("41").insutype("310").build());
        String access_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjo1NjAzOCwidXNlcl9uYW1lIjoiWFpkZDIzMDMyNDA1MCIsInNjb3BlIjpbInNlcnZlciJdLCJleHAiOjE2OTY5NDEyODAsImF1dGhvcml0aWVzIjpbImNoczoxMjAxIiwiY2hzOjM1MDEiLCJjaHM6MzUwNSIsImNoczozNTA0IiwiY2hzOjM1MDMiLCJjaHM6MzUwNyIsImNoczozNTA2IiwiaGFuZGxlOm9wc3ByZWc6cXVlcnkiLCJzeXN0ZW06Y2xpZW50OmV4cG9ydCIsImNoczoyMDAxIiwiY2hzOjEzMTEiLCJjaHM6MTMxMiIsImNoczoyNDAxIiwiY2hzOjEzMTAiLCJzeXN0ZW06aXNzdWVzOmV4cG9ydCIsImNoczoxMzE1IiwiY2hzOjI0MDQiLCJjaHM6MTMxNiIsImNoczoyNDA1IiwiY2hzOjEzMTMiLCJjaHM6MjQwMiIsImNoczoxMzE0IiwiY2hzOjI0MDMiLCJjaHM6NDcwMSIsImNoczoxMzE5Iiwic3lzdGVtOmNsaWVudDpyZW1vdmUiLCJjaHM6MTMxNyIsImNoczoxMzE4IiwiaGFuZGxlOmZlZTpsaXN0IiwiYnVzaGFuZGxlOm1hdG50cnQ6ZGVsIiwic3lzdGVtOmxvZzpsaXN0IiwiY2hzOjM0MDIiLCJjaHM6MzQwMSIsImNoczo1MzA0IiwiY2hzOjUzMDMiLCJjaHM6MTEwMSIsImNoczo1MzAyIiwiaGFuZGxlOmRpYWc6bGlzdCIsImNoczozNDAzIiwiY2hzOjE5MDEiLCJjaHM6NDIwMSIsInN5c3RlbTpjbGllbnQ6ZWRpdCIsImNoczoyMzAxIiwiY2hzOjIzMDIiLCJjaHM6MjMwMyIsImNoczoyMzA0IiwiaGFuZGxlOmRydWc6bGlzdCIsImhhbmRsZTp0cnRzZXJ2YjpsaXN0IiwiY2hzOjUyMDEiLCJjaHM6NTIwNiIsImNoczozMzAyIiwiY2hzOjUyMDUiLCJjaHM6MzMwMSIsImNoczo1MjA0IiwiY2hzOjUyMDMiLCJzeXN0ZW06Y2xpZW50OnF1ZXJ5IiwiY2hzOjQxMDEiLCJidXNoYW5kbGU6bWF0bnRydDpxdWVyeSIsInN5c3RlbTppc3N1ZXM6bGlzdCIsImNoczoyMjAyIiwiY2hzOjIyMDMiLCJjaHM6MjIwMSIsImNoczoyMjA2IiwiY2hzOjIyMDciLCJjaHM6MjIwNCIsImhhbmRsZTpzcGRydWc6cXVlcnkiLCJjaHM6MjIwNSIsImNoczoyNjAxIiwiaGFuZGxlOmZ1bmQ6bGlzdCIsImNoczoyMjA4Iiwic3lzdGVtOmNsaWVudDphZGQiLCJzeXN0ZW06Y2xpZW50Omxpc3QiLCJjaHM6NTEwMiIsImhhbmRsZTpzZXRsZGlhZzpsaXN0IiwiaW50ZXJmYWNlIiwiY2hzOjEzMDEiLCJjaHM6MzIwMSIsImNoczozMjAyIiwiY2hzOjEzMDQiLCJjaHM6MTMwNSIsImhhbmRsZTpieWRpc2U6bGlzdCIsImNoczoxMzAyIiwiY2hzOjEzMDMiLCJjaHM6MTMwOCIsImNoczoxMzA5IiwiY2hzOjEzMDYiLCJjaHM6MTMwNyIsImNoczoyMTAzIiwiY2hzOjQ0MDIiLCJjaHM6MjEwMSIsImNoczo0NDAxIiwiY2hzOjIxMDIiLCJjaHM6MjUwMyIsImNoczoyNTA0IiwiY2hzOjI1MDEiLCJjaHM6MjUwMiIsImNoczoyNTA1IiwiY2hzOjI1MDYiXSwianRpIjoiYmFlZWI0NzMtZmVkMy00YTI0LWE0OTQtMTIyNGE2YmNhNDVhIiwiY2xpZW50X2lkIjoiQjU5QkE0QUM3MEUwOThDNiIsInVzZXJuYW1lIjoiWFpkZDIzMDMyNDA1MCJ9.Kbzc4Jy2aNUqasho9GQLODVzxPOA4NPWKGufiJCMvmY";
        String refresh_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjo1NjAzOCwidXNlcl9uYW1lIjoiWFpkZDIzMDMyNDA1MCIsInNjb3BlIjpbInNlcnZlciJdLCJhdGkiOiJiYWVlYjQ3My1mZWQzLTRhMjQtYTQ5NC0xMjI0YTZiY2E0NWEiLCJleHAiOjE2OTY5NDQ4ODAsImF1dGhvcml0aWVzIjpbImNoczoxMjAxIiwiY2hzOjM1MDEiLCJjaHM6MzUwNSIsImNoczozNTA0IiwiY2hzOjM1MDMiLCJjaHM6MzUwNyIsImNoczozNTA2IiwiaGFuZGxlOm9wc3ByZWc6cXVlcnkiLCJzeXN0ZW06Y2xpZW50OmV4cG9ydCIsImNoczoyMDAxIiwiY2hzOjEzMTEiLCJjaHM6MTMxMiIsImNoczoyNDAxIiwiY2hzOjEzMTAiLCJzeXN0ZW06aXNzdWVzOmV4cG9ydCIsImNoczoxMzE1IiwiY2hzOjI0MDQiLCJjaHM6MTMxNiIsImNoczoyNDA1IiwiY2hzOjEzMTMiLCJjaHM6MjQwMiIsImNoczoxMzE0IiwiY2hzOjI0MDMiLCJjaHM6NDcwMSIsImNoczoxMzE5Iiwic3lzdGVtOmNsaWVudDpyZW1vdmUiLCJjaHM6MTMxNyIsImNoczoxMzE4IiwiaGFuZGxlOmZlZTpsaXN0IiwiYnVzaGFuZGxlOm1hdG50cnQ6ZGVsIiwic3lzdGVtOmxvZzpsaXN0IiwiY2hzOjM0MDIiLCJjaHM6MzQwMSIsImNoczo1MzA0IiwiY2hzOjUzMDMiLCJjaHM6MTEwMSIsImNoczo1MzAyIiwiaGFuZGxlOmRpYWc6bGlzdCIsImNoczozNDAzIiwiY2hzOjE5MDEiLCJjaHM6NDIwMSIsInN5c3RlbTpjbGllbnQ6ZWRpdCIsImNoczoyMzAxIiwiY2hzOjIzMDIiLCJjaHM6MjMwMyIsImNoczoyMzA0IiwiaGFuZGxlOmRydWc6bGlzdCIsImhhbmRsZTp0cnRzZXJ2YjpsaXN0IiwiY2hzOjUyMDEiLCJjaHM6NTIwNiIsImNoczozMzAyIiwiY2hzOjUyMDUiLCJjaHM6MzMwMSIsImNoczo1MjA0IiwiY2hzOjUyMDMiLCJzeXN0ZW06Y2xpZW50OnF1ZXJ5IiwiY2hzOjQxMDEiLCJidXNoYW5kbGU6bWF0bnRydDpxdWVyeSIsInN5c3RlbTppc3N1ZXM6bGlzdCIsImNoczoyMjAyIiwiY2hzOjIyMDMiLCJjaHM6MjIwMSIsImNoczoyMjA2IiwiY2hzOjIyMDciLCJjaHM6MjIwNCIsImhhbmRsZTpzcGRydWc6cXVlcnkiLCJjaHM6MjIwNSIsImNoczoyNjAxIiwiaGFuZGxlOmZ1bmQ6bGlzdCIsImNoczoyMjA4Iiwic3lzdGVtOmNsaWVudDphZGQiLCJzeXN0ZW06Y2xpZW50Omxpc3QiLCJjaHM6NTEwMiIsImhhbmRsZTpzZXRsZGlhZzpsaXN0IiwiaW50ZXJmYWNlIiwiY2hzOjEzMDEiLCJjaHM6MzIwMSIsImNoczozMjAyIiwiY2hzOjEzMDQiLCJjaHM6MTMwNSIsImhhbmRsZTpieWRpc2U6bGlzdCIsImNoczoxMzAyIiwiY2hzOjEzMDMiLCJjaHM6MTMwOCIsImNoczoxMzA5IiwiY2hzOjEzMDYiLCJjaHM6MTMwNyIsImNoczoyMTAzIiwiY2hzOjQ0MDIiLCJjaHM6MjEwMSIsImNoczo0NDAxIiwiY2hzOjIxMDIiLCJjaHM6MjUwMyIsImNoczoyNTA0IiwiY2hzOjI1MDEiLCJjaHM6MjUwMiIsImNoczoyNTA1IiwiY2hzOjI1MDYiXSwianRpIjoiMDYzNGU4ZWMtNDgxOC00M2E3LTk1MmQtNzM0ODVjM2FjNzAyIiwiY2xpZW50X2lkIjoiQjU5QkE0QUM3MEUwOThDNiIsInVzZXJuYW1lIjoiWFpkZDIzMDMyNDA1MCJ9.nNp5nRUtg8AisboJNcH4c99jFUD234J7v62KPT-9cxE";
        context.getGlobalContext().put("access_token", access_token);
        context.getGlobalContext().put("refresh_token", refresh_token);
    }

    /**
     * 获取token
     */
    @Test
    @Order(-1)
    public void testToken() {
        DSLConfig signinConfig = configReader.readerConfig(null, "getToken", null);
        Assertions.assertInstanceOf(ContractConfig.class, signinConfig);
        ContractConfig contractConfig = (ContractConfig) signinConfig;
        System.out.println("获取token...");
        TokenResult tokenResult = DSLExecutor.instance.contractInvoker(contractConfig, TokenResult.class);
        System.out.println(tokenResult);
        System.out.println(JSONUtil.toJsonStr(DSLContext.getGlobalContext()));
        //刷新Token
        DSLContext context = DSLContextHolder.getContext();
        init();
        context.getGlobalContext().put("refresh_token", tokenResult.getRefreshToken());
        context.getGlobalContext().put("access_token", tokenResult.getAccessToken() + 1);
        System.out.println("刷新token...");
        tokenResult = DSLExecutor.instance.contractInvoker(contractConfig, TokenResult.class);
        System.out.println(tokenResult);
        System.out.println(JSONUtil.toJsonStr(DSLContext.getGlobalContext()));
    }

    /**
     * 签到
     */
    @Test
    @Order(-1)
    public void testSignIn() {
        DSLConfig signinConfig = configReader.readerConfig(DSLKey.signin, null, null);
        Assertions.assertInstanceOf(ContractConfig.class, signinConfig);
        ContractConfig contractConfig = (ContractConfig) signinConfig;

        //currentInput.bodys['input'].toString().getBytes(T(java.nio.charset.StandardCharsets).UTF_8),
        // drugstore.certMap['clientid'].getBytes(T(java.nio.charset.StandardCharsets).UTF_8),
        // T(java.util.Base64).getDecoder().decode(drugstore.certMap['medicarePrivateKey'])
        DSLContext context = DSLContextHolder.getContext();


        SignInResult signInResult = DSLExecutor.instance.contractInvoker(contractConfig, SignInResult.class);
        System.out.println(signInResult);
        //测试解析值
        Assertions.assertNotNull(signInResult.getSignNo());
        Assertions.assertNotNull(signInResult.getSignTime());
    }
}

package com.xyy.saas.localserver.medicare.dsl.response;

import lombok.Data;

/**
 * "access_token": "[access_token]" #token
 * "token_type": "[token_type]" #token类型
 * "refresh_token": "[refresh_token]" #Refreshtoken
 * "expires_in": "[expires_in]" #有效期
 * "scope": "[scope]" #授权范围
 * "user_id": "[user_id]" #用户id
 * "username": "[username]" #
 * "jti": "[jti]" #TOKEN_ID
 *
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/09/08 16:25
 */
@Data
public class TokenResult {

    private String accessToken;

    private String tokenType;

    private String refreshToken;

    private long expiresIn;

    private String scope;

    private String userId;

    private String username;

    private String jti;
}

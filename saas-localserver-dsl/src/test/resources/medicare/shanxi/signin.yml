dslType: contract
enable: true
name: '签到'
protocol: http
format: json
domain: *************:21001
path: /fsi/api/signInSignOutService/signIn
functions:
  - domain: *************:21001
    #    protocol: http
    path: /fsi/api/signInSignOutService/signIn
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "infno": 9001 #接口号
        "input":
          "signIn":
            "opterNo": operator.id
            "mac": device.mac
            "ip": device.natIp
    response:
      "output":
        "signinoutb":
          "sign_time": "[sign_time]" #格式：yyyy-MM-dd HH:mm:ss
          "sign_no": "[sign_no]" #长度30
outputFields:
  sign_time: sign_time
  sign_no: sign_no"

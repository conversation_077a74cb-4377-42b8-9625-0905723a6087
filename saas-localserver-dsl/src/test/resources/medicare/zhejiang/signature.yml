name: '签名'
domain: CardReaderDLL.dll
path: ZJ_Hmac_SM3
protocol: dll
common: false
functions:
  - condition: globalContext['access_token'] != null
    request:
      body:
        "scope": "'server'" #终端授权范围
        "grant_type": "'refresh_token'" #终端授权类型
        "refresh_token": globalContext['refresh_token'] #token
    response:
      "access_token": "[access_token]" #token
      "token_type": "[token_type]" #token类型
      "refresh_token": "[refresh_token]" #Refreshtoken
    result:
      success: output[code] == 0
      skip: true
      tips: output[msg]
      hook:
        - globalContext.remove('access_token')
        - globalContext.remove('refresh_token')
global:
  "token": "['access_token']"
  "refresh_token": "['refresh_token']"
result:
  success: output[code] == null
  skip: false
  tips: output[msg]





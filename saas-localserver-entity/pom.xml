<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.xyy.saas</groupId>
    <artifactId>saas-localserver</artifactId>
    <version>${revision}</version>
  </parent>
  <artifactId>saas-localserver-entity</artifactId>

  <dependencies>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
    <dependency>
      <groupId>jakarta.validation</groupId>
      <artifactId>jakarta.validation-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-common</artifactId>
    </dependency>

    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-biz-tenant</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>checker-qual</artifactId>
          <groupId>org.checkerframework</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jsr305</artifactId>
          <groupId>com.google.code.findbugs</groupId>
        </exclusion>
        <exclusion>
          <artifactId>error_prone_annotations</artifactId>
          <groupId>com.google.errorprone</groupId>
        </exclusion>
        <exclusion>
          <artifactId>yudao-module-system-api</artifactId>
          <groupId>cn.iocoder.boot</groupId>
        </exclusion>
        <!--        <exclusion>-->
<!--          <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>-->
<!--          <groupId>com.baomidou</groupId>-->
<!--        </exclusion>-->
      </exclusions>
    </dependency>

    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-module-infra-biz</artifactId>
<!--      <exclusions>-->
<!--        <exclusion>-->
<!--          <artifactId>yudao-module-system-api</artifactId>-->
<!--          <groupId>cn.iocoder.boot</groupId>-->
<!--        </exclusion>-->
<!--      </exclusions>-->
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>data-pipeline-h2</artifactId>
    </dependency>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-product-api</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>rocketmq-event-bus-spring-boot-starter</artifactId>
          <groupId>com.xyy.saas</groupId>
        </exclusion>
        <exclusion>
          <artifactId>saas-inquiry-pojo</artifactId>
          <groupId>com.xyy.saas</groupId>
        </exclusion>
      </exclusions>
    </dependency>



    <!-- 服务保障相关 -->
    <!--<dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
      &lt;!&ndash;      <optional>true</optional>&ndash;&gt;
    </dependency>-->

    <!-- dubbo -->
<!--    <dependency>-->
<!--      <artifactId>dubbo-spring-boot-starter</artifactId>-->
<!--      <groupId>org.apache.dubbo</groupId>-->
<!--      <version>3.3.0</version>-->
<!--    </dependency>-->
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>data-pipeline-starter-client</artifactId>
      <version>${project.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>mybatis-plus</artifactId>
          <groupId>com.baomidou</groupId>
        </exclusion>
      </exclusions>
    </dependency>
<!--    <dependency>-->
<!--      <groupId>com.xyy.saas</groupId>-->
<!--      <artifactId>rocketmq-event-bus-spring-boot-starter</artifactId>-->
<!--    </dependency>-->
    <dependency>
      <groupId>org.eclipse.paho</groupId>
      <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>${maven-springboot-plugin.version}</version>
      </plugin>
    </plugins>
  </build>

</project>

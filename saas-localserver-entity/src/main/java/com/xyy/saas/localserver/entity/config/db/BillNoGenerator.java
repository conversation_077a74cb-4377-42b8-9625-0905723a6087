package com.xyy.saas.localserver.entity.config.db;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.entity.config.db.DeviceIdentifierGenerator.generateDeviceId;
import static com.xyy.saas.localserver.entity.enums.ErrorCodeConstants.PURCHASE_TABLE_NAME_ANNOTATION_NOT_EXISTS;

@Slf4j
public class BillNoGenerator implements ApplicationContextAware {

    // -----------------------------------------常量定义---------------------------------------------------
    private static final long MAX_DEVICE_ID = 999L;
    private static final int MAX_INCREMENT_ID = 999_999;
    private static final String DATE_PATTERN = "yyMMdd";
    private static final String DEFAULT_BILL_NO_COLUMN = "billNo";

    // -----------------------------------------缓存定义---------------------------------------------------
    private static final ConcurrentHashMap<String, AtomicInteger> sequenceCache = new ConcurrentHashMap<>();
    private static JdbcTemplate jdbcTemplate;
    private static final int deviceId = generateDeviceId();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        jdbcTemplate = applicationContext.getBean(JdbcTemplate.class);
    }

    // -----------------------------------------公有方法---------------------------------------------------
    public static String generateBillNoByDate(String prefix, Class<?> clazz, String billNoColumn,
            LocalDateTime localDateTime) {
        // 设置默认时间
        if (localDateTime == null) {
            localDateTime = LocalDateTime.now();
        }

        if (StringUtils.isBlank(billNoColumn)) {
            billNoColumn = DEFAULT_BILL_NO_COLUMN;
        }

        validateDeviceId();

        // 生成基础信息
        String dateStr = localDateTime.format(DateTimeFormatter.ofPattern(DATE_PATTERN));
        String formattedDeviceId = String.format("%03d", generateDeviceId());
        String searchPattern = String.format("%s%s%s%%", prefix, dateStr, formattedDeviceId);

        // 获取或初始化序列号
        int sequence = getOrInitSequence(searchPattern, billNoColumn, clazz);

        // 格式化完整单号
        return String.format("%s%s%s%06d",
                prefix, dateStr, formattedDeviceId, sequence);
    }

    // -----------------------------------------私有方法--------------------------------------------------
    private static String getTableName(Class<?> clazz) {
        TableName annotation = clazz.getAnnotation(TableName.class);
        if (annotation == null) {
            throw exception(PURCHASE_TABLE_NAME_ANNOTATION_NOT_EXISTS);
        }
        return annotation.value();
    }

    private static void validateDeviceId() {
        if (deviceId < 0 || deviceId > MAX_DEVICE_ID) {
            throw new IllegalArgumentException(
                    String.format("设备ID超出范围 (0 <= deviceId <= %d)", MAX_DEVICE_ID));
        }
    }

    /**
     * 获取或初始化序列号（核心缓存逻辑）
     */
    private static int getOrInitSequence(String searchPattern, String billNoColumn, Class<?> clazz) {
        return sequenceCache.compute(searchPattern, (key, atomicSeq) -> {
            // 情况1：首次初始化缓存
            if (atomicSeq == null) {
                int dbSequence = getMaxSequenceFromDB(clazz, searchPattern, billNoColumn);
                return new AtomicInteger(dbSequence);
            }

            // 情况2：已有缓存时校验数据库最新值
            int dbSequence = getMaxSequenceFromDB(clazz, searchPattern, billNoColumn);
            if (atomicSeq.get() < dbSequence) {
                log.info("[单号生成] 同步数据库最新序列号: {} -> {}", atomicSeq.get(), dbSequence);
                atomicSeq.set(dbSequence);
            }

            // 情况3：序列号溢出检查
            if (atomicSeq.get() >= MAX_INCREMENT_ID) {
                throw new IllegalStateException("序列号已达上限 " + MAX_INCREMENT_ID);
            }

            return atomicSeq;
        }).incrementAndGet(); // 返回递增后的值
    }

    /**
     * 从数据库查询当前最大序列号
     */
    private static int getMaxSequenceFromDB(Class<?> clazz, String searchPattern, String billNoColumn) {
        String tableName = getTableName(clazz);
        String sql = "SELECT MAX(" + billNoColumn + ") FROM " + tableName + " WHERE tenantId = " +TenantContextHolder.getTenantId() +" AND " + billNoColumn + " LIKE ?";

        try {
            String maxBillNo = jdbcTemplate.queryForObject(sql, String.class, searchPattern);
            if (maxBillNo == null)
                return 0;

            // 截取序列号部分（示例：CGDDZ240603001000001 → 000001）
            String sequenceStr = maxBillNo.substring(searchPattern.length() - 1);
            return Integer.parseInt(sequenceStr);
        } catch (EmptyResultDataAccessException e) {
            return 0;
        } catch (DataAccessException | StringIndexOutOfBoundsException e) {
            log.warn("[单号生成] 数据库查询异常，返回初始序列: {}", e.getMessage());
            return 0;
        }
    }
}
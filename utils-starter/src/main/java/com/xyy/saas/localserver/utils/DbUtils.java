package com.xyy.saas.localserver.utils;


import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
public class DbUtils {

	public static final String TENANT_DB = "system_tenant";
	/**
	 * 判断是否是租户表
	 * @param tableName
	 * @return
	 */
	public static boolean isTenantDB(String tableName) {
		return TENANT_DB.equalsIgnoreCase(tableName);
	}

	/**
	 * 从map中获取主键id的值，key大小写不敏感
	 * @param map
	 * @return
	 */
	public static Object getIdValueFromMap(Map<String, Object> map) {
		if (map == null || map.isEmpty()) {
			return null;
		}
		return Optional.ofNullable(map.get("ID")).orElseGet(() -> map.getOrDefault("id", null));
	}

	/**
	 * 防止sql出现关键字冲突，列名加上``
	 * @param columnName
	 * @return
	 */
	public static String addBackquote(String columnName) {
		return "`" + columnName + "`";
	}

	/**
	 * 防止sql出现关键字冲突，列名加上``
	 * @param columnNames
	 * @return
	 */
	public static List<String> addBackquotes(List<String> columnNames) {
		return columnNames.stream().map(DbUtils::addBackquote).toList();
	}

}
